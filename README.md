# PBL5 Match Suggestion

## Python references

-   [Flask](https://flask.palletsprojects.com/en/3.0.x/)
-   [PyJWT](https://pyjwt.readthedocs.io/en/stable/)
-   [pre-commit](https://pre-commit.com/)
-   [ChromaDB](https://docs.trychroma.com/)

## ChromaDB Vector Database

The application now includes ChromaDB, a vector database for storing and querying embeddings.

### Running with Docker

The ChromaDB service is configured in `docker-compose.yml` and will start automatically when you run:

```bash
docker-compose up -d
```

### Data Persistence

ChromaDB data is persisted in the `./chroma_data` directory, which is mounted as a volume in the Docker container.

### API Endpoints

The following API endpoints are available for interacting with ChromaDB:

- `GET /api/v1/vector/collections?key=YOUR_FLASK_PASSWORD` - List all collections
- `POST /api/v1/vector/collections` - Create a new collection
  ```json
  {
    "key": "YOUR_FLASK_PASSWORD",
    "collection_name": "my_collection",
    "metadata": { "description": "My collection description" }
  }
  ```
- `DELETE /api/v1/vector/collections/{collection_name}?key=YOUR_FLASK_PASSWORD` - Delete a collection
- `POST /api/v1/vector/collections/{collection_name}/documents` - Add documents to a collection
  ```json
  {
    "key": "YOUR_FLASK_PASSWORD",
    "documents": ["document 1", "document 2"],
    "metadatas": [{"source": "source1"}, {"source": "source2"}],
    "ids": ["id1", "id2"]
  }
  ```
- `POST /api/v1/vector/collections/{collection_name}/query` - Query a collection
  ```json
  {
    "key": "YOUR_FLASK_PASSWORD",
    "query_texts": ["query text"],
    "n_results": 5,
    "where": {"metadata_field": "metadata_value"},
    "where_document": {"$contains": "search term"}
  }
  ```
