#!/usr/bin/env python
# -*- coding: utf-8 -*-
from utils.chroma_db import get_chroma_client
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def initialize_chroma():
    try:
        client = get_chroma_client()
        
        # Kiểm tra kết nối
        logger.info("Kiểm tra kết nối ChromaDB...")
        try:
            collections = client.list_collections()
            logger.info(f"Kết nối thành công. Có {len(collections)} collections.")
        except Exception as e:
            logger.error(f"Lỗi kết nối: {e}")
            return False
        
        # Tạo collections cần thiết
        collection_names = [c.name for c in collections]
        
        if "job_descriptions" not in collection_names:
            client.create_collection("job_descriptions", {"description": "Job description embeddings"})
            logger.info("Đã tạo collection job_descriptions")
        else:
            logger.info("Collection job_descriptions đã tồn tại")
        
        if "user_resumes" not in collection_names:
            client.create_collection("user_resumes", {"description": "User resume embeddings"})
            logger.info("Đã tạo collection user_resumes")
        else:
            logger.info("Collection user_resumes đã tồn tại")
        
        return True
    except Exception as e:
        logger.error(f"Lỗi không xác định: {e}")
        return False

if __name__ == "__main__":
    success = initialize_chroma()
    if success:
        logger.info("Khởi tạo ChromaDB thành công!")
    else:
        logger.error("Khởi tạo ChromaDB thất bại!") 