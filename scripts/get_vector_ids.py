#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
<PERSON>ript to fetch vector IDs from ChromaDB collections.
This is useful for finding IDs to highlight in the visualization API.
"""

import logging
import argparse
import json
import sys
import os

# Add parent directory to path to allow importing modules
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_collection_ids(collection_name, limit=5):
    """Fetch IDs from a ChromaDB collection"""
    try:
        from utils.chroma_db import get_chroma_client
        client = get_chroma_client()
        
        # Get the collection
        collection = client.client.get_collection(collection_name)
        if not collection:
            logger.error(f"Collection '{collection_name}' not found")
            return []
        
        # Get all items from the collection (up to the limit)
        result = collection.get(limit=limit, include=["metadatas"])
        
        if not result or "ids" not in result or len(result["ids"]) == 0:
            logger.warning(f"No embeddings found in collection '{collection_name}'")
            return []
        
        # Combine IDs with metadata for better display
        combined_data = []
        for i, id_value in enumerate(result["ids"]):
            metadata = result["metadatas"][i] if i < len(result["metadatas"]) else {}
            
            # Extract useful information from metadata for display
            display_name = None
            if collection_name == "job_descriptions":
                display_name = metadata.get("position", "Unknown Job")
            elif collection_name == "user_resumes":
                display_name = metadata.get("name", "Unknown User")
            
            combined_data.append({
                "id": id_value,
                "display_name": display_name,
                "metadata": metadata
            })
        
        return combined_data
    except Exception as e:
        logger.error(f"Error retrieving IDs from '{collection_name}': {e}")
        import traceback
        logger.error(traceback.format_exc())
        return []

def main():
    parser = argparse.ArgumentParser(description='Fetch vector IDs from ChromaDB collections')
    parser.add_argument('--collection', '-c', choices=['job_descriptions', 'user_resumes', 'both'], 
                        default='both', help='Collection to fetch IDs from')
    parser.add_argument('--limit', '-l', type=int, default=5, 
                        help='Maximum number of IDs to retrieve')
    args = parser.parse_args()
    
    collections_to_fetch = []
    if args.collection == 'both':
        collections_to_fetch = ['job_descriptions', 'user_resumes']
    else:
        collections_to_fetch = [args.collection]
    
    all_data = {}
    for collection_name in collections_to_fetch:
        logger.info(f"Fetching IDs from '{collection_name}'...")
        data = get_collection_ids(collection_name, args.limit)
        all_data[collection_name] = data
        
        logger.info(f"Found {len(data)} items in '{collection_name}':")
        for item in data:
            logger.info(f"  ID: {item['id']} - {item['display_name']}")
    
    # Save to JSON file
    output_file = "vector_ids.json"
    with open(output_file, 'w') as f:
        json.dump(all_data, f, indent=2)
    
    logger.info(f"IDs saved to {output_file}")
    
    # Print suggested curl command for testing
    if len(all_data.get('job_descriptions', [])) > 0 and len(all_data.get('user_resumes', [])) > 0:
        job_id = all_data['job_descriptions'][0]['id']
        resume_id = all_data['user_resumes'][0]['id']
        
        print("\nSuggested curl command for testing:")
        print(f"""
curl -X POST http://localhost:5000/api/v1/vector/visualize \\
  -H "Content-Type: application/json" \\
  -d '{{
    "key": "default", 
    "highlight_job_id": "{job_id}", 
    "highlight_resume_id": "{resume_id}", 
    "show_labels": true
  }}' | jq
        """)

if __name__ == "__main__":
    main() 