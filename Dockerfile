FROM python:3.11-slim

WORKDIR /job_swipe
COPY . /job_swipe

RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    gcc \
    g++ \
    cmake \
    python3-dev \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Set environment variables for C++11 support
ENV CFLAGS="-std=c11"
ENV CXXFLAGS="-std=c++11"

# Install any needed packages specified in requirements.txt
RUN pip install --upgrade pip
# Install hnswlib separately first
RUN pip install --no-cache-dir hnswlib==0.7.0
# Then install the rest of the requirements
RUN pip install --no-cache-dir -r requirements.txt
RUN pip install gunicorn

# Add /job_swipe to the Python path
ENV PYTHONPATH=/job_swipe

EXPOSE 5000

# Update the Gunicorn command to include the full module path
CMD [ "gunicorn", "-w", "4", "-t", "1800", "-b", "0.0.0.0:5000", "app:app" ]