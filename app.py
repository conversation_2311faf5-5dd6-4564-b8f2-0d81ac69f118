import os
import io
import uuid
import numpy as np
import logging
import time
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
from sklearn.decomposition import PCA
import boto3
from botocore.exceptions import NoCredentialsError, ClientError
from utils.environment import Env

logger = logging.getLogger(__name__)

class VectorVisualizer:
    """Class to visualize vector embeddings in 2D space"""
    
    def __init__(self):
        """Initialize the visualizer with S3 connection"""
        self.s3_bucket = Env.S3_BUCKET_NAME
        self.s3_client = boto3.client(
            's3',
            aws_access_key_id=Env.S3_ACCESS_KEY,
            aws_secret_access_key=Env.S3_SECRET_KEY,
            region_name=Env.S3_REGION
        )
        logger.info(f"Initialized VectorVisualizer with bucket: {self.s3_bucket}")
    
    def get_embeddings(self, collection_name):
        """Retrieve embeddings from a ChromaDB collection"""
        try:
            from utils.chroma_db import get_chroma_client
            client = get_chroma_client()
            
            # Log which client we're using for debugging
            logger.info(f"Using ChromaDB client of type: {type(client)} to retrieve embeddings")
            
            # Get the collection
            try:
                collection = client.client.get_collection(collection_name)
                if not collection:
                    logger.error(f"Collection '{collection_name}' not found or empty")
                    return None, None, None
                
                logger.info(f"Successfully retrieved collection '{collection_name}'")
            except Exception as collection_error:
                logger.error(f"Error getting collection '{collection_name}': {collection_error}")
                return None, None, None
            
            # Get all items from the collection
            try:
                result = collection.get(include=["embeddings", "metadatas", "documents"])
                
                if not result or "ids" not in result or len(result["ids"]) == 0:
                    logger.warning(f"No embeddings found in collection '{collection_name}'")
                    return [], [], []
                
                # Log successful retrieval
                logger.info(f"Successfully retrieved {len(result['ids'])} embeddings from collection '{collection_name}'")
                logger.info(f"First few IDs: {result['ids'][:3] if result['ids'] else []}")
                logger.info(f"First embedding dimensions: {len(result['embeddings'][0]) if result['embeddings'] and len(result['embeddings']) > 0 else 'No embeddings'}")
                
                return result["ids"], result["embeddings"], result["metadatas"]
            except Exception as e:
                logger.error(f"Error retrieving data from collection '{collection_name}': {e}")
                import traceback
                logger.error(f"Traceback: {traceback.format_exc()}")
                return None, None, None
            
        except Exception as e:
            logger.error(f"Error retrieving embeddings from '{collection_name}': {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return None, None, None
    
    def reduce_dimensions(self, embeddings):
        """Reduce embedding dimensions to 2D using PCA"""
        if not embeddings or len(embeddings) == 0:
            return np.array([])
            
        # Convert list of embeddings to numpy array
        embeddings_array = np.array(embeddings)
        
        # Check if we have enough samples for PCA
        n_samples = embeddings_array.shape[0]
        if n_samples <= 1:
            logger.warning(f"Not enough samples for PCA (found {n_samples}, need at least 2)")
            # Return dummy coordinates for a single point
            if n_samples == 1:
                return np.array([[0.0, 0.0]])
            return np.array([])
        
        # Apply PCA to reduce to 2D
        pca = PCA(n_components=2)
        reduced_embeddings = pca.fit_transform(embeddings_array)
        
        logger.info(f"Reduced {len(embeddings)} embeddings from {len(embeddings[0])} to 2 dimensions")
        return reduced_embeddings
    
    def create_visualization(self, job_embeddings, resume_embeddings, 
                            highlight_job_id=None, highlight_resume_id=None,
                            show_labels=False):
        """Create a 2D visualization of job and resume embeddings"""
        plt.figure(figsize=(12, 8))
        
        # Check if we have any vectors to visualize
        job_ids, job_vectors, job_metadatas = job_embeddings
        resume_ids, resume_vectors, resume_metadatas = resume_embeddings
        
        if (not job_vectors or len(job_vectors) == 0) and (not resume_vectors or len(resume_vectors) == 0):
            # No vectors to visualize, create an empty plot with message
            plt.text(0.5, 0.5, "No vectors available to visualize", 
                     horizontalalignment='center', verticalalignment='center',
                     transform=plt.gca().transAxes, fontsize=14)
            plt.title('Vector Visualization - No Data')
            
            # Save empty plot to buffer
            buf = io.BytesIO()
            plt.savefig(buf, format='png', dpi=300)
            buf.seek(0)
            plt.close()
            return buf
        
        # Process job embeddings if available
        job_points = None
        if job_vectors and len(job_vectors) > 0:
            job_points = self.reduce_dimensions(job_vectors)
            if len(job_points) > 0:
                plt.scatter(
                    job_points[:, 0], 
                    job_points[:, 1], 
                    c='red', 
                    alpha=0.6, 
                    label='Jobs'
                )
                
                # Add labels if requested
                if show_labels and job_metadatas:
                    for i, metadata in enumerate(job_metadatas):
                        if metadata and 'position' in metadata:
                            plt.annotate(
                                metadata['position'], 
                                (job_points[i, 0], job_points[i, 1]),
                                fontsize=8
                            )
        
        # Process resume embeddings if available
        resume_points = None
        if resume_vectors and len(resume_vectors) > 0:
            resume_points = self.reduce_dimensions(resume_vectors)
            if len(resume_points) > 0:
                plt.scatter(
                    resume_points[:, 0], 
                    resume_points[:, 1], 
                    c='blue', 
                    alpha=0.6, 
                    label='Resumes'
                )
                
                # Add labels if requested
                if show_labels and resume_metadatas:
                    for i, metadata in enumerate(resume_metadatas):
                        if metadata and 'name' in metadata:
                            plt.annotate(
                                metadata['name'], 
                                (resume_points[i, 0], resume_points[i, 1]),
                                fontsize=8
                            )
        
        # Highlight specific job and resume if IDs provided
        if highlight_job_id and job_points is not None and job_ids:
            try:
                idx = job_ids.index(highlight_job_id)
                plt.scatter(
                    job_points[idx, 0], 
                    job_points[idx, 1], 
                    c='yellow', 
                    s=100, 
                    edgecolors='black', 
                    label='Highlighted Job'
                )
                if show_labels and job_metadatas and idx < len(job_metadatas):
                    if job_metadatas[idx] and 'position' in job_metadatas[idx]:
                        plt.annotate(
                            job_metadatas[idx]['position'], 
                            (job_points[idx, 0], job_points[idx, 1]),
                            fontsize=10,
                            weight='bold'
                        )
            except ValueError:
                logger.warning(f"Highlight job ID '{highlight_job_id}' not found in collection")
        
        if highlight_resume_id and resume_points is not None and resume_ids:
            try:
                idx = resume_ids.index(highlight_resume_id)
                plt.scatter(
                    resume_points[idx, 0], 
                    resume_points[idx, 1], 
                    c='green', 
                    s=100, 
                    edgecolors='black', 
                    label='Highlighted Resume'
                )
                if show_labels and resume_metadatas and idx < len(resume_metadatas):
                    if resume_metadatas[idx] and 'name' in resume_metadatas[idx]:
                        plt.annotate(
                            resume_metadatas[idx]['name'], 
                            (resume_points[idx, 0], resume_points[idx, 1]),
                            fontsize=10,
                            weight='bold'
                        )
            except ValueError:
                logger.warning(f"Highlight resume ID '{highlight_resume_id}' not found in collection")
        
        plt.title('2D Visualization of Job and Resume Embeddings')
        plt.xlabel('Principal Component 1')
        plt.ylabel('Principal Component 2')
        plt.legend()
        plt.grid(True, linestyle='--', alpha=0.7)
        
        # Save plot to a buffer
        buf = io.BytesIO()
        plt.savefig(buf, format='png', dpi=300)
        buf.seek(0)
        plt.close()
        
        return buf
    
    def upload_to_s3(self, image_buffer):
        """Upload an image to S3 and return the URL"""
        try:
            # Generate a unique filename
            filename = f"vector-viz-{uuid.uuid4()}.png"
            object_key = f"visualizations/{filename}"
            
            # Upload the file
            self.s3_client.upload_fileobj(
                image_buffer, 
                self.s3_bucket, 
                object_key,
                ExtraArgs={'ContentType': 'image/png'}
            )
            
            # Generate URL
            url = f"https://{self.s3_bucket}.s3.{Env.S3_REGION}.amazonaws.com/{object_key}"
            logger.info(f"Uploaded visualization to S3: {url}")
            
            return url
        except NoCredentialsError:
            logger.error("AWS credentials not found")
            return None
        except ClientError as e:
            logger.error(f"AWS S3 error: {e}")
            return None
        except Exception as e:
            logger.error(f"Error uploading to S3: {e}")
            return None