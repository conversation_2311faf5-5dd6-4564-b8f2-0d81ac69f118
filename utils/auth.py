import logging
import jwt
from functools import wraps
from flask import request
from utils.environment import Env
from utils.response import AppResponse
from models.account import Account

logger = logging.getLogger(__name__)


def decode_jwt_token(jwt_token: str | None) -> str | None:
    """
    Decode JWT token và trả về account_id
    
    Args:
        jwt_token: JWT token string (có thể có prefix "Bearer ")
        
    Returns:
        str: account_id nếu token hợp lệ, None nếu không hợp lệ
    """
    if not jwt_token:
        return None

    try:
        # Remove "Bearer " prefix if present
        if jwt_token.startswith("Bearer "):
            jwt_token = jwt_token.split(" ")[1]
        
        jwt_payload = jwt.decode(
            jwt_token,
            Env.JWT_SECRET_KEY,
            algorithms=["HS256"],
            options={
                "verify_signature": True,
                "require": ["sub", "exp", "iat"],
                "verify_exp": True,
                "verify_iat": True,
            },
        )
        return jwt_payload["sub"]
    except jwt.ExpiredSignatureError:
        logger.warning("JWT token has expired")
        return None
    except jwt.InvalidTokenError as e:
        logger.error(f"Invalid JWT token: {e}")
        return None
    except Exception as e:
        logger.error(f"Error decoding JWT token: {e}")
        return None


def token_required(f):
    """
    Decorator để yêu cầu JWT token hợp lệ
    
    Usage:
        @token_required
        def protected_route(current_user):
            # current_user là Account object
            pass
    """
    @wraps(f)
    def decorated(*args, **kwargs):
        try:
            # Lấy token từ header
            auth_header = request.headers.get('Authorization')
            if not auth_header:
                logger.warning("Missing Authorization header")
                return AppResponse.bad_request(message="Authorization header is required", status_code=401)
            
            # Decode token
            account_id = decode_jwt_token(auth_header)
            if not account_id:
                logger.warning("Invalid or expired token")
                return AppResponse.bad_request(message="Invalid or expired token", status_code=401)
            
            # Lấy thông tin user từ database
            try:
                current_user = Account.query.filter_by(account_id=account_id).first()
                if not current_user:
                    logger.warning(f"Account not found for account_id: {account_id}")
                    return AppResponse.bad_request(message="Account not found", status_code=401)
                
                logger.info(f"Authenticated user: {account_id}")
                
                # Gọi function với current_user
                return f(current_user, *args, **kwargs)
                
            except Exception as e:
                logger.error(f"Error fetching user from database: {e}")
                return AppResponse.server_error(error="Database error during authentication")
                
        except Exception as e:
            logger.error(f"Error in token_required decorator: {e}")
            return AppResponse.server_error(error="Authentication error")
    
    return decorated


def get_current_user_from_token(auth_header: str = None) -> Account | None:
    """
    Lấy thông tin user hiện tại từ JWT token
    
    Args:
        auth_header: Authorization header (optional, sẽ lấy từ request nếu không cung cấp)
        
    Returns:
        Account: Account object nếu token hợp lệ, None nếu không hợp lệ
    """
    try:
        if not auth_header:
            auth_header = request.headers.get('Authorization')
        
        if not auth_header:
            return None
        
        account_id = decode_jwt_token(auth_header)
        if not account_id:
            return None
        
        current_user = Account.query.filter_by(account_id=account_id).first()
        return current_user
        
    except Exception as e:
        logger.error(f"Error getting current user from token: {e}")
        return None


def optional_token_required(f):
    """
    Decorator cho các endpoint có thể hoạt động với hoặc không có token
    
    Usage:
        @optional_token_required
        def optional_protected_route(current_user=None):
            # current_user có thể là None hoặc Account object
            pass
    """
    @wraps(f)
    def decorated(*args, **kwargs):
        try:
            # Lấy token từ header
            auth_header = request.headers.get('Authorization')
            current_user = None
            
            if auth_header:
                current_user = get_current_user_from_token(auth_header)
            
            # Gọi function với current_user (có thể là None)
            return f(current_user, *args, **kwargs)
                
        except Exception as e:
            logger.error(f"Error in optional_token_required decorator: {e}")
            return AppResponse.server_error(error="Authentication error")
    
    return decorated
