import os

from dotenv import find_dotenv, load_dotenv


class Env:
    load_dotenv(find_dotenv())

    FLASK_HOST = os.environ.get("FLASK_HOST", "localhost").lower()
    FLASK_ENV = os.environ.get("FLASK_ENV", "development").lower()

    FLASK_PASSWORD = os.environ.get("FLASK_PASSWORD", "example-password")
    MOBILE_APP_URL = os.environ.get("MOBILE_APP_URL", "localhost").lower()
    DATABASE_URI = os.environ.get("DATABASE_URI", "")
    JWT_SECRET_KEY = os.environ.get("JWT_SECRET_KEY", "example-secret-key")
    DEFAULT_PASSWORD = os.environ.get("DEFAULT_PASSWORD", "example-password")

    # ChromaDB configuration
    CHROMA_HOST = os.environ.get("CHROMA_HOST", "localhost").lower()
    CHROMA_PORT = os.environ.get("CHROMA_PORT", "8000")
    
    # OpenAI API Key
    OPENAI_API_KEY = os.environ.get("OPENAI_API_KEY", "")

    # S3 configuration
    S3_BUCKET_NAME = os.environ.get("S3_BUCKET_NAME", "")
    S3_ACCESS_KEY = os.environ.get("S3_ACCESS_KEY", "")
    S3_SECRET_KEY = os.environ.get("S3_SECRET_KEY", "")
    S3_REGION = os.environ.get("S3_REGION", "ap-southeast-2")