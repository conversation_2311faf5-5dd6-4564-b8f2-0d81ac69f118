import logging
import time
from utils.environment import Env

logger = logging.getLogger(__name__)

class ChromaDBClient:
    _instance = None
    _client = None

    @classmethod
    def get_instance(cls):
        """Singleton pattern to ensure only one ChromaDB client instance is created"""
        if cls._instance is None:
            cls._instance = ChromaDBClient()
        return cls._instance

    def __init__(self):
        """Initialize the ChromaDB client lazily"""
        # Client will be initialized on first use
        pass

    @property
    def client(self):
        """Lazy loading of the ChromaDB client"""
        if self._client is None:
            self._initialize_client()
        return self._client

    def _initialize_client(self):
        """Initialize the ChromaDB client with retries"""
        max_retries = 5
        retry_delay = 2

        for attempt in range(max_retries):
            try:
                # Import here to avoid immediate import errors
                import chromadb
                from chromadb.config import Settings

                # Initialize ChromaDB client using persistent storage
                logger.info(f"Initializing ChromaDB client with host={Env.CHROMA_HOST}, port={Env.CHROMA_PORT}")
                try:
                    # Try to connect to a remote ChromaDB server first
                    try:
                        # For newer versions of ChromaDB
                        if hasattr(chromadb, 'HttpClient'):
                            self._client = chromadb.HttpClient(
                                host=Env.CHROMA_HOST,
                                port=int(Env.CHROMA_PORT)
                            )
                        else:
                            # For older versions, use Client with settings
                            self._client = chromadb.Client(Settings(
                                chroma_server_host=Env.CHROMA_HOST,
                                chroma_server_http_port=int(Env.CHROMA_PORT),
                                chroma_api_impl="rest"
                            ))
                        
                        # Test connection by listing collections
                        self._client.list_collections()
                        logger.info(f"ChromaDB client connected to remote server at {Env.CHROMA_HOST}:{Env.CHROMA_PORT}")
                    except Exception as remote_error:
                        logger.warning(f"Failed to connect to remote ChromaDB server: {remote_error}")
                        # Fall back to local storage
                        if hasattr(chromadb, 'PersistentClient'):
                            self._client = chromadb.PersistentClient(path="./chroma_db")
                        else:
                            # For older versions, use Client with local settings
                            self._client = chromadb.Client(Settings(
                                chroma_db_impl="duckdb+parquet",
                                persist_directory="./chroma_db"
                            ))
                        logger.info(f"ChromaDB client initialized with local storage at ./chroma_db")
                    
                    logger.info(f"ChromaDB client initialized successfully. Type: {type(self._client)}")
                except Exception as client_error:
                    logger.error(f"Error initializing ChromaDB client: {client_error}")
                    raise
                return
            except Exception as e:
                logger.warning(f"Attempt {attempt+1}/{max_retries} to initialize ChromaDB client failed: {e}")
                if attempt < max_retries - 1:
                    logger.info(f"Retrying in {retry_delay} seconds...")
                    time.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
                else:
                    logger.error(f"Failed to initialize ChromaDB client after {max_retries} attempts")
                    raise

    def create_collection(self, collection_name, metadata=None):
        """Create a new collection or get an existing one"""
        try:
            return self.client.get_or_create_collection(
                name=collection_name,
                metadata=metadata or {}
            )
        except Exception as e:
            logger.error(f"Failed to create/get collection '{collection_name}': {e}")
            raise

    def list_collections(self):
        """List all collections with retry mechanism"""
        max_retries = 3
        retry_delay = 1
        last_error = None

        for attempt in range(max_retries):
            try:
                logger.info(f"Listing collections (attempt {attempt+1}/{max_retries})")
                collections = self.client.list_collections()
                logger.info(f"Successfully listed {len(collections)} collections")
                return collections
            except Exception as e:
                last_error = e
                logger.warning(f"Failed to list collections (attempt {attempt+1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    logger.info(f"Retrying in {retry_delay} seconds...")
                    time.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff

        logger.error(f"Failed to list collections after {max_retries} attempts: {last_error}")
        raise last_error

    def delete_collection(self, collection_name):
        """Delete a collection"""
        try:
            self.client.delete_collection(collection_name)
            logger.info(f"Collection '{collection_name}' deleted")
            return True
        except Exception as e:
            logger.error(f"Failed to delete collection '{collection_name}': {e}")
            raise

    def add_documents(self, collection_name, documents, metadatas=None, ids=None):
        """Add documents to a collection"""
        try:
            collection = self.client.get_collection(collection_name)
            collection.add(
                documents=documents,
                metadatas=metadatas,
                ids=ids
            )
            logger.info(f"Added {len(documents)} documents to collection '{collection_name}'")
            return True
        except Exception as e:
            logger.error(f"Failed to add documents to collection '{collection_name}': {e}")
            raise

    def query_collection(self, collection_name, query_texts, n_results=5, where=None, where_document=None):
        """Query a collection"""
        try:
            collection = self.client.get_collection(collection_name)
            results = collection.query(
                query_texts=query_texts,
                n_results=n_results,
                where=where,
                where_document=where_document
            )
            return results
        except Exception as e:
            logger.error(f"Failed to query collection '{collection_name}': {e}")
            raise

def get_chroma_client():
    """Helper function to get the ChromaDB client instance"""
    return ChromaDBClient.get_instance()

def test_chroma_connection():
    """Test connection to ChromaDB"""
    try:
        logger.info("Testing connection to ChromaDB")
        client = get_chroma_client()
        if client is None:
            logger.error("ChromaDB client is None")
            return False, "ChromaDB client is None"

        # Try to list collections
        logger.info("Trying to list collections")
        collections = client.list_collections()
        logger.info(f"Successfully connected to ChromaDB. Found {len(collections)} collections.")
        return True, f"Connected to ChromaDB. Found {len(collections)} collections."
    except Exception as e:
        logger.error(f"Failed to connect to ChromaDB: {e}")
        return False, f"Failed to connect to ChromaDB: {e}"
