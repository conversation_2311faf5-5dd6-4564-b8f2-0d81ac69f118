# -*- coding: utf-8 -*-
from flask import Blueprint
import logging
from utils.environment import Env

# Create the Blueprint object
embed_bp = Blueprint("embed", __name__, url_prefix="/api/v1/embed")
logger = logging.getLogger(__name__)

# Initialize collections when module is imported
try:
    from utils.chroma_db import get_chroma_client
    client = get_chroma_client()
    
    # Create necessary collections
    collections = client.list_collections()
    collection_names = [c.name for c in collections]
    
    if "job_descriptions" not in collection_names:
        client.create_collection("job_descriptions", {"description": "Job description embeddings"})
        logger.info("Created job_descriptions collection")
    
    if "user_resumes" not in collection_names:
        client.create_collection("user_resumes", {"description": "User resume embeddings"})
        logger.info("Created user_resumes collection")
except Exception as e:
    logger.error(f"Cannot initialize collections: {e}")

# Import routes at the end - this ensures they're properly registered
from embed.routes import *
logger.info("Embed routes registered with blueprint")