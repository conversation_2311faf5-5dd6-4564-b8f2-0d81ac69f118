from flask import request, jsonify
import logging
from typing import Dict, Any
from sqlalchemy import and_
from datetime import datetime
import concurrent.futures
from functools import partial
import time

from embed import embed_bp
from embed.processor import process_job_embedding, process_user_embedding, format_job_data, format_resume_data, EmbeddingProcessor
from utils.environment import Env
from utils.response import AppResponse
from utils.chroma_db import get_chroma_client, test_chroma_connection
from models.account import Account
from models.user import User
from models.company import Company
from models.application_position import ApplicationPosition
from models.application_skill import ApplicationSkill
from models.languages import Language
from models.user_experience import UserExperience
from models.user_education import UserEducation
from models.user_award import UserAward
from utils import db

logger = logging.getLogger(__name__)

# Remove the threading lock
# chroma_lock = threading.Lock()

def verify_chroma_collections():
    """Check if collections exist and create them if needed"""
    try:
        client = get_chroma_client()

        collections = client.list_collections()
        collection_names = [c.name for c in collections]

        missing = []
        if "job_descriptions" not in collection_names:
            missing.append("job_descriptions")

        if "user_resumes" not in collection_names:
            missing.append("user_resumes")

        for name in missing:
            client.create_collection(name, {"description": f"Collection for {name}"})
            logger.info(f"Created collection {name}")

        return True, "Collections are ready"
    except Exception as e:
        logger.error(f"Error checking/creating collections: {e}")
        return False, str(e)

def fetch_user_data(account_id: str) -> Dict[str, Any]:
    """
    Fetch user data from database including related entities

    Args:
        account_id: User account ID

    Returns:
        Complete user data object
    """
    try:
        # Fetch account details
        account = Account.query.filter_by(account_id=account_id).first()
        if not account:
            return None

        # Fetch user details
        user = User.query.filter_by(account_id=account_id).first()
        if not user:
            return None

        # Fetch related data
        languages = Language.query.filter_by(account_id=account_id).all()
        educations = UserEducation.query.filter_by(account_id=account_id).all()
        experiences = UserExperience.query.filter_by(account_id=account_id).all()
        awards = UserAward.query.filter_by(account_id=account_id).all()

        # Build the user data object
        user_data = {
            "account_id": str(account.account_id),
            "email": account.email,
            "account_status": account.account_status,
            "address": account.address or "",
            "avatar": account.avatar or "",
            "phone_number": account.phone_number or "",
            "system_role": {
                "constant_id": str(account.system_role),
                "constant_type": "0111001",  # Assuming default user type
                "constant_name": "User",
                "description": {
                    "note": "System role"
                }
            },
            "created_at": account.created_at.isoformat() if account.created_at else None,
            "updated_at": account.updated_at.isoformat() if account.updated_at else None,
            "languages": [
                {
                    "id": str(lang.id),
                    "language_name": lang.language_name,
                    "language_score": lang.language_score,
                    "language_certificate_name": lang.language_certificate_name,
                    "created_at": lang.created_at.isoformat() if lang.created_at else None,
                    "updated_at": lang.updated_at.isoformat() if lang.updated_at else None
                } for lang in languages
            ],
            "first_name": user.first_name,
            "last_name": user.last_name,
            "gender": user.gender,
            "date_of_birth": user.date_of_birth.isoformat() if user.date_of_birth else None,
            "summary_introduction": user.summary_introduction or "",
            "social_media_link": user.social_media_link or [],
            "educations": [
                {
                    "id": str(edu.id),
                    "study_place": edu.study_place,
                    "study_start_time": edu.study_start_time.isoformat() if edu.study_start_time else None,
                    "study_end_time": edu.study_end_time.isoformat() if edu.study_end_time else None,
                    "majority": edu.majority or "",
                    "cpa": float(edu.cpa) if edu.cpa is not None else None,
                    "created_at": edu.created_at.isoformat() if edu.created_at else None,
                    "updated_at": edu.updated_at.isoformat() if edu.updated_at else None,
                    "description": edu.description or ""
                } for edu in educations
            ],
            "awards": [
                {
                    "id": str(award.id),
                    "certificate_time": award.certificate_time.isoformat() if award.certificate_time else None,
                    "certificate_name": award.certificate_name,
                    "created_at": award.created_at.isoformat() if award.created_at else None,
                    "updated_at": award.updated_at.isoformat() if award.updated_at else None,
                    "description": award.description or ""
                } for award in awards
            ],
            "experiences": [
                {
                    "id": str(exp.id),
                    "experience_start_time": exp.experience_start_time.isoformat() if exp.experience_start_time else None,
                    "experience_end_time": exp.experience_end_time.isoformat() if exp.experience_end_time else None,
                    "work_place": exp.work_place,
                    "position": exp.position,
                    "created_at": exp.created_at.isoformat() if exp.created_at else None,
                    "updated_at": exp.updated_at.isoformat() if exp.updated_at else None,
                    "description": exp.description or ""
                } for exp in experiences
            ]
        }

        return user_data
    except Exception as e:
        logger.error(f"Error fetching user data: {e}")
        return None

def fetch_job_data(company_account_id: str, application_position_id: str) -> Dict[str, Any]:
    """
    Fetch job data from database including related entities

    Args:
        company_account_id: Company account ID
        application_position_id: Position ID

    Returns:
        Complete job data object
    """
    try:
        # Fetch account details
        account = Account.query.filter_by(account_id=company_account_id).first()
        if not account:
            return None

        # Fetch company details
        company = Company.query.filter_by(account_id=company_account_id).first()
        if not company:
            return None

        # Fetch position and verify it belongs to this company
        position = ApplicationPosition.query.filter(and_(
            ApplicationPosition.id == application_position_id,
            ApplicationPosition.account_id == company_account_id
        )).first()

        if not position:
            return None

        # Fetch skills for this position
        skills = ApplicationSkill.query.filter_by(application_position_id=application_position_id).all()

        # Build the job data object
        job_data = {
            "account_id": str(account.account_id),
            "email": account.email,
            "account_status": account.account_status,
            "address": account.address or "",
            "avatar": account.avatar or "",
            "phone_number": account.phone_number or "",
            "system_role": {
                "constant_id": str(account.system_role),
                "constant_type": "0111002",  # Assuming default company type
                "constant_name": "Company",
                "note": {
                    "note": "System role"
                }
            },
            "created_at": account.created_at.isoformat() if account.created_at else None,
            "application_positions": [
                {
                    "id": str(position.id),
                    "status": position.status,
                    "apply_position_title": position.apply_position_title,
                    "salary": position.salary or "",
                    "description": position.description or "",
                    "skills": [
                        {
                            "id": str(skill.id),
                            "skill_name": skill.skill_name,
                            "created_at": skill.created_at.isoformat() if skill.created_at else None,
                            "updated_at": skill.updated_at.isoformat() if skill.updated_at else None
                        } for skill in skills
                    ],
                    "created_at": position.created_at.isoformat() if position.created_at else None,
                    "updated_at": position.updated_at.isoformat() if position.updated_at else None
                }
            ],
            "languages": [],  # Companies don't have languages in this model
            "company_name": company.company_name,
            "company_url": company.company_url or "",
            "established_date": company.established_date.isoformat() if company.established_date else None
        }

        return job_data
    except Exception as e:
        logger.error(f"Error fetching job data: {e}")
        return None

@embed_bp.route("/jobs", methods=["POST"])
def embed_job():
    """
    API endpoint to create and store embeddings for job descriptions.

    Request Headers:
    - X-API-KEY: FLASK_PASSWORD
    - X-Account-ID: Company account ID
    - X-Position-ID: Application position ID

    Returns:
        JSON response with embedding results
    """
    try:
        # Debug: Print headers
        print("DEBUG: Headers received:", dict(request.headers))
        print(f"DEBUG: API KEY from ENV: {Env.FLASK_PASSWORD}")

        # Check and create collections if needed
        success, message = verify_chroma_collections()
        if not success:
            return AppResponse.server_error(error=message)

        # Check API key from headers or query parameters
        api_key = request.headers.get("X-API-KEY") or request.args.get("X-API-KEY")
        if api_key != Env.FLASK_PASSWORD:
            return AppResponse.bad_request(message="Forbidden", status_code=403)

        # Get account and position IDs from headers or query parameters
        account_id = request.headers.get("X-Account-ID") or request.args.get("X-Account-ID")
        position_id = request.headers.get("X-Position-ID") or request.args.get("X-Position-ID")

        if not account_id or not position_id:
            return AppResponse.bad_request(message="Missing required parameters: X-Account-ID and/or X-Position-ID")

        # Fetch job data from database
        job_data = fetch_job_data(account_id, position_id)
        if not job_data:
            return AppResponse.bad_request(message="Job not found or does not belong to this company")

        # Extract position data (assuming first position in list)
        position_data = None
        if job_data.get("application_positions") and len(job_data["application_positions"]) > 0:
            position_data = job_data["application_positions"][0]

        if not position_data:
            return AppResponse.bad_request(message="No position data found in the job data")

        # Get account_id and position_id
        account_id = job_data.get("account_id")
        position_id = position_data.get("id")

        # Create metadata with basic information
        metadata = {
            "api_key": Env.FLASK_PASSWORD,
            "company_name": job_data.get("company_name", "Unknown Company"),
            "position_title": position_data.get("apply_position_title", "Unknown Position"),
            "processed_at": datetime.now().isoformat()
        }

        # Build request body for embedding processor
        request_body = {
            "key": Env.FLASK_PASSWORD,
            "data": job_data
        }

        # Use the new EmbeddingProcessor với automatic matrix creation
        processor = EmbeddingProcessor()

        # Create structured text
        format_request = {"data": job_data}
        structured_text = format_job_data(format_request)

        if not structured_text:
            return AppResponse.bad_request(message="Could not create structured text from job data")

        # Process embedding with automatic matrix creation
        job_id = f"job_{account_id}_{position_id}"
        result = processor.process_and_store_embedding(
            content=structured_text,
            entity_id=job_id,
            entity_type="job",
            collection_name="job_descriptions",
            metadata=metadata
        )

        if not result["success"]:
            return AppResponse.bad_request(message=result.get("error", "Failed to process embedding"))

        # Create response object
        response_data = {
            "job_id": job_id,
            "account_id": account_id,
            "position_id": position_id,
            "position_title": position_data.get("apply_position_title", "Unknown Position"),
            "embedding_created": result["success"],
            "matrix_created": result["matrix_created"],
            "structured_text": structured_text,
            "embedding_dimensions": result["embedding_dimension"],
            "matrix_info": result.get("matrix_info", {}),
            "metadata": metadata
        }

        return AppResponse.success_with_data(data=response_data, message=result["message"])

        if success:
            return AppResponse.success_with_data(data=result, message=message)
        else:
            return AppResponse.bad_request(message=message)
    except Exception as e:
        logger.error(f"Error processing job embedding: {e}")
        return AppResponse.server_error(error=e)

# Keep old route for backward compatibility
@embed_bp.route("/job", methods=["POST"])
def embed_job_deprecated():
    return embed_job()

@embed_bp.route("/users", methods=["POST"])
def embed_user():
    """
    API endpoint to create and store embeddings for user resumes.

    Request Headers:
    - X-API-KEY: FLASK_PASSWORD
    - X-Account-ID: User account ID

    Returns:
        JSON response with embedding results
    """
    try:
        # Debug: Print headers
        print("DEBUG: Headers received:", dict(request.headers))
        print(f"DEBUG: API KEY from ENV: {Env.FLASK_PASSWORD}")

        # Check and create collections if needed
        success, message = verify_chroma_collections()
        if not success:
            return AppResponse.server_error(error=message)

        # Check API key from headers or query parameters
        api_key = request.headers.get("X-API-KEY") or request.args.get("X-API-KEY")
        if api_key != Env.FLASK_PASSWORD:
            return AppResponse.bad_request(message="Forbidden", status_code=403)

        # Get account ID from headers or query parameters
        account_id = request.headers.get("X-Account-ID") or request.args.get("X-Account-ID")

        if not account_id:
            return AppResponse.bad_request(message="Missing required parameter: X-Account-ID")

        # Fetch user data from database
        user_data = fetch_user_data(account_id)
        if not user_data:
            return AppResponse.bad_request(message=f"User not found for account_id: {account_id}")

        # Extract the required parameters for embedding processor
        account_id = user_data.get("account_id")

        # Create metadata with basic information
        metadata = {
            "api_key": Env.FLASK_PASSWORD,
            "user_name": f"{user_data.get('first_name', '')} {user_data.get('last_name', '')}".strip() or "Unknown User",
            "processed_at": datetime.now().isoformat()
        }

        # Use the new EmbeddingProcessor với automatic matrix creation
        processor = EmbeddingProcessor()

        # Create structured text
        format_request = {"data": user_data}
        structured_text = format_resume_data(format_request)

        if not structured_text:
            return AppResponse.bad_request(message="Could not create structured text from user data")

        # Process embedding with automatic matrix creation
        user_id = f"user_{account_id}"
        result = processor.process_and_store_embedding(
            content=structured_text,
            entity_id=user_id,
            entity_type="user",
            collection_name="user_resumes",
            metadata=metadata
        )

        if not result["success"]:
            return AppResponse.bad_request(message=result.get("error", "Failed to process embedding"))

        # Get user's full name
        first_name = user_data.get("first_name", "")
        last_name = user_data.get("last_name", "")
        full_name = f"{first_name} {last_name}".strip()

        # Create response object
        response_data = {
            "user_id": user_id,
            "account_id": account_id,
            "name": full_name,
            "embedding_created": result["success"],
            "matrix_created": result["matrix_created"],
            "structured_text": structured_text,
            "embedding_dimensions": result["embedding_dimension"],
            "matrix_info": result.get("matrix_info", {}),
            "metadata": metadata
        }

        return AppResponse.success_with_data(data=response_data, message=result["message"])
    except Exception as e:
        logger.error(f"Error processing resume embedding: {e}")
        return AppResponse.server_error(error=e)

# Keep old route for backward compatibility
@embed_bp.route("/user", methods=["POST"])
def embed_user_deprecated():
    return embed_user()

@embed_bp.route("/health-check", methods=["GET"])
def embed_health_check():
    """
    Check embedding API status.

    Returns:
        JSON response with status
    """
    try:
        # Check and create collections if needed
        success, message = verify_chroma_collections()
        if not success:
            return AppResponse.server_error(error=message)

        # Check ChromaDB connection
        chroma_success, chroma_message = test_chroma_connection()

        if chroma_success:
            return AppResponse.success_with_data(
                data={
                    "status": "healthy",
                    "collections_check": message,
                    "chroma_connection": chroma_message
                },
                message="Embedding API is functioning normally"
            )
        else:
            return AppResponse.success_with_data(
                data={
                    "status": "degraded",
                    "collections_check": message,
                    "chroma_connection": chroma_message
                },
                message="Embedding API is operating but has issues with ChromaDB"
            )
    except Exception as e:
        logger.error(f"Error checking embedding status: {e}")
        return AppResponse.server_error(error=str(e))

# Add a shorter route for health check
@embed_bp.route("/health", methods=["GET"])
def embed_health_short():
    """
    Alternative short URL for health check
    """
    return embed_health_check()

def process_single_user(user, user_collection, skip_existing=False):
    """
    Process a single user for embedding - designed for parallel execution

    Returns:
        Dict with status information
    """
    result = {
        "processed": False,
        "success": False,
        "skipped": False,
        "error": None
    }

    try:
        account_id = str(user.account_id)

        # Check if user already has an embedding
        if skip_existing:
            try:
                existing = user_collection.get(ids=[account_id])
                if existing and len(existing["ids"]) > 0:
                    logger.info(f"Skipping user {account_id} - embedding already exists")
                    result["skipped"] = True
                    return result
            except Exception as e:
                logger.warning(f"Error checking existing user embedding: {e}")

        # Fetch full user data
        user_data = fetch_user_data(account_id)
        result["processed"] = True

        if not user_data:
            logger.warning(f"Could not fetch data for user {account_id}")
            result["error"] = "Could not fetch user data"
            return result

        # Build request body for embedding processor
        request_body = {
            "key": Env.FLASK_PASSWORD,
            "data": user_data
        }

        # Process user embedding
        success, _, _ = process_user_embedding(request_body)

        result["success"] = success

        if not success:
            result["error"] = "Failed to process embedding"

        return result

    except Exception as e:
        logger.error(f"Error processing user {getattr(user, 'account_id', 'unknown')}: {e}")
        result["error"] = str(e)
        return result

def process_single_job(position, company_id, job_collection, skip_existing=False):
    """
    Process a single job position for embedding - designed for parallel execution

    Returns:
        Dict with status information
    """
    result = {
        "processed": False,
        "success": False,
        "skipped": False,
        "error": None
    }

    try:
        position_id = str(position.id)

        # Check if job already has an embedding
        if skip_existing:
            try:
                existing = job_collection.get(ids=[position_id])
                if existing and len(existing["ids"]) > 0:
                    logger.info(f"Skipping job {position_id} - embedding already exists")
                    result["skipped"] = True
                    return result
            except Exception as e:
                logger.warning(f"Error checking existing job embedding: {e}")

        # Fetch full job data
        job_data = fetch_job_data(company_id, position_id)
        result["processed"] = True

        if not job_data:
            logger.warning(f"Could not fetch data for job {position_id} at company {company_id}")
            result["error"] = "Could not fetch job data"
            return result

        # Build request body for embedding processor
        request_body = {
            "key": Env.FLASK_PASSWORD,
            "data": job_data
        }

        # Process job embedding
        success, _, _ = process_job_embedding(request_body)

        result["success"] = success

        if not success:
            result["error"] = "Failed to process embedding"

        return result

    except Exception as e:
        logger.error(f"Error processing job {getattr(position, 'id', 'unknown')}: {e}")
        result["error"] = str(e)
        return result

@embed_bp.route("/bulk", methods=["POST"])
def embed_bulk():
    """
    API endpoint to create and store embeddings for ALL users and jobs in the database.
    Uses sequential processing for reliability.

    Request Headers:
    - X-API-KEY: FLASK_PASSWORD

    Query Parameters:
    - limit: Optional limit of records to process per type (default: all)
    - skip_existing: Skip records that already have embeddings (default: false)

    Returns:
        JSON response with embedding results and collection stats
    """
    try:
        start_time = time.time()

        # Check API key
        api_key = request.headers.get("X-API-KEY") or request.args.get("key")
        if api_key != Env.FLASK_PASSWORD:
            return AppResponse.bad_request(message="Forbidden", status_code=403)

        # Get parameters
        limit = request.args.get("limit", type=int)
        skip_existing = request.args.get("skip_existing", "false").lower() == "true"

        # Check and create collections if needed
        success, message = verify_chroma_collections()
        if not success:
            return AppResponse.server_error(error=message)

        # Get ChromaDB client for checking existing embeddings
        client = get_chroma_client()
        if client is None:
            return AppResponse.server_error(error="ChromaDB client not available")

        job_collection = client.client.get_or_create_collection("job_descriptions")
        user_collection = client.client.get_or_create_collection("user_resumes")

        # Stats to track progress
        stats = {
            "users": {
                "total": 0,
                "processed": 0,
                "success": 0,
                "failed": 0,
                "skipped": 0
            },
            "jobs": {
                "total": 0,
                "processed": 0,
                "success": 0,
                "failed": 0,
                "skipped": 0
            },
            "timing": {
                "started_at": datetime.now().isoformat(),
                "total_duration_seconds": 0
            }
        }

        # Process Users
        logger.info("Starting bulk embedding for users (sequential processing)")

        # Fetch all users from database
        users_query = User.query.join(Account, User.account_id == Account.account_id)

        if limit:
            users_query = users_query.limit(limit)

        users = users_query.all()
        stats["users"]["total"] = len(users)
        logger.info(f"Found {stats['users']['total']} users to process")

        # Process each user sequentially
        for user in users:
            try:
                account_id = str(user.account_id)

                # Check if user already has an embedding
                if skip_existing:
                    try:
                        existing = user_collection.get(ids=[account_id])
                        if existing and len(existing["ids"]) > 0:
                            logger.info(f"Skipping user {account_id} - embedding already exists")
                            stats["users"]["skipped"] += 1
                            continue
                    except Exception as e:
                        logger.warning(f"Error checking existing user embedding: {e}")

                # Fetch full user data
                user_data = fetch_user_data(account_id)
                stats["users"]["processed"] += 1

                if not user_data:
                    logger.warning(f"Could not fetch data for user {account_id}")
                    stats["users"]["failed"] += 1
                    continue

                # Use EmbeddingProcessor for automatic matrix creation
                from embed.processor import EmbeddingProcessor
                processor = EmbeddingProcessor()

                # Convert user data to structured text first
                format_request = {"data": user_data}
                structured_text = format_resume_data(format_request)

                if not structured_text:
                    logger.error(f"Could not create structured text for user {account_id}")
                    stats["users"]["failed"] += 1
                    continue

                # Prepare metadata
                first_name = user_data.get("first_name", "")
                last_name = user_data.get("last_name", "")
                full_name = f"{first_name} {last_name}".strip()
                created_at = user_data.get("created_at", datetime.now().isoformat())
                metadata = {
                    "type": "resume",
                    "name": full_name,
                    "date": created_at
                }

                # Process user embedding with automatic matrix creation
                result = processor.process_and_store_embedding(
                    content=structured_text,
                    entity_id=account_id,
                    entity_type="user",
                    collection_name="user_resumes",
                    metadata=metadata
                )

                if result["success"]:
                    stats["users"]["success"] += 1
                    if result.get("matrix_created"):
                        logger.info(f"Matrix created for user {account_id}")
                else:
                    stats["users"]["failed"] += 1
                    logger.error(f"Failed to process user {account_id}: {result.get('message', 'Unknown error')}")

            except Exception as e:
                logger.error(f"Error processing user {getattr(user, 'account_id', 'unknown')}: {e}")
                stats["users"]["failed"] += 1

        # Process Jobs (Companies and their positions)
        logger.info("Starting bulk embedding for jobs (sequential processing)")

        # Fetch all companies and their positions
        companies_query = Company.query.join(Account, Company.account_id == Account.account_id)

        if limit:
            companies_query = companies_query.limit(limit)

        companies = companies_query.all()

        # Count total positions
        all_positions = []
        company_ids = {}  # Map position_id -> company_id for easier lookup

        for company in companies:
            company_id = str(company.account_id)
            positions = ApplicationPosition.query.filter_by(account_id=company_id).all()

            for position in positions:
                all_positions.append(position)
                company_ids[str(position.id)] = company_id

        stats["jobs"]["total"] = len(all_positions)
        logger.info(f"Found {stats['jobs']['total']} jobs across {len(companies)} companies to process")

        # Process each job sequentially
        for position in all_positions:
            try:
                position_id = str(position.id)
                company_id = company_ids[position_id]

                # Check if job already has an embedding
                if skip_existing:
                    try:
                        existing = job_collection.get(ids=[position_id])
                        if existing and len(existing["ids"]) > 0:
                            logger.info(f"Skipping job {position_id} - embedding already exists")
                            stats["jobs"]["skipped"] += 1
                            continue
                    except Exception as e:
                        logger.warning(f"Error checking existing job embedding: {e}")

                # Fetch full job data
                job_data = fetch_job_data(company_id, position_id)
                stats["jobs"]["processed"] += 1

                if not job_data:
                    logger.warning(f"Could not fetch data for job {position_id} at company {company_id}")
                    stats["jobs"]["failed"] += 1
                    continue

                # Use EmbeddingProcessor for automatic matrix creation
                from embed.processor import EmbeddingProcessor
                processor = EmbeddingProcessor()

                # Convert job data to structured text first
                format_request = {"data": job_data}
                structured_text = format_job_data(format_request)

                if not structured_text:
                    logger.error(f"Could not create structured text for job {position_id}")
                    stats["jobs"]["failed"] += 1
                    continue

                # Prepare metadata
                company_name = job_data.get("company_name", "Unknown Company")
                position_title = job_data.get("apply_position_title", "Unknown Position")
                created_at = job_data.get("created_at", datetime.now().isoformat())
                metadata = {
                    "type": "job",
                    "company": company_name,
                    "position": position_title,
                    "date": created_at
                }

                # Create composite entity_id for job
                job_entity_id = f"job_{company_id}_{position_id}"

                # Process job embedding with automatic matrix creation
                result = processor.process_and_store_embedding(
                    content=structured_text,
                    entity_id=job_entity_id,
                    entity_type="job",
                    collection_name="job_descriptions",
                    metadata=metadata
                )

                if result["success"]:
                    stats["jobs"]["success"] += 1
                    if result.get("matrix_created"):
                        logger.info(f"Matrix created for job {job_entity_id}")
                else:
                    stats["jobs"]["failed"] += 1
                    logger.error(f"Failed to process job {job_entity_id}: {result.get('message', 'Unknown error')}")
                    stats["jobs"]["failed"] += 1

            except Exception as e:
                logger.error(f"Error processing job {getattr(position, 'id', 'unknown')}: {e}")
                stats["jobs"]["failed"] += 1

        # Get updated collection information
        collections = client.list_collections()
        collection_list = []
        for collection in collections:
            collection_list.append({
                "name": collection.name,
                "metadata": collection.metadata,
                "count": collection.count()
            })

        # Calculate total duration
        end_time = time.time()
        duration_seconds = end_time - start_time
        stats["timing"]["total_duration_seconds"] = round(duration_seconds, 2)
        stats["timing"]["finished_at"] = datetime.now().isoformat()
        stats["timing"]["average_time_per_item"] = round(duration_seconds / max(1,
                                                      stats["users"]["processed"] +
                                                      stats["jobs"]["processed"]), 2)

        # Create response with both stats and collection info
        result = {
            "collections": collection_list,
            "embedding_stats": stats
        }

        return AppResponse.success_with_data(
            data=result,
            message=f"Bulk embedding completed in {duration_seconds:.2f}s: {stats['users']['success']} users and {stats['jobs']['success']} jobs embedded successfully"
        )

    except Exception as e:
        logger.error(f"Error in bulk embedding: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return AppResponse.server_error(error=str(e))

# Add a route that returns just the collection statistics (similar to /vector/collections)
@embed_bp.route("/collections", methods=["GET"])
def embed_collections():
    """
    Get collection statistics after embedding.

    Returns:
        JSON response with collection information
    """
    try:
        # Check API key
        api_key = request.args.get("key", "")
        if api_key != Env.FLASK_PASSWORD:
            return AppResponse.bad_request(message="Forbidden", status_code=403)

        # Get ChromaDB client
        client = get_chroma_client()
        if client is None:
            return AppResponse.server_error(error="ChromaDB client not available")

        # Get collections
        collections = client.list_collections()
        collection_list = []
        for collection in collections:
            collection_list.append({
                "name": collection.name,
                "metadata": collection.metadata,
                "count": collection.count()
            })

        return AppResponse.success_with_data(data=collection_list)

    except Exception as e:
        logger.error(f"Error getting collections: {e}")
        return AppResponse.server_error(error=str(e))

@embed_bp.route("/process-and-store", methods=["POST"])
def process_and_store_embedding():
    """
    Xử lý content thành embedding và tự động tạo ma trận cá nhân hóa
    """
    try:
        # Verify API key
        body = request.get_json()
        flask_key = body.get("key", "")
        if flask_key != Env.FLASK_PASSWORD:
            return AppResponse.bad_request(message="Forbidden", status_code=403)

        # Validate input
        content = body.get("content", "")
        entity_id = body.get("entity_id", "")
        entity_type = body.get("entity_type", "")  # 'user' or 'job'
        collection_name = body.get("collection_name", "")
        metadata = body.get("metadata", {})

        if not all([content, entity_id, entity_type, collection_name]):
            return AppResponse.bad_request(
                message="Cần cung cấp content, entity_id, entity_type và collection_name"
            )

        if entity_type not in ["user", "job"]:
            return AppResponse.bad_request(
                message="entity_type phải là 'user' hoặc 'job'"
            )

        # Process embedding với automatic matrix creation
        processor = EmbeddingProcessor()
        result = processor.process_and_store_embedding(
            content=content,
            entity_id=entity_id,
            entity_type=entity_type,
            collection_name=collection_name,
            metadata=metadata
        )

        if result["success"]:
            return AppResponse.success_with_data(
                data=result,
                message=f"Đã xử lý thành công embedding và ma trận cho {entity_type} {entity_id}"
            )
        else:
            return AppResponse.server_error(error=result.get("error", "Unknown error"))

    except Exception as e:
        logger.error(f"Error in process_and_store_embedding: {e}")
        return AppResponse.server_error(error=str(e))


@embed_bp.route("/job-with-matrix", methods=["POST"])
def process_job_with_matrix():
    """
    Xử lý job data thành embedding và tự động tạo ma trận cá nhân hóa
    """
    try:
        body = request.get_json()
        flask_key = body.get("key", "")
        if flask_key != Env.FLASK_PASSWORD:
            return AppResponse.bad_request(message="Forbidden", status_code=403)

        # Process job embedding
        success, job_result, message = process_job_embedding(body)

        if not success:
            return AppResponse.server_error(error=message)

        # Tự động tạo ma trận cho job
        processor = EmbeddingProcessor()
        matrix_result = processor._create_personalization_matrix(
            entity_id=job_result["job_id"],
            entity_type="job",
            dimension=job_result["embedding_dimensions"],
            entity_embedding=job_result["embedding"]
        )

        # Combine results
        combined_result = {
            **job_result,
            "matrix_creation": matrix_result
        }

        return AppResponse.success_with_data(
            data=combined_result,
            message="Đã xử lý job embedding và tạo ma trận thành công"
        )

    except Exception as e:
        logger.error(f"Error in process_job_with_matrix: {e}")
        return AppResponse.server_error(error=str(e))


@embed_bp.route("/user-with-matrix", methods=["POST"])
def process_user_with_matrix():
    """
    Xử lý user resume thành embedding và tự động tạo ma trận cá nhân hóa thông minh
    """
    try:
        body = request.get_json()
        flask_key = body.get("key", "")
        if flask_key != Env.FLASK_PASSWORD:
            return AppResponse.bad_request(message="Forbidden", status_code=403)

        # Process user embedding
        success, user_result, message = process_user_embedding(body)

        if not success:
            return AppResponse.server_error(error=message)

        # Tự động tạo ma trận thông minh cho user
        processor = EmbeddingProcessor()
        matrix_result = processor._create_personalization_matrix(
            entity_id=user_result["account_id"],
            entity_type="user",
            dimension=user_result["embedding_dimensions"],
            entity_embedding=user_result["embedding"]
        )

        # Combine results
        combined_result = {
            **user_result,
            "matrix_creation": matrix_result
        }

        return AppResponse.success_with_data(
            data=combined_result,
            message="Đã xử lý user embedding và tạo ma trận thông minh thành công"
        )

    except Exception as e:
        logger.error(f"Error in process_user_with_matrix: {e}")
        return AppResponse.server_error(error=str(e))