import json
import logging
import openai
import numpy as np
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple

from utils.environment import Env
from utils.chroma_db import get_chroma_client
from personalization.db_matrix_storage import get_db_matrix_storage
from personalization.cold_start import initialize_user_matrix

logger = logging.getLogger(__name__)

def format_job_data(job_data: Dict[str, Any]) -> str:
    """
    Convert job data to structured text format.
    
    Args:
        job_data: Job data from request
        
    Returns:
        Structured text
    """
    try:
        account_data = job_data.get("data", {})
        company_name = account_data.get("company_name", "")
        application_positions = account_data.get("application_positions", [])
        
        # Check data
        if not application_positions or len(application_positions) == 0:
            return ""
        
        # Get first position
        position = application_positions[0]
        position_title = position.get("apply_position_title", "")
        position_salary = position.get("salary", "")
        position_status = "Active" if position.get("status", False) else "Inactive"
        position_description = position.get("description", "")
        
        # Get skills
        skills = position.get("skills", [])
        skill_names = [skill.get("skill_name", "") for skill in skills]
        
        # Create structured text
        structured_text = f"""
        Company: {company_name}
        Position: {position_title}
        Salary: {position_salary}
        Status: {position_status}
        Skills Required: {', '.join(skill_names)}
        Description: {position_description}
        """
        
        return structured_text.strip()
    except Exception as e:
        logger.error(f"Error formatting job data: {e}")
        return ""

def format_resume_data(resume_data: Dict[str, Any]) -> str:
    """
    Convert resume data to structured text format.
    
    Args:
        resume_data: Resume data from request
        
    Returns:
        Structured text
    """
    try:
        account_data = resume_data.get("data", {})
        first_name = account_data.get("first_name", "")
        last_name = account_data.get("last_name", "")
        full_name = f"{first_name} {last_name}".strip()
        address = account_data.get("address", "")
        summary = account_data.get("summary_introduction", "")
        
        # Education
        educations = account_data.get("educations", [])
        education_text = ""
        for edu in educations:
            study_place = edu.get("study_place", "")
            majority = edu.get("majority", "")
            start_time = edu.get("study_start_time", "")
            end_time = edu.get("study_end_time", "")
            
            if study_place:
                education_text += f"- {study_place}"
                if majority:
                    education_text += f", {majority}"
                if start_time:
                    # Convert time to year format
                    try:
                        start_year = datetime.fromisoformat(start_time.replace('Z', '+00:00')).year
                        if end_time:
                            end_year = datetime.fromisoformat(end_time.replace('Z', '+00:00')).year
                            education_text += f" ({start_year}-{end_year})"
                        else:
                            education_text += f" ({start_year}-Present)"
                    except:
                        pass
                education_text += "\n"
        
        # Experience
        experiences = account_data.get("experiences", [])
        experience_text = ""
        for exp in experiences:
            position = exp.get("position", "")
            work_place = exp.get("work_place", "")
            start_time = exp.get("experience_start_time", "")
            end_time = exp.get("experience_end_time", "")
            description = exp.get("description", "")
            
            if position or work_place:
                if position:
                    experience_text += f"- {position}"
                    if work_place:
                        experience_text += f" at {work_place}"
                else:
                    experience_text += f"- {work_place}"
                
                if start_time:
                    # Convert time to year format
                    try:
                        start_year = datetime.fromisoformat(start_time.replace('Z', '+00:00')).year
                        if end_time:
                            end_year = datetime.fromisoformat(end_time.replace('Z', '+00:00')).year
                            experience_text += f" ({start_year}-{end_year})"
                        else:
                            experience_text += f" ({start_year}-Present)"
                    except:
                        pass
                
                if description:
                    experience_text += f"\n  {description}"
                experience_text += "\n"
        
        # Languages
        languages = account_data.get("languages", [])
        language_names = [lang.get("language_name", "") for lang in languages]
        
        # Create structured text
        structured_text = f"""
        Name: {full_name}
        Location: {address}
        Summary: {summary}
        
        Education:
        {education_text}
        
        Experience:
        {experience_text}
        
        Languages: {', '.join(language_names)}
        """
        
        return structured_text.strip()
    except Exception as e:
        logger.error(f"Error formatting resume data: {e}")
        return ""

def generate_description(text: str, entity_type: str) -> str:
    """
    Generate a concise description from text using OpenAI GPT-4.1 nano.
    
    Args:
        text: Input text
        entity_type: Entity type ("job" or "resume")
        
    Returns:
        AI-generated description
    """
    try:
        # Common format guidelines for both job and resume
        common_guidelines = """
        UNIVERSAL FORMATTING GUIDELINES:
        - Create a flowing, paragraph-style description without bullet points or section headers
        - Begin directly with the Role/Identity and flow naturally through the information
        - Use concise, precise language without redundancy or filler phrases
        - Quantify ALL requirements/experience with specific metrics (years, team size, etc.)
        - Use industry-standard terminology consistently and third-person perspective throughout
        - Apply a unified taxonomy for technical skills (Frontend, Backend, Data, DevOps, Mobile, etc.)
        - Remove subjective qualifiers ("extensive", "exceptional") in favor of measurable indicators
        - Structure information by importance/relevance
        - Ensure output is between 350-400 tokens exactly - prioritize key information if length is a concern
        - Write in third-person perspective only, never use second-person ("you") phrasing
        - If output exceeds 400 tokens, automatically truncate the least critical details
        """
        
        # Create a unified structure prompt for both job and resume
        if entity_type == "job":
            prompt = f"""{common_guidelines}

            Create a vector-optimized job description (350-400 tokens) aligned with candidate profile structure for optimal matching.

            EXTRACT AND INCLUDE WITHOUT NUMBERING OR LABELS:
            • Role Identity (position, seniority, industry context)
            • Technical Skills (all technical skills with explicit proficiency levels)
            • Functional Skills & Domain Knowledge (non-technical skills and industry expertise)
            • Key Responsibilities & Deliverables (critical functions and outcomes)
            
            EXAMPLE STRUCTURE (BEGINNING ONLY):
            "Senior Backend Engineer at FinTech startup with 5+ years experience in distributed systems. Proficient in Advanced Java, Intermediate Kubernetes, and Expert SQL..."
            
            SOURCE TEXT:
            {text}
            
            OUTPUT: A single dense, information-rich paragraph that begins with the role title and flows naturally through all required elements without labels or bullet points. Must be exactly 350-400 tokens. If exceeding 400 tokens, prioritize technical skills and core responsibilities.
            """
        else:  # resume
            prompt = f"""{common_guidelines}

            Create a vector-optimized candidate profile (350-400 tokens) aligned with job description structure for optimal matching.

            EXTRACT AND INCLUDE WITHOUT NUMBERING OR LABELS:
            • Professional Identity (name, current role, industry context, years of experience)
            • Technical Skills (all technical skills with explicit proficiency levels)
            • Functional Skills & Domain Knowledge (non-technical skills and industry expertise)
            • Key Achievements & Deliverables (significant professional accomplishments)
            
            EXAMPLE STRUCTURE (BEGINNING ONLY):
            "John Smith, Full Stack Developer with 4 years of experience in e-commerce. Skilled in Advanced React.js, Intermediate Node.js, and Proficient MongoDB..."
            
            SOURCE TEXT:
            {text}
            
            OUTPUT: A single dense, information-rich paragraph that begins with the person's name/role and flows naturally through all required elements without labels or bullet points. Must be exactly 350-400 tokens. If exceeding 400 tokens, prioritize technical skills and recent achievements.
            """
        
        # Log the input text we're using for description generation
        logger.info(f"Generating {entity_type} description with GPT-4.1 nano from text: {text}")
        
        # Call OpenAI API
        openai.api_key = Env.OPENAI_API_KEY
        response = openai.chat.completions.create(
            model="gpt-4.1-nano",  # Use GPT-4.1 Nano
            messages=[
                {"role": "system", "content": "You are an AI specialized in creating vector-optimized, skill-centric descriptions for job/resume matching systems. Your primary goal is to enable accurate semantic matching by ensuring structural alignment. Generate cohesive, flowing paragraphs without section headers, bullet points, or numbered lists. Use third-person perspective exclusively. Focus on technical specificity, quantifiable metrics, and consistent skill taxonomy. Maintain strict token count (350-400) in output."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=500,
            temperature=0.2  # Lower temperature for more consistent, focused output
        )
        
        # Extract generated description
        description = response.choices[0].message.content.strip()
        
        return description
    except Exception as e:
        logger.error(f"Error generating description: {e}")
        return text[:300]  # Return first part of original text if error occurs

def generate_embedding(text: str) -> List[float]:
    """
    Generate embedding vector from text using OpenAI API.
    
    Args:
        text: Input text
        
    Returns:
        Embedding vector
    """
    try:
        openai.api_key = Env.OPENAI_API_KEY
        response = openai.embeddings.create(
            model="text-embedding-3-small",  # Use small model with 256 dimensions
            input=[text],
            dimensions=256  # Embedding dimensions
        )
        return response.data[0].embedding
    except Exception as e:
        logger.error(f"Error generating embedding: {e}")
        return []

def store_in_chromadb(
    collection_name: str, 
    id: str, 
    embedding: List[float], 
    description: str, 
    metadata: Dict[str, Any]
) -> Tuple[bool, str]:
    """
    Store embedding in ChromaDB.
    
    Args:
        collection_name: Collection name
        id: Document ID
        embedding: Embedding vector
        description: Text description
        metadata: Metadata
        
    Returns:
        Tuple (success, message)
    """
    try:
        client = get_chroma_client()
        if client is None:
            return False, "ChromaDB client not available"
        
        # Log which client we're using and the ID being stored
        logger.info(f"Using ChromaDB client: {type(client)} to store embedding")
        logger.info(f"Storing embedding for ID={id} in collection '{collection_name}'")
        logger.info(f"Embedding dimensions: {len(embedding)}")
        
        # Check and log collections list
        try:
            # Handle both client structures
            if hasattr(client, 'client'):
                all_collections = client.client.list_collections()
                collection = client.client.get_or_create_collection(name=collection_name)
            else:
                all_collections = client.list_collections()
                collection = client.get_or_create_collection(name=collection_name)
                
            collection_names = [c.name for c in all_collections]
            logger.info(f"Available collections: {collection_names}")
            logger.info(f"Retrieved/created collection '{collection_name}'")
            
            # Check if document exists
            existing_docs = collection.get(ids=[id])
            has_existing = len(existing_docs['ids']) > 0
            
            if has_existing:
                # Document exists, update it
                logger.info(f"Found existing document with ID={id}, deleting before update")
                collection.delete(
                    ids=[id]
                )
                logger.info(f"Deleted existing document with ID={id} from collection '{collection_name}'")
            
            # Add/update the document
            logger.info(f"Adding document with ID={id} to collection '{collection_name}'")
            collection.add(
                ids=[id],
                embeddings=[embedding],
                documents=[description],
                metadatas=[metadata]
            )
            
            # Verify the document was added correctly
            verify_docs = collection.get(ids=[id])
            if len(verify_docs['ids']) > 0:
                logger.info(f"Successfully verified document ID={id} exists after add/update")
            else:
                logger.warning(f"Document ID={id} not found after add operation!")
                
            # Check document count
            count = collection.count()
            logger.info(f"Collection '{collection_name}' now has {count} documents")
            
            action = "updated" if has_existing else "saved"
            return True, f"Embedding {action} in collection '{collection_name}'"
        
        except Exception as ce:
            logger.error(f"Unable to access collections: {ce}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return False, f"Unable to access collections: {ce}"
            
    except Exception as e:
        logger.error(f"Error storing in ChromaDB: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False, f"Error storing in ChromaDB: {e}"

def process_job_embedding(job_data: Dict[str, Any]) -> Tuple[bool, Dict[str, Any], str]:
    """
    Process job data to create and store embeddings.
    
    Args:
        job_data: Job data from request
        
    Returns:
        Tuple (success, result_data, message)
    """
    try:
        # Check input data
        account_data = job_data.get("data", {})
        application_positions = account_data.get("application_positions", [])
        company_name = account_data.get("company_name", "Unknown Company")
        
        if not application_positions or len(application_positions) == 0:
            return False, {}, "No application position data"
        
        # Get job ID
        position = application_positions[0]
        job_id = position.get("id", "")
        
        if not job_id:
            return False, {}, "Invalid job ID"
        
        # Convert to structured text
        structured_text = format_job_data(job_data)
        
        if not structured_text:
            return False, {}, "Could not create structured text from job data"
        
        # Generate description
        description = generate_description(structured_text, "job")
        
        if not description:
            return False, {}, "Could not generate description from structured text"
        
        # Generate embedding
        embedding = generate_embedding(description)
        
        if not embedding:
            return False, {}, "Could not generate embedding from description"
        
        # Prepare metadata
        created_at = position.get("created_at", datetime.now().isoformat())
        metadata = {
            "type": "job",
            "company": company_name,
            "date": created_at,
            "position": position.get("apply_position_title", "Unknown Position")
        }
        
        # Store in ChromaDB - Use consistent collection_name
        success, message = store_in_chromadb(
            collection_name="job_descriptions",
            id=job_id,
            embedding=embedding,
            description=description,
            metadata=metadata
        )
        
        if not success:
            return False, {}, message
        
        # Return result with structured text included
        result = {
            "job_id": job_id,
            "description": description,
            "structured_text": structured_text,  # Include the structured text in the response
            "embedding_dimensions": len(embedding),
            "embedding": embedding,
            "metadata": metadata,
            "document": description
        }
        
        return True, result, "Job embedding processed successfully"
    except Exception as e:
        logger.error(f"Error processing job embedding: {e}")
        return False, {}, f"Error processing job embedding: {e}"

def process_user_embedding(resume_data: Dict[str, Any]) -> Tuple[bool, Dict[str, Any], str]:
    """
    Process user resume data to create and store embeddings.
    
    Args:
        resume_data: Resume data from request
        
    Returns:
        Tuple (success, result_data, message)
    """
    try:
        # Check input data
        account_data = resume_data.get("data", {})
        account_id = account_data.get("account_id", "")
        
        if not account_id:
            return False, {}, "Invalid account ID"
        
        # Convert to structured text
        structured_text = format_resume_data(resume_data)
        
        if not structured_text:
            return False, {}, "Could not create structured text from resume data"
        
        # Generate description
        description = generate_description(structured_text, "resume")
        
        if not description:
            return False, {}, "Could not generate description from structured text"
        
        # Generate embedding
        embedding = generate_embedding(description)
        
        if not embedding:
            return False, {}, "Could not generate embedding from description"
        
        # Prepare metadata
        first_name = account_data.get("first_name", "")
        last_name = account_data.get("last_name", "")
        full_name = f"{first_name} {last_name}".strip()
        created_at = account_data.get("created_at", datetime.now().isoformat())
        metadata = {
            "type": "resume",
            "name": full_name,
            "date": created_at
        }
        
        # Store in ChromaDB - Use consistent collection_name
        success, message = store_in_chromadb(
            collection_name="user_resumes",
            id=account_id,
            embedding=embedding,
            description=description,
            metadata=metadata
        )
        
        if not success:
            return False, {}, message
        
        # Return result with structured text included
        result = {
            "account_id": account_id,
            "description": description,
            "structured_text": structured_text,  # Include the structured text in the response
            "embedding_dimensions": len(embedding),
            "embedding": embedding,
            "metadata": metadata,
            "document": description
        }
        
        return True, result, "Resume embedding processed successfully"
    except Exception as e:
        logger.error(f"Error processing resume embedding: {e}")
        return False, {}, f"Error processing resume embedding: {e}"

class EmbeddingProcessor:
    def __init__(self):
        self.chroma_client = get_chroma_client()
        self.matrix_storage = get_db_matrix_storage()
        logger.info("Initialized EmbeddingProcessor with matrix storage")
        
    def process_and_store_embedding(self, 
                                  content: str, 
                                  entity_id: str, 
                                  entity_type: str,  # 'user' or 'job'
                                  collection_name: str,
                                  metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Process content, generate embedding và tự động tạo ma trận cá nhân hóa
        
        Args:
            content: Text content to embed
            entity_id: ID của user hoặc job
            entity_type: 'user' hoặc 'job'
            collection_name: Tên collection trong ChromaDB
            metadata: Metadata bổ sung
            
        Returns:
            Dict với thông tin embedding và ma trận đã tạo
        """
        try:
            # Generate embedding
            embedding_result = self._generate_embedding(content)
            if not embedding_result["success"]:
                return embedding_result
                
            embedding_vector = embedding_result["embedding"]
            dimension = len(embedding_vector)
            
            # Store embedding in ChromaDB
            chroma_result = self._store_in_chroma(
                embedding_vector, entity_id, collection_name, content, metadata
            )
            
            if not chroma_result["success"]:
                return chroma_result
            
            # Tự động tạo ma trận cá nhân hóa cho entity
            matrix_result = self._create_personalization_matrix(
                entity_id, entity_type, dimension, embedding_vector
            )
            
            return {
                "success": True,
                "entity_id": entity_id,
                "entity_type": entity_type,
                "embedding_dimension": dimension,
                "chroma_stored": chroma_result["success"],
                "matrix_created": matrix_result["success"],
                "matrix_info": matrix_result.get("info", {}),
                "message": f"Đã tạo embedding và ma trận cho {entity_type} {entity_id}"
            }
            
        except Exception as e:
            logger.error(f"Error processing embedding for {entity_type} {entity_id}: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _create_personalization_matrix(self, 
                                     entity_id: str, 
                                     entity_type: str, 
                                     dimension: int,
                                     entity_embedding: List[float]) -> Dict[str, Any]:
        """
        Tạo ma trận cá nhân hóa cho entity (user hoặc job)
        """
        try:
            # Kiểm tra xem đã có ma trận chưa
            existing_metadata = self.matrix_storage.get_entity_metadata(entity_id, entity_type)
            
            if existing_metadata:
                # Nếu đã có ma trận, reset về identity matrix
                logger.info(f"Resetting existing matrix for {entity_type} {entity_id}")
                identity_matrix = np.eye(dimension, dtype=np.float32)
                
                success, message = self.matrix_storage.save_entity_matrix(
                    identity_matrix, 
                    entity_id,
                    entity_type,
                    {
                        "reset_reason": "embedding_updated",
                        "embedding_dimension": dimension,
                        "created_at": existing_metadata.get("created_at"),
                        "reset_at": datetime.now().isoformat()
                    }
                )
                
                return {
                    "success": success,
                    "action": "reset",
                    "message": message,
                    "info": {"dimension": dimension, "entity_type": entity_type}
                }
            else:
                # Tạo ma trận mới
                if entity_type == "user":
                    # Cho user, tạo ma trận thông minh dựa trên job embeddings có sẵn
                    matrix, init_info = self._initialize_smart_user_matrix(dimension, entity_embedding)
                elif entity_type == "job":
                    # Cho job, tạo identity matrix
                    matrix = np.eye(dimension, dtype=np.float32)
                    init_info = {"strategy": "identity", "reason": "job_entity"}
                else:
                    # Default: identity matrix
                    matrix = np.eye(dimension, dtype=np.float32)
                    init_info = {"strategy": "identity", "reason": "unknown_entity_type"}
                
                success, message = self.matrix_storage.save_entity_matrix(
                    matrix, 
                    entity_id,
                    entity_type,
                    {
                        "embedding_dimension": dimension,
                        "initialization_info": init_info,
                        "created_at": datetime.now().isoformat()
                    }
                )
                
                return {
                    "success": success,
                    "action": "create",
                    "message": message,
                    "info": {
                        "dimension": dimension, 
                        "entity_type": entity_type,
                        "initialization": init_info
                    }
                }
                
        except Exception as e:
            logger.error(f"Error creating matrix for {entity_type} {entity_id}: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _initialize_smart_user_matrix(self, dimension: int, user_embedding: List[float]) -> Tuple[np.ndarray, Dict[str, Any]]:
        """
        Tạo ma trận thông minh cho user dựa trên job embeddings có sẵn
        """
        try:
            # Lấy job embeddings từ ChromaDB
            # Handle both client structures
            if hasattr(self.chroma_client, 'client'):
                collection = self.chroma_client.client.get_collection("job_descriptions")
            else:
                collection = self.chroma_client.get_collection("job_descriptions")
                
            job_data = collection.get(include=["embeddings", "metadatas"], limit=100)
            
            if job_data and job_data["embeddings"]:
                job_embeddings = np.array(job_data["embeddings"])
                job_metadatas = job_data.get("metadatas", [])
                
                # Sử dụng cold start algorithm
                matrix, init_info = initialize_user_matrix(
                    job_embeddings=job_embeddings,
                    resume_embeddings=np.array([user_embedding]),
                    job_metadata=job_metadatas,
                    strategy="clustering"
                )
                
                return matrix, init_info
            else:
                # Fallback to identity if no jobs available
                return np.eye(dimension, dtype=np.float32), {"strategy": "identity", "reason": "no_jobs_available"}
                
        except Exception as e:
            logger.warning(f"Failed to create smart matrix, using identity: {e}")
            return np.eye(dimension, dtype=np.float32), {"strategy": "identity", "reason": "error_fallback"}
    
    def _generate_embedding(self, content: str) -> Dict[str, Any]:
        """Generate embedding từ content"""
        try:
            embedding = generate_embedding(content)
            if not embedding:
                return {"success": False, "error": "Failed to generate embedding"}
            return {"success": True, "embedding": embedding}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _store_in_chroma(self, embedding: List[float], entity_id: str, 
                        collection_name: str, content: str, metadata: Dict) -> Dict[str, Any]:
        """Store embedding vào ChromaDB"""
        try:
            success, message = store_in_chromadb(collection_name, entity_id, embedding, content, metadata or {})
            return {"success": success, "message": message}
        except Exception as e:
            return {"success": False, "error": str(e)}