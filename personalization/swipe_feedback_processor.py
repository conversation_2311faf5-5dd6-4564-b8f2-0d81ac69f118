import logging
import numpy as np
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from collections import defaultdict
import threading
import time

from personalization.db_matrix_storage import get_db_matrix_storage
from personalization.matrix_update import update_user_matrix
from utils.chroma_db import get_chroma_client

logger = logging.getLogger(__name__)


class SwipeFeedbackProcessor:
    """
    Xử lý feedback từ việc swipe và cập nhật ma trận cá nhân hóa với batch processing thông minh
    """

    def __init__(self, batch_size: int = 5):
        self.matrix_storage = get_db_matrix_storage()
        self.chroma_client = get_chroma_client()
        self.batch_size = batch_size
        self.feedback_buffer = defaultdict(list)  # user_id -> list of feedbacks
        self.processing_queue = []  # Queue for background processing
        self.is_processing = False
        logger.info(f"Initialized SwipeFeedbackProcessor with batch_size={batch_size}")

    def add_feedback(self, user_id: str, item_id: str, item_embedding: np.ndarray,
                    feedback: int) -> Dict[str, Any]:
        """
        Thêm một feedback vào buffer

        Args:
            user_id: ID của người dùng
            item_id: ID của item (job/company)
            item_embedding: Vector embedding của item
            feedback: 1 (like), -1 (dislike), 0 (neutral)

        Returns:
            Dict[str, Any]: Thông tin về việc xử lý feedback
        """
        try:
            # Validate feedback value
            if feedback not in [-1, 0, 1]:
                return {
                    "success": False,
                    "error": "Invalid feedback value. Must be -1, 0, or 1"
                }

            # Validate embedding
            if not isinstance(item_embedding, np.ndarray):
                return {
                    "success": False,
                    "error": "item_embedding must be numpy array"
                }

            # Thêm feedback vào buffer
            feedback_data = {
                "item_id": item_id,
                "embedding": item_embedding,
                "feedback": feedback,
                "timestamp": datetime.now().isoformat()
            }

            self.feedback_buffer[user_id].append(feedback_data)

            logger.info(f"Đã thêm feedback cho user_id={user_id}, item_id={item_id}, feedback={feedback}")
            logger.info(f"Buffer hiện tại cho user {user_id}: {len(self.feedback_buffer[user_id])} feedbacks")

            # Kiểm tra xem có đủ feedback để cập nhật ma trận không
            should_update = len(self.feedback_buffer[user_id]) >= self.batch_size

            result = {
                "success": True,
                "message": "Đã thêm feedback thành công",
                "buffer_size": len(self.feedback_buffer[user_id]),
                "batch_size": self.batch_size,
                "should_update": should_update
            }

            # Nếu đủ feedback, tự động cập nhật ma trận
            if should_update:
                logger.info(f"Đủ {self.batch_size} feedbacks, bắt đầu xử lý batch cho user {user_id}")
                batch_result = self.process_batch_feedback(user_id)
                result["batch_processing"] = batch_result
                result["matrix_updated"] = batch_result.get("success", False)

            return result

        except Exception as e:
            logger.error(f"Error adding feedback for user {user_id}: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def process_batch_feedback(self, user_id: str, force: bool = False) -> Dict[str, Any]:
        """
        Xử lý batch feedback và cập nhật ma trận để đạt similarity mục tiêu

        Args:
            user_id: ID của người dùng
            force: Có ép buộc xử lý dù chưa đủ batch_size không

        Returns:
            Dict[str, Any]: Kết quả xử lý
        """
        try:
            feedbacks = self.feedback_buffer.get(user_id, [])

            if not feedbacks:
                return {
                    "success": False,
                    "message": "Không có feedback nào để xử lý"
                }

            if not force and len(feedbacks) < self.batch_size:
                return {
                    "success": False,
                    "message": f"Chưa đủ feedback để xử lý (có {len(feedbacks)}, cần {self.batch_size})"
                }

            logger.info(f"Bắt đầu xử lý batch feedback cho user_id={user_id}, số lượng: {len(feedbacks)}")

            # Phân tích feedback để điều chỉnh tham số
            positive_feedbacks = [f for f in feedbacks if f["feedback"] > 0]
            negative_feedbacks = [f for f in feedbacks if f["feedback"] < 0]
            
            logger.info(f"Feedback analysis: {len(positive_feedbacks)} likes, {len(negative_feedbacks)} dislikes")

            # Chuẩn bị dữ liệu
            item_ids = [f["item_id"] for f in feedbacks]
            embeddings = np.array([f["embedding"] for f in feedbacks], dtype=np.float32)
            feedback_values = [f["feedback"] for f in feedbacks]

            # Lấy ma trận hiện tại hoặc tạo mới
            embedding_dim = embeddings.shape[1]
            success, user_matrix = self.matrix_storage.load_user_matrix(user_id, dim=embedding_dim)

            if not success:
                logger.error(f"Không thể load ma trận cho user_id={user_id}: {user_matrix}")
                return {
                    "success": False,
                    "message": f"Không thể load ma trận: {user_matrix}"
                }

            # Điều chỉnh tham số dựa trên loại feedback
            learning_params = self._calculate_optimal_learning_params(positive_feedbacks, negative_feedbacks)
            
            logger.info(f"Using learning parameters: {learning_params}")

            # Cập nhật ma trận với multiple iterations để đạt similarity mục tiêu
            final_matrix, all_update_info = self._iterative_matrix_update(
                user_matrix, embeddings, feedback_values, item_ids, learning_params
            )

            # Lưu ma trận đã cập nhật
            extra_metadata = {
                "last_feedback_count": len(feedbacks),
                "last_feedback_time": datetime.now().isoformat(),
                "last_feedback_summary": {
                    "positive": len(positive_feedbacks),
                    "negative": len(negative_feedbacks),
                    "neutral": len(feedbacks) - len(positive_feedbacks) - len(negative_feedbacks)
                },
                "update_info": all_update_info,
                "processed_items": item_ids,
                "learning_params": learning_params
            }

            save_success, save_message = self.matrix_storage.save_user_matrix(
                final_matrix, user_id, extra_metadata
            )

            if not save_success:
                logger.error(f"Không thể lưu ma trận đã cập nhật cho user_id={user_id}: {save_message}")
                return {
                    "success": False,
                    "message": f"Không thể lưu ma trận: {save_message}"
                }

            # Xóa feedback đã xử lý khỏi buffer
            self.feedback_buffer[user_id] = []

            logger.info(f"Đã xử lý thành công batch feedback cho user_id={user_id}")

            return {
                "success": True,
                "message": "Đã cập nhật ma trận thành công với similarity mục tiêu",
                "processed_count": len(feedbacks),
                "feedback_summary": extra_metadata["last_feedback_summary"],
                "update_info": all_update_info,
                "matrix_shape": final_matrix.shape,
                "learning_params": learning_params
            }

        except Exception as e:
            logger.error(f"Lỗi khi xử lý batch feedback cho user_id={user_id}: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return {
                "success": False,
                "message": f"Lỗi khi xử lý batch feedback: {str(e)}"
            }

    def _calculate_optimal_learning_params(self, positive_feedbacks: List[Dict], 
                                         negative_feedbacks: List[Dict]) -> Dict[str, float]:
        """
        Tính toán tham số học tối ưu dựa trên loại feedback
        
        Mục tiêu: Đạt similarity 0.9 cho like items và 0.1 cho dislike items
        """
        total_feedbacks = len(positive_feedbacks) + len(negative_feedbacks)
        positive_ratio = len(positive_feedbacks) / max(total_feedbacks, 1)
        
        # Điều chỉnh learning rate dựa trên tỷ lệ positive/negative
        if positive_ratio > 0.7:  # Chủ yếu là likes
            learning_rate = 0.25
            l2_reg = 0.003
        elif positive_ratio < 0.3:  # Chủ yếu là dislikes  
            learning_rate = 0.20
            l2_reg = 0.005
        else:  # Mix cân bằng
            learning_rate = 0.30
            l2_reg = 0.004
        
        # Điều chỉnh dựa trên số lượng feedback
        if total_feedbacks >= self.batch_size:
            learning_rate *= 1.2  # Tăng learning rate khi có đủ data
        
        return {
            "learning_rate": learning_rate,
            "l2_reg": l2_reg,
            "shrinkage": 0.80,  # Giảm shrinkage để giữ lại nhiều thay đổi
            "spectral_clip": 8.0  # Cho phép eigenvalues lớn hơn
        }

    def _iterative_matrix_update(self, user_matrix: np.ndarray, embeddings: np.ndarray,
                               feedback_values: List[int], item_ids: List[str],
                               learning_params: Dict[str, float]) -> Tuple[np.ndarray, Dict[str, Any]]:
        """
        Cập nhật ma trận qua nhiều iterations để đạt similarity mục tiêu
        """
        current_matrix = user_matrix.copy()
        all_iterations_info = []
        
        # Target similarities
        TARGET_LIKE_SIMILARITY = 0.85  # Mục tiêu cho liked items
        TARGET_DISLIKE_SIMILARITY = 0.15  # Mục tiêu cho disliked items
        MAX_ITERATIONS = 3
        
        for iteration in range(MAX_ITERATIONS):
            logger.info(f"Matrix update iteration {iteration + 1}/{MAX_ITERATIONS}")
            
            # Cập nhật ma trận
            updated_matrix, iteration_info = update_user_matrix(
                user_matrix=current_matrix,
                vectors=embeddings,
                feedbacks=feedback_values,
                ids=item_ids,
                **learning_params
            )
            
            # Kiểm tra similarity hiện tại
            current_similarities = self._check_current_similarities(updated_matrix, embeddings, feedback_values)
            
            iteration_info["current_similarities"] = current_similarities
            iteration_info["iteration"] = iteration + 1
            all_iterations_info.append(iteration_info)
            
            logger.info(f"Iteration {iteration + 1} similarities: {current_similarities}")
            
            # Kiểm tra xem đã đạt mục tiêu chưa
            targets_met = self._check_similarity_targets(current_similarities, 
                                                       TARGET_LIKE_SIMILARITY, 
                                                       TARGET_DISLIKE_SIMILARITY)
            
            if targets_met:
                logger.info(f"Đạt similarity targets sau {iteration + 1} iterations")
                break
                
            # Điều chỉnh learning rate cho iteration tiếp theo
            if iteration < MAX_ITERATIONS - 1:
                learning_params["learning_rate"] *= 0.8  # Giảm dần learning rate
                
            current_matrix = updated_matrix
        
        return current_matrix, {
            "total_iterations": len(all_iterations_info),
            "iterations_info": all_iterations_info,
            "final_similarities": current_similarities
        }

    def _check_current_similarities(self, matrix: np.ndarray, embeddings: np.ndarray, 
                                  feedback_values: List[int]) -> Dict[str, float]:
        """
        Kiểm tra similarity hiện tại giữa user vector và item vectors
        """
        try:
            # Tạo user vector (có thể dùng trung bình của liked items hoặc vector đặc biệt)
            user_vector = np.mean(embeddings, axis=0).reshape(1, -1)
            
            # Transform user vector với ma trận
            transformed_user = np.dot(user_vector, matrix)
            
            # Tính similarity với từng item
            similarities = []
            like_similarities = []
            dislike_similarities = []
            
            for i, (embedding, feedback) in enumerate(zip(embeddings, feedback_values)):
                # Transform item embedding
                transformed_item = np.dot(embedding.reshape(1, -1), matrix)
                
                # Tính cosine similarity
                similarity = np.dot(transformed_user, transformed_item.T)[0, 0] / (
                    np.linalg.norm(transformed_user) * np.linalg.norm(transformed_item)
                )
                
                similarities.append(float(similarity))
                
                if feedback > 0:
                    like_similarities.append(float(similarity))
                elif feedback < 0:
                    dislike_similarities.append(float(similarity))
            
            return {
                "average_similarity": float(np.mean(similarities)),
                "like_similarities": like_similarities,
                "dislike_similarities": dislike_similarities,
                "average_like_similarity": float(np.mean(like_similarities)) if like_similarities else 0.0,
                "average_dislike_similarity": float(np.mean(dislike_similarities)) if dislike_similarities else 0.0
            }
            
        except Exception as e:
            logger.error(f"Error checking similarities: {e}")
            return {"error": str(e)}

    def _check_similarity_targets(self, similarities: Dict[str, float], 
                                target_like: float, target_dislike: float) -> bool:
        """
        Kiểm tra xem đã đạt similarity targets chưa
        """
        try:
            like_met = similarities.get("average_like_similarity", 0) >= target_like
            dislike_met = similarities.get("average_dislike_similarity", 1) <= target_dislike
            
            return like_met and dislike_met
            
        except Exception as e:
            logger.error(f"Error checking targets: {e}")
            return False

    def get_feedback_status(self, user_id: str) -> Dict[str, Any]:
        """
        Lấy thông tin về trạng thái feedback của người dùng

        Args:
            user_id: ID của người dùng

        Returns:
            Dict[str, Any]: Thông tin về trạng thái feedback
        """
        try:
            # Thông tin buffer
            buffer_size = len(self.feedback_buffer.get(user_id, []))

            # Thông tin ma trận từ database
            metadata = self.matrix_storage.get_user_metadata(user_id)

            return {
                "user_id": user_id,
                "buffer_size": buffer_size,
                "batch_size": self.batch_size,
                "ready_for_update": buffer_size >= self.batch_size,
                "matrix_metadata": metadata,
                "has_matrix": metadata is not None
            }

        except Exception as e:
            logger.error(f"Lỗi khi lấy feedback status cho user_id={user_id}: {str(e)}")
            return {
                "user_id": user_id,
                "error": str(e)
            }

    def clear_buffer(self, user_id: str) -> Dict[str, Any]:
        """
        Xóa buffer feedback của người dùng

        Args:
            user_id: ID của người dùng

        Returns:
            Dict[str, Any]: Kết quả xóa buffer
        """
        try:
            cleared_count = len(self.feedback_buffer.get(user_id, []))
            self.feedback_buffer[user_id] = []

            logger.info(f"Đã xóa {cleared_count} feedbacks trong buffer cho user_id={user_id}")

            return {
                "success": True,
                "message": f"Đã xóa {cleared_count} feedbacks trong buffer",
                "cleared_count": cleared_count
            }

        except Exception as e:
            logger.error(f"Lỗi khi xóa buffer cho user_id={user_id}: {str(e)}")
            return {
                "success": False,
                "message": f"Lỗi khi xóa buffer: {str(e)}"
            }


# Global instance
_feedback_processor = None

def get_swipe_feedback_processor(batch_size: int = 5):
    """Factory function để tạo SwipeFeedbackProcessor instance"""
    global _feedback_processor
    if _feedback_processor is None:
        _feedback_processor = SwipeFeedbackProcessor(batch_size=batch_size)
    return _feedback_processor
