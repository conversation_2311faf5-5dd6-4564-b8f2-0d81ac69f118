import numpy as np
import logging
import datetime
from typing import List, Dict, Any, Optional, Tuple, Union

from personalization.db_matrix_storage import get_db_matrix_storage
from personalization.similarity import update_user_matrix

logger = logging.getLogger(__name__)

class FeedbackProcessor:
    """X<PERSON> lý feedback từ người dùng và cập nhật ma trận người dùng"""

    def __init__(self, storage_dir="user_matrices"):
        """
        Khởi tạo FeedbackProcessor

        Args:
            storage_dir: Th<PERSON> mục lưu trữ ma trận người dùng (không sử dụng với database storage)
        """
        self.matrix_storage = get_db_matrix_storage()

    def process_feedback(
        self,
        user_id: str,
        item_ids: List[str],
        item_embeddings: np.ndarray,
        feedbacks: List[int],
        dimension: Optional[int] = None,
        learning_params: Optional[Dict[str, float]] = None
    ) -> <PERSON><PERSON>[bool, Dict[str, Any]]:
        """
        Xử lý feedback từ người dùng và cập nhật ma trận người dùng

        Args:
            user_id: ID của người dùng
            item_ids: Danh sách ID của các items
            item_embeddings: Ma trận embeddings của các items
            feedbacks: Danh sách giá trị feedback (+1/-1/0)
            dimension: Kích thước của embedding
            learning_params: Các tham số học (learning_rate, l2_reg, shrinkage, spectral_clip)

        Returns:
            Tuple[bool, Dict[str, Any]]: (Thành công hay không, Thông tin cập nhật)
        """
        # Kiểm tra đầu vào
        if len(item_ids) != len(feedbacks) or len(item_ids) != len(item_embeddings):
            error_msg = "Số lượng item_ids, item_embeddings và feedbacks phải bằng nhau"
            logger.error(error_msg)
            return False, {"error": error_msg}

        # Nếu không có dimension, lấy từ item_embeddings
        if dimension is None and len(item_embeddings) > 0:
            dimension = item_embeddings.shape[1]

        # Đảm bảo các giá trị feedback hợp lệ
        normalized_feedbacks = []
        for f in feedbacks:
            if f > 0:
                normalized_feedbacks.append(1)  # Thích
            elif f < 0:
                normalized_feedbacks.append(-1)  # Không thích
            else:
                normalized_feedbacks.append(0)  # Trung lập

        # Thiết lập các tham số học tối ưu cho visualization
        default_params = {
            "learning_rate": 2.0,   # Tăng learning rate để thay đổi rõ rệt hơn
            "l2_reg": 0.005,        # Giảm regularization để cho phép thay đổi lớn hơn
            "shrinkage": 0.8,       # Giảm shrinkage để giữ lại nhiều thay đổi hơn
            "spectral_clip": 8.0    # Tăng spectral clip để cho phép eigenvalues lớn hơn
        }

        if learning_params:
            params = {**default_params, **learning_params}
        else:
            params = default_params

        # Lấy ma trận người dùng hiện tại hoặc tạo mới nếu chưa có
        success, result = self.matrix_storage.load_user_matrix(user_id, dimension)

        if not success:
            logger.warning(f"Không thể nạp ma trận người dùng: {result}")
            if dimension is None:
                return False, {"error": "Không thể tạo ma trận mới vì không có dimension"}

            # Tạo ma trận mới (ma trận đơn vị)
            user_matrix = np.eye(dimension)
            logger.info(f"Đã tạo ma trận mới cho user_id={user_id}")
        else:
            user_matrix = result

        try:
            # Cập nhật ma trận
            updated_matrix, update_info = update_user_matrix(
                user_matrix=user_matrix,
                vectors=item_embeddings,
                feedbacks=normalized_feedbacks,
                ids=item_ids,
                learning_rate=params["learning_rate"],
                l2_reg=params["l2_reg"],
                shrinkage=params["shrinkage"],
                spectral_clip=params["spectral_clip"]
            )

            # Lưu ma trận đã cập nhật
            extra_metadata = {
                "last_feedback_count": len(item_ids),
                "last_feedback_time": datetime.datetime.now().isoformat(),
                "last_feedback_summary": {
                    "positive": sum(1 for f in normalized_feedbacks if f > 0),
                    "negative": sum(1 for f in normalized_feedbacks if f < 0),
                    "neutral": sum(1 for f in normalized_feedbacks if f == 0)
                },
                "last_update_params": params
            }

            success, message = self.matrix_storage.save_user_matrix(
                updated_matrix,
                user_id,
                extra_metadata
            )

            if not success:
                logger.error(f"Không thể lưu ma trận cập nhật: {message}")
                return False, {"error": f"Không thể lưu ma trận: {message}"}

            # Chuẩn bị kết quả trả về
            result = {
                "user_id": user_id,
                "success": True,
                "message": message,
                "matrix_updated": True,
                "update_info": update_info,
                "timestamp": datetime.datetime.now().isoformat()
            }

            return True, result

        except Exception as e:
            error_msg = f"Lỗi khi xử lý feedback: {str(e)}"
            logger.error(error_msg)
            return False, {"error": error_msg}

    def get_feedback_status(self, user_id: str) -> Dict[str, Any]:
        """
        Lấy thông tin về trạng thái feedback của người dùng

        Args:
            user_id: ID của người dùng

        Returns:
            Dict[str, Any]: Thông tin về trạng thái feedback
        """
        metadata = self.matrix_storage.get_user_metadata(user_id)

        if not metadata:
            return {
                "user_id": user_id,
                "has_matrix": False,
                "message": "Chưa có ma trận người dùng"
            }

        return {
            "user_id": user_id,
            "has_matrix": True,
            "update_count": metadata.get("update_count", 0),
            "created_at": metadata.get("created_at", ""),
            "last_updated": metadata.get("last_updated", ""),
            "last_feedback_summary": metadata.get("last_feedback_summary", {}),
            "matrix_shape": metadata.get("matrix_shape", [])
        }

    def reset_user_matrix(self, user_id: str, dimension: Optional[int] = None) -> Tuple[bool, Dict[str, Any]]:
        """
        Reset ma trận người dùng về ma trận đơn vị

        Args:
            user_id: ID của người dùng
            dimension: Kích thước ma trận mới

        Returns:
            Tuple[bool, Dict[str, Any]]: (Thành công hay không, Thông tin)
        """
        # Nếu không có dimension, thử lấy từ ma trận hiện tại
        if dimension is None:
            metadata = self.matrix_storage.get_user_metadata(user_id)
            matrix_shape = metadata.get("matrix_shape", [])

            if matrix_shape and len(matrix_shape) == 2:
                dimension = matrix_shape[0]
            else:
                return False, {"error": "Cần cung cấp dimension để reset ma trận"}

        # Tạo ma trận đơn vị mới
        new_matrix = np.eye(dimension)

        # Lưu ma trận mới
        success, message = self.matrix_storage.save_user_matrix(
            new_matrix,
            user_id,
            {
                "note": "Reset to identity matrix",
                "reset_time": datetime.datetime.now().isoformat()
            }
        )

        if success:
            return True, {
                "user_id": user_id,
                "dimension": dimension,
                "message": "Đã reset ma trận người dùng thành công"
            }
        else:
            return False, {"error": f"Không thể reset ma trận: {message}"}

# Đối tượng FeedbackProcessor toàn cục
_feedback_processor = None

def get_feedback_processor(storage_dir="user_matrices") -> FeedbackProcessor:
    """Lấy instance của FeedbackProcessor"""
    global _feedback_processor
    if _feedback_processor is None:
        _feedback_processor = FeedbackProcessor(storage_dir)
    return _feedback_processor
