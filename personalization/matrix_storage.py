import os
import numpy as np
import json
import datetime
import logging
from typing import Dict, Any, Optional, Tuple, List, Union

logger = logging.getLogger(__name__)

class UserMatrixStorage:
    _instance = None
    
    @classmethod
    def get_instance(cls, storage_dir: str = "user_matrices"):
        """Singleton pattern để đảm bảo chỉ có một instance của UserMatrixStorage"""
        if cls._instance is None:
            cls._instance = UserMatrixStorage(storage_dir)
        return cls._instance
    
    def __init__(self, storage_dir: str = "user_matrices"):
        """Khởi tạo storage system cho ma trận người dùng"""
        self.storage_dir = storage_dir
        os.makedirs(storage_dir, exist_ok=True)
        logger.info(f"Khởi tạo UserMatrixStorage tại: {os.path.abspath(storage_dir)}")
        
    def _get_metadata_path(self, user_id: str) -> str:
        """Đường dẫn đến file metadata"""
        return os.path.join(self.storage_dir, f"{user_id}_metadata.json")
        
    def _get_matrix_path(self, user_id: str) -> str:
        """Đường dẫn đến file ma trận"""
        return os.path.join(self.storage_dir, f"{user_id}_matrix.npy")
    
    def save_user_matrix(self, user_matrix: np.ndarray, user_id: str = "default", 
                         extra_metadata: Optional[Dict[str, Any]] = None) -> Tuple[bool, str]:
        """
        Lưu ma trận người dùng và metadata
        
        Args:
            user_matrix: Ma trận người dùng dạng numpy array
            user_id: ID của người dùng
            extra_metadata: Metadata bổ sung
            
        Returns:
            Tuple[bool, str]: (Thành công hay không, Thông báo)
        """
        try:
            # Kiểm tra ma trận hợp lệ
            if not isinstance(user_matrix, np.ndarray):
                return False, "Ma trận không phải là numpy array"
                
            if len(user_matrix.shape) != 2 or user_matrix.shape[0] != user_matrix.shape[1]:
                return False, f"Ma trận phải là ma trận vuông, nhận được shape {user_matrix.shape}"
            
            # Lưu ma trận
            matrix_path = self._get_matrix_path(user_id)
            np.save(matrix_path, user_matrix)
            
            # Chuẩn bị metadata
            now = datetime.datetime.now().isoformat()
            
            # Đọc metadata hiện có nếu có
            metadata = {}
            try:
                if os.path.exists(self._get_metadata_path(user_id)):
                    with open(self._get_metadata_path(user_id), 'r') as f:
                        metadata = json.load(f)
                    # Tăng số lần cập nhật
                    metadata['update_count'] = metadata.get('update_count', 0) + 1
                    metadata['last_updated'] = now
            except Exception as e:
                logger.warning(f"Không đọc được metadata hiện có cho user_id={user_id}: {str(e)}")
                # Nếu không đọc được, tạo metadata mới
                metadata = {
                    'created_at': now,
                    'last_updated': now,
                    'update_count': 1,
                    'matrix_shape': list(user_matrix.shape),
                    'matrix_version': '1.0'
                }
            
            # Thêm metadata bổ sung
            if extra_metadata:
                metadata.update(extra_metadata)
                
            # Lưu metadata
            with open(self._get_metadata_path(user_id), 'w') as f:
                json.dump(metadata, f, indent=2)
                
            logger.info(f"Đã lưu ma trận người dùng cho user_id={user_id}")
            return True, f"Đã lưu ma trận thành công cho user_id={user_id}"
        except Exception as e:
            error_msg = f"Lỗi khi lưu ma trận người dùng cho user_id={user_id}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    def load_user_matrix(self, user_id: str = "default", dim: Optional[int] = None) -> Tuple[bool, Union[np.ndarray, str]]:
        """
        Nạp ma trận người dùng từ storage
        
        Args:
            user_id: ID của người dùng
            dim: Kích thước ma trận mong muốn (nếu cần kiểm tra hoặc tạo mới)
            
        Returns:
            Tuple[bool, Union[np.ndarray, str]]: (Thành công hay không, Ma trận hoặc thông báo lỗi)
        """
        matrix_path = self._get_matrix_path(user_id)
        
        try:
            if os.path.exists(matrix_path):
                # Đọc ma trận
                user_matrix = np.load(matrix_path)
                
                # Kiểm tra shape nếu dim được chỉ định
                if dim is not None and user_matrix.shape[0] != dim:
                    logger.warning(f"Kích thước ma trận ({user_matrix.shape[0]}) khác với dimension yêu cầu ({dim})")
                    logger.info(f"Tạo ma trận mới cho user_id={user_id} với dim={dim}")
                    
                    # Tạo ma trận đơn vị mới
                    new_matrix = np.eye(dim)
                    
                    # Lưu ma trận mới
                    self.save_user_matrix(new_matrix, user_id, {
                        'note': f'Recreated due to dimension mismatch. Old: {user_matrix.shape[0]}, New: {dim}'
                    })
                    
                    return True, new_matrix
                
                logger.info(f"Đã nạp ma trận cho user_id={user_id}, shape={user_matrix.shape}")
                return True, user_matrix
            else:
                # Trường hợp không tìm thấy ma trận
                if dim is None:
                    return False, "Không tìm thấy ma trận và không có dimension để tạo mới"
                
                logger.info(f"Không tìm thấy ma trận cho user_id={user_id}, tạo ma trận mới với dim={dim}")
                
                # Tạo ma trận đơn vị mới
                new_matrix = np.eye(dim)
                
                # Lưu ma trận mới
                self.save_user_matrix(new_matrix, user_id, {
                    'note': 'Initial identity matrix'
                })
                
                return True, new_matrix
        except Exception as e:
            error_msg = f"Lỗi khi nạp ma trận người dùng cho user_id={user_id}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    def delete_user_matrix(self, user_id: str) -> Tuple[bool, str]:
        """
        Xóa ma trận người dùng
        
        Args:
            user_id: ID của người dùng
            
        Returns:
            Tuple[bool, str]: (Thành công hay không, Thông báo)
        """
        try:
            matrix_path = self._get_matrix_path(user_id)
            metadata_path = self._get_metadata_path(user_id)
            
            # Kiểm tra tồn tại
            if not os.path.exists(matrix_path) and not os.path.exists(metadata_path):
                return False, f"Không tồn tại ma trận cho user_id={user_id}"
                
            # Xóa các file
            if os.path.exists(matrix_path):
                os.remove(matrix_path)
            
            if os.path.exists(metadata_path):
                os.remove(metadata_path)
                
            logger.info(f"Đã xóa ma trận người dùng cho user_id={user_id}")
            return True, f"Đã xóa ma trận cho user_id={user_id}"
        except Exception as e:
            error_msg = f"Lỗi khi xóa ma trận người dùng cho user_id={user_id}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    def list_users(self) -> List[str]:
        """
        Liệt kê tất cả người dùng đã lưu ma trận
        
        Returns:
            List[str]: Danh sách user_id
        """
        users = set()
        try:
            for filename in os.listdir(self.storage_dir):
                if filename.endswith("_matrix.npy"):
                    users.add(filename.replace("_matrix.npy", ""))
            return list(users)
        except Exception as e:
            logger.error(f"Lỗi khi liệt kê users: {str(e)}")
            return []
    
    def get_user_metadata(self, user_id: str = "default") -> Dict[str, Any]:
        """
        Lấy metadata của người dùng
        
        Args:
            user_id: ID của người dùng
            
        Returns:
            Dict[str, Any]: Metadata
        """
        metadata_path = self._get_metadata_path(user_id)
        if os.path.exists(metadata_path):
            try:
                with open(metadata_path, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Lỗi khi đọc metadata cho user_id={user_id}: {str(e)}")
        return {}

# Hàm tiện ích để lấy instance
def get_matrix_storage(storage_dir: str = "user_matrices") -> UserMatrixStorage:
    """Lấy instance của UserMatrixStorage"""
    return UserMatrixStorage.get_instance(storage_dir)
