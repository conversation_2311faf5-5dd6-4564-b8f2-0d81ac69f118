import numpy as np
import logging
from typing import List, Dict, Any, Optional, Tuple, Union

logger = logging.getLogger(__name__)

def cosine_similarity(X: np.ndarray, Y: np.ndarray) -> np.ndarray:
    """
    Tính cosine similarity giữa hai tập vectors
    
    Args:
        X: np.ndarray (N×D) - Ma trận chứa N vectors với dimension D
        Y: np.ndarray (M×D) - <PERSON> trận chứa M vectors với dimension D
        
    Returns:
        np.ndarray: Ma trận similarity (N×M)
    """
    # Tính norm (độ dài) của các vectors trong X và Y
    X_norm = np.linalg.norm(X, axis=1, keepdims=True)
    Y_norm = np.linalg.norm(Y, axis=1, keepdims=True)
    
    # Tránh divide by zero
    X_norm = np.maximum(X_norm, 1e-8)
    Y_norm = np.maximum(Y_norm, 1e-8)
    
    # <PERSON><PERSON><PERSON> hóa X và Y
    X_normalized = X / X_norm
    Y_normalized = Y / Y_norm
    
    # Tính cosine similarity
    return np.dot(X_normalized, Y_normalized.T)

def personalized_similarity(
    vectors1: np.ndarray, 
    vectors2: np.ndarray, 
    user_matrix: np.ndarray = None
) -> np.ndarray:
    """
    Tính toán similarity được cá nhân hóa giữa hai tập vectors
    
    Args:
        vectors1: np.ndarray (N×D) - Ma trận chứa N vectors với dimension D
        vectors2: np.ndarray (M×D) - Ma trận chứa M vectors với dimension D
        user_matrix: np.ndarray (D×D) - Ma trận cá nhân hóa của người dùng
        
    Returns:
        np.ndarray: Ma trận similarity (N×M)
    """
    if user_matrix is None:
        # Nếu không có ma trận người dùng, sử dụng cosine similarity thông thường
        return cosine_similarity(vectors1, vectors2)
    
    try:
        # Kiểm tra kích thước ma trận
        if user_matrix.shape[0] != vectors1.shape[1] or user_matrix.shape[1] != vectors2.shape[1]:
            logger.warning(f"Kích thước ma trận không khớp: user_matrix={user_matrix.shape}, vectors1={vectors1.shape}, vectors2={vectors2.shape}")
            logger.warning("Sử dụng cosine similarity thông thường")
            return cosine_similarity(vectors1, vectors2)
        
        # Áp dụng ma trận người dùng cho vectors1
        vectors1_transformed = np.matmul(vectors1, user_matrix)
        
        # Tính similarity giữa vectors1_transformed và vectors2
        return cosine_similarity(vectors1_transformed, vectors2)
    except Exception as e:
        logger.error(f"Lỗi khi tính personalized similarity: {str(e)}")
        # Fallback: Sử dụng cosine similarity thông thường
        return cosine_similarity(vectors1, vectors2)

def update_user_matrix(
    user_matrix: np.ndarray,
    vectors: np.ndarray, 
    feedbacks: List[int],
    ids: List[str] = None,
    learning_rate: float = 0.1,
    l2_reg: float = 0.01,
    shrinkage: float = 0.005,
    spectral_clip: float = 10.0
) -> Tuple[np.ndarray, Dict[str, Any]]:
    """
    Cập nhật ma trận người dùng dựa trên feedback
    
    Args:
        user_matrix: Ma trận người dùng hiện tại (D×D)
        vectors: Các vectors (embedding) nhận feedback (B×D)
        feedbacks: Giá trị feedback cho mỗi vector (+1/-1)
        ids: ID của các vector nhận feedback
        learning_rate: Tốc độ học
        l2_reg: Hệ số regularization L2
        shrinkage: Hệ số kéo về ma trận đơn vị
        spectral_clip: Giới hạn eigenvalues
        
    Returns:
        Tuple[np.ndarray, Dict[str, Any]]: (Ma trận đã cập nhật, Thông tin cập nhật)
    """
    if len(vectors) != len(feedbacks):
        error_msg = f"Số lượng vectors ({len(vectors)}) khác với số lượng feedbacks ({len(feedbacks)})"
        logger.error(error_msg)
        raise ValueError(error_msg)
    
    if ids is None:
        ids = [f"item_{i}" for i in range(len(vectors))]
    
    D = user_matrix.shape[0]
    I = np.eye(D)  # Ma trận đơn vị
    
    update_info = {
        "num_updates": len(feedbacks),
        "feedback_summary": {},
        "norm_before": float(np.linalg.norm(user_matrix)),
        "updated_items": []
    }
    
    try:
        # Tóm tắt feedbacks
        update_info["feedback_summary"]["positive"] = sum(1 for f in feedbacks if f > 0)
        update_info["feedback_summary"]["negative"] = sum(1 for f in feedbacks if f < 0)
        update_info["feedback_summary"]["neutral"] = sum(1 for f in feedbacks if f == 0)
        
        # 1. Cập nhật dựa trên feedback
        for v, f, item_id in zip(vectors, feedbacks, ids):
            if f == 0:  # Bỏ qua feedback trung lập
                continue
                
            v = v / (np.linalg.norm(v) + 1e-8)  # Chuẩn hóa vector
            outer = np.outer(v, v)
            
            # Cập nhật ma trận với L2 regularization
            user_matrix = user_matrix + f * learning_rate * outer - learning_rate * l2_reg * (user_matrix - I)
            
            update_info["updated_items"].append({
                "id": item_id,
                "feedback": f
            })
        
        # 2. Shrinkage về ma trận đơn vị
        user_matrix = (1 - shrinkage) * user_matrix + shrinkage * I
        
        # 3. Đảm bảo ma trận đối xứng
        user_matrix = (user_matrix + user_matrix.T) / 2
        
        # 4. Spectral regularization (giới hạn eigenvalues)
        if spectral_clip > 0:
            eigenvalues, eigenvectors = np.linalg.eigh(user_matrix)
            # Giới hạn eigenvalues trong khoảng [1/spectral_clip, spectral_clip]
            eigenvalues = np.clip(eigenvalues, 1.0/spectral_clip, spectral_clip)
            # Tái tạo ma trận
            user_matrix = eigenvectors @ np.diag(eigenvalues) @ eigenvectors.T
        
        # Thêm thông tin về ma trận sau khi cập nhật
        update_info["norm_after"] = float(np.linalg.norm(user_matrix))
        update_info["change_magnitude"] = update_info["norm_after"] - update_info["norm_before"]
        
        return user_matrix, update_info
    
    except Exception as e:
        logger.error(f"Lỗi khi cập nhật ma trận người dùng: {str(e)}")
        # Trả về ma trận ban đầu trong trường hợp có lỗi
        return user_matrix, {"error": str(e)}

def analyze_matrix_properties(user_matrix: np.ndarray) -> Dict[str, Any]:
    """
    Phân tích các thuộc tính của ma trận người dùng
    
    Args:
        user_matrix: Ma trận người dùng (D×D)
        
    Returns:
        Dict[str, Any]: Các thuộc tính của ma trận
    """
    properties = {}
    
    try:
        # Kích thước
        properties["shape"] = list(user_matrix.shape)
        
        # Norm của ma trận
        properties["frobenius_norm"] = float(np.linalg.norm(user_matrix))
        
        # Tính eigenvalues
        eigenvalues = np.linalg.eigvalsh(user_matrix)
        properties["min_eigenvalue"] = float(np.min(eigenvalues))
        properties["max_eigenvalue"] = float(np.max(eigenvalues))
        properties["condition_number"] = float(properties["max_eigenvalue"] / max(properties["min_eigenvalue"], 1e-10))
        
        # Độ lệch so với ma trận đơn vị
        identity = np.eye(user_matrix.shape[0])
        properties["identity_deviation"] = float(np.linalg.norm(user_matrix - identity))
        
        # Kiểm tra tính chất PSD (Positive Semi-Definite)
        properties["is_psd"] = bool(np.all(eigenvalues >= -1e-10))
        
        # Kiểm tra tính đối xứng
        is_symmetric = np.allclose(user_matrix, user_matrix.T, rtol=1e-5, atol=1e-8)
        properties["is_symmetric"] = bool(is_symmetric)
        
        return properties
    except Exception as e:
        logger.error(f"Lỗi khi phân tích ma trận: {str(e)}")
        return {"error": str(e)}
