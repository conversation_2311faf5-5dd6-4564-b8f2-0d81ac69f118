import logging
import numpy as np
import pickle
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from sqlalchemy.exc import SQLAlchemyError

from models.personalization_matrix import PersonalizationMatrix
from utils import db

logger = logging.getLogger(__name__)


class DatabaseMatrixStorage:
    """
    Lưu trữ ma trận cá nhân hóa trong database thay vì file system
    """

    def __init__(self):
        self.db = db
        logger.info("Initialized DatabaseMatrixStorage")

    def save_entity_matrix(self, entity_matrix: np.ndarray, entity_id: str, entity_type: str,
                          extra_metadata: Optional[Dict[str, Any]] = None) -> Tuple[bool, str]:
        """
        Lưu ma trận cho entity (user hoặc job) vào database

        Args:
            entity_matrix: Ma trận dạng numpy array
            entity_id: ID của entity
            entity_type: Loại entity ('user' hoặc 'job')
            extra_metadata: Metadata bổ sung

        Returns:
            Tuple[bool, str]: (success, message)
        """
        try:
            # Rollback any existing transaction first
            try:
                self.db.session.rollback()
            except:
                pass

            # Serialize ma trận thành bytes
            matrix_bytes = pickle.dumps(entity_matrix)

            # Chuẩn bị metadata
            now = datetime.now().isoformat()
            metadata = {
                'matrix_shape': list(entity_matrix.shape),
                'matrix_version': '2.0',
                'storage_type': 'database',
                'created_at': now,
                'last_updated': now,
                'update_count': 1,
                'entity_type': entity_type
            }

            # Thêm metadata bổ sung
            if extra_metadata:
                metadata.update(extra_metadata)

            # Kiểm tra xem đã có ma trận cho entity này chưa
            existing_matrix = PersonalizationMatrix.query.filter_by(
                entity_id=entity_id,
                entity_type=entity_type
            ).first()

            if existing_matrix:
                # Cập nhật ma trận hiện có
                existing_matrix.matrix = matrix_bytes
                existing_matrix.dimension = entity_matrix.shape[0]

                # Cập nhật metadata
                old_metadata = existing_matrix.matrix_metadata or {}
                old_metadata['update_count'] = old_metadata.get('update_count', 0) + 1
                old_metadata['last_updated'] = now
                old_metadata.update(extra_metadata or {})
                existing_matrix.matrix_metadata = old_metadata

                logger.info(f"Cập nhật ma trận cho {entity_type}_id={entity_id}")
            else:
                # Tạo ma trận mới
                new_matrix = PersonalizationMatrix(
                    entity_id=entity_id,
                    entity_type=entity_type,
                    matrix_bytes=matrix_bytes,
                    dimension=entity_matrix.shape[0],
                    metadata=metadata
                )
                self.db.session.add(new_matrix)
                logger.info(f"Tạo ma trận mới cho {entity_type}_id={entity_id}")

            # Commit changes
            self.db.session.commit()

            logger.info(f"Đã lưu ma trận {entity_type} cho entity_id={entity_id} vào database")
            return True, f"Đã lưu ma trận thành công cho {entity_type}_id={entity_id}"

        except SQLAlchemyError as e:
            self.db.session.rollback()
            error_msg = f"Lỗi database khi lưu ma trận cho {entity_type}_id={entity_id}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
        except Exception as e:
            self.db.session.rollback()
            error_msg = f"Lỗi khi lưu ma trận {entity_type} cho entity_id={entity_id}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    def save_user_matrix(self, user_matrix: np.ndarray, user_id: str,
                         extra_metadata: Optional[Dict[str, Any]] = None) -> Tuple[bool, str]:
        """
        Legacy method - redirects to save_entity_matrix
        """
        return self.save_entity_matrix(user_matrix, user_id, 'user', extra_metadata)

    def load_user_matrix(self, user_id: str, dim: Optional[int] = None) -> Tuple[bool, np.ndarray]:
        """
        Nạp ma trận người dùng từ database

        Args:
            user_id: ID của người dùng
            dim: Kích thước ma trận mong muốn (nếu cần tạo mới)

        Returns:
            Tuple[bool, np.ndarray]: (success, matrix hoặc error_message)
        """
        try:
            # Rollback any existing transaction first
            try:
                self.db.session.rollback()
            except:
                pass

            # Tìm ma trận trong database - entity_id is now TEXT, no UUID conversion needed
            matrix_record = PersonalizationMatrix.query.filter_by(
                entity_id=user_id,  # Use string directly
                entity_type='user'
            ).first()

            if matrix_record:
                # Deserialize ma trận từ bytes
                user_matrix = pickle.loads(matrix_record.matrix)
                logger.info(f"Đã nạp ma trận cho user_id={user_id}, shape={user_matrix.shape}")
                return True, user_matrix
            else:
                # Trường hợp không tìm thấy ma trận
                if dim is None:
                    return False, "Không tìm thấy ma trận và không có dimension để tạo mới"

                logger.info(f"Không tìm thấy ma trận cho user_id={user_id}, tạo ma trận mới với dim={dim}")

                # Tạo ma trận đơn vị mới
                new_matrix = np.eye(dim, dtype=np.float32)

                # Lưu ma trận mới
                success, message = self.save_user_matrix(new_matrix, user_id, {
                    'note': 'Initial identity matrix',
                    'auto_created': True
                })

                if success:
                    return True, new_matrix
                else:
                    return False, message

        except Exception as e:
            error_msg = f"Lỗi khi nạp ma trận người dùng cho user_id={user_id}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    def get_entity_metadata(self, entity_id: str, entity_type: str = 'user') -> Optional[Dict[str, Any]]:
        """
        Lấy metadata của ma trận entity

        Args:
            entity_id: ID của entity
            entity_type: Loại entity ('user' hoặc 'job')

        Returns:
            Dict[str, Any]: Metadata hoặc None nếu không tìm thấy
        """
        try:
            matrix_record = PersonalizationMatrix.query.filter_by(
                entity_id=entity_id,
                entity_type=entity_type
            ).first()

            if matrix_record:
                return matrix_record.matrix_metadata
            else:
                return None

        except Exception as e:
            logger.error(f"Lỗi khi lấy metadata cho {entity_type}_id={entity_id}: {str(e)}")
            return None

    def get_user_metadata(self, user_id: str) -> Optional[Dict[str, Any]]:
        """
        Legacy method - redirects to get_entity_metadata for users
        """
        return self.get_entity_metadata(user_id, 'user')

    def list_users(self) -> List[str]:
        """
        Liệt kê tất cả user_id có ma trận

        Returns:
            List[str]: Danh sách user_id
        """
        try:
            user_matrices = PersonalizationMatrix.query.filter_by(entity_type='user').all()
            return [str(matrix.entity_id) for matrix in user_matrices]
        except Exception as e:
            logger.error(f"Lỗi khi liệt kê users: {str(e)}")
            return []

    def delete_user_matrix(self, user_id: str) -> Tuple[bool, str]:
        """
        Xóa ma trận của người dùng

        Args:
            user_id: ID của người dùng

        Returns:
            Tuple[bool, str]: (success, message)
        """
        try:
            matrix_record = PersonalizationMatrix.query.filter_by(
                entity_id=user_id,
                entity_type='user'
            ).first()

            if matrix_record:
                self.db.session.delete(matrix_record)
                self.db.session.commit()
                logger.info(f"Đã xóa ma trận cho user_id={user_id}")
                return True, f"Đã xóa ma trận cho user_id={user_id}"
            else:
                return False, f"Không tìm thấy ma trận cho user_id={user_id}"

        except SQLAlchemyError as e:
            self.db.session.rollback()
            error_msg = f"Lỗi database khi xóa ma trận cho user_id={user_id}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
        except Exception as e:
            self.db.session.rollback()
            error_msg = f"Lỗi khi xóa ma trận cho user_id={user_id}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg


def get_db_matrix_storage():
    """Factory function để tạo DatabaseMatrixStorage instance"""
    return DatabaseMatrixStorage()
