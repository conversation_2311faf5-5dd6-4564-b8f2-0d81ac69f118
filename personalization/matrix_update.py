import logging
import numpy as np
from typing import Dict, Any, <PERSON>, <PERSON><PERSON>
from sklearn.preprocessing import normalize

logger = logging.getLogger(__name__)


def update_user_matrix(
    user_matrix: np.ndarray,
    vectors: np.n<PERSON><PERSON>,
    feedbacks: List[int],
    ids: List[str],
    learning_rate: float = 0.01,
    l2_reg: float = 0.001,
    shrinkage: float = 0.95,
    spectral_clip: float = 2.0
) -> <PERSON><PERSON>[np.ndarray, Dict[str, Any]]:
    """
    Cập nhật ma trận cá nhân hóa dựa trên feedback
    
    Thuật toán:
    1. Tính gradient dựa trên feedback
    2. Áp dụng L2 regularization
    3. Cập nhật ma trận với learning rate
    4. Áp dụng shrinkage để tránh overfitting
    5. Clip eigenvalues để đảm bảo stability
    
    Args:
        user_matrix: Ma trận hiện tại (D x D)
        vectors: Ma trận vectors (N x D)
        feedbacks: List feedback values [-1, 0, 1]
        ids: List item IDs
        learning_rate: <PERSON><PERSON><PERSON> độ học
        l2_reg: L2 regularization coefficient
        shrinkage: Shrinkage factor
        spectral_clip: Giới hạn eigenvalues
        
    Returns:
        Tuple[np.ndarray, Dict]: (updated_matrix, update_info)
    """
    try:
        logger.info(f"Bắt đầu cập nhật ma trận với {len(feedbacks)} feedbacks")
        
        # Validate inputs
        if user_matrix.shape[0] != user_matrix.shape[1]:
            raise ValueError("User matrix phải là ma trận vuông")
        
        if vectors.shape[1] != user_matrix.shape[0]:
            raise ValueError("Dimension của vectors không khớp với ma trận")
        
        if len(feedbacks) != len(vectors) or len(feedbacks) != len(ids):
            raise ValueError("Số lượng feedbacks, vectors và ids phải bằng nhau")
        
        # Convert to numpy arrays
        user_matrix = user_matrix.astype(np.float32)
        vectors = vectors.astype(np.float32)
        feedbacks = np.array(feedbacks, dtype=np.float32)
        
        # Normalize vectors
        vectors_normalized = normalize(vectors, norm='l2', axis=1)
        
        # Tính gradient matrix
        gradient_matrix = compute_gradient_matrix(
            user_matrix, vectors_normalized, feedbacks
        )
        
        # Áp dụng L2 regularization
        l2_penalty = l2_reg * user_matrix
        gradient_matrix += l2_penalty
        
        # Cập nhật ma trận
        updated_matrix = user_matrix - learning_rate * gradient_matrix
        
        # Áp dụng shrinkage
        updated_matrix = shrinkage * updated_matrix + (1 - shrinkage) * np.eye(updated_matrix.shape[0])
        
        # Đảm bảo ma trận đối xứng
        updated_matrix = 0.5 * (updated_matrix + updated_matrix.T)
        
        # Clip eigenvalues để đảm bảo stability
        updated_matrix = clip_eigenvalues(updated_matrix, spectral_clip)
        
        # Tính toán thông tin cập nhật
        update_info = {
            "gradient_norm": float(np.linalg.norm(gradient_matrix)),
            "matrix_change_norm": float(np.linalg.norm(updated_matrix - user_matrix)),
            "learning_rate": learning_rate,
            "l2_reg": l2_reg,
            "shrinkage": shrinkage,
            "spectral_clip": spectral_clip,
            "feedback_stats": {
                "positive": int(np.sum(feedbacks > 0)),
                "negative": int(np.sum(feedbacks < 0)),
                "neutral": int(np.sum(feedbacks == 0))
            },
            "matrix_properties": analyze_matrix_properties(updated_matrix)
        }
        
        logger.info(f"Cập nhật ma trận thành công. Gradient norm: {update_info['gradient_norm']:.6f}")
        logger.info(f"Matrix change norm: {update_info['matrix_change_norm']:.6f}")
        
        return updated_matrix, update_info
        
    except Exception as e:
        logger.error(f"Lỗi khi cập nhật ma trận: {str(e)}")
        raise


def compute_gradient_matrix(
    user_matrix: np.ndarray,
    vectors: np.ndarray,
    feedbacks: np.ndarray
) -> np.ndarray:
    """
    Tính gradient matrix dựa trên feedback
    
    Gradient được tính theo công thức:
    ∇M = Σ feedback_i * (v_i * v_i^T - M * v_i * v_i^T * M)
    
    Args:
        user_matrix: Ma trận hiện tại (D x D)
        vectors: Ma trận vectors đã normalize (N x D)
        feedbacks: Array feedback values (N,)
        
    Returns:
        np.ndarray: Gradient matrix (D x D)
    """
    try:
        D = user_matrix.shape[0]
        gradient = np.zeros((D, D), dtype=np.float32)
        
        for i, (vector, feedback) in enumerate(zip(vectors, feedbacks)):
            if feedback == 0:
                continue  # Skip neutral feedback
            
            # Reshape vector to column vector
            v = vector.reshape(-1, 1)
            
            # Compute outer product v * v^T
            vvT = np.outer(v, v)
            
            # Compute transformed vector M * v
            Mv = user_matrix @ v
            
            # Compute M * v * v^T * M
            MvvTM = np.outer(Mv, Mv)
            
            # Add to gradient
            if feedback > 0:  # Positive feedback - move closer
                gradient += feedback * (vvT - MvvTM)
            else:  # Negative feedback - move away
                gradient += feedback * (MvvTM - vvT)
        
        return gradient
        
    except Exception as e:
        logger.error(f"Lỗi khi tính gradient matrix: {str(e)}")
        raise


def clip_eigenvalues(matrix: np.ndarray, max_eigenvalue: float) -> np.ndarray:
    """
    Clip eigenvalues của ma trận để đảm bảo stability
    
    Args:
        matrix: Ma trận cần clip
        max_eigenvalue: Giá trị eigenvalue tối đa
        
    Returns:
        np.ndarray: Ma trận với eigenvalues đã được clip
    """
    try:
        # Eigenvalue decomposition
        eigenvalues, eigenvectors = np.linalg.eigh(matrix)
        
        # Clip eigenvalues
        eigenvalues_clipped = np.clip(eigenvalues, -max_eigenvalue, max_eigenvalue)
        
        # Reconstruct matrix
        clipped_matrix = eigenvectors @ np.diag(eigenvalues_clipped) @ eigenvectors.T
        
        logger.debug(f"Eigenvalues before clipping: min={eigenvalues.min():.4f}, max={eigenvalues.max():.4f}")
        logger.debug(f"Eigenvalues after clipping: min={eigenvalues_clipped.min():.4f}, max={eigenvalues_clipped.max():.4f}")
        
        return clipped_matrix.astype(np.float32)
        
    except Exception as e:
        logger.error(f"Lỗi khi clip eigenvalues: {str(e)}")
        return matrix


def analyze_matrix_properties(matrix: np.ndarray) -> Dict[str, Any]:
    """
    Phân tích các thuộc tính của ma trận
    
    Args:
        matrix: Ma trận cần phân tích
        
    Returns:
        Dict: Thông tin về thuộc tính ma trận
    """
    try:
        eigenvalues = np.linalg.eigvals(matrix)
        
        properties = {
            "shape": list(matrix.shape),
            "frobenius_norm": float(np.linalg.norm(matrix, 'fro')),
            "spectral_norm": float(np.linalg.norm(matrix, 2)),
            "condition_number": float(np.linalg.cond(matrix)),
            "determinant": float(np.linalg.det(matrix)),
            "trace": float(np.trace(matrix)),
            "eigenvalues": {
                "min": float(eigenvalues.min()),
                "max": float(eigenvalues.max()),
                "mean": float(eigenvalues.mean()),
                "std": float(eigenvalues.std())
            },
            "is_symmetric": bool(np.allclose(matrix, matrix.T, atol=1e-6)),
            "is_positive_definite": bool(np.all(eigenvalues > 0))
        }
        
        return properties
        
    except Exception as e:
        logger.error(f"Lỗi khi phân tích ma trận: {str(e)}")
        return {"error": str(e)}


def create_identity_matrix(dimension: int) -> np.ndarray:
    """
    Tạo ma trận đơn vị
    
    Args:
        dimension: Kích thước ma trận
        
    Returns:
        np.ndarray: Ma trận đơn vị
    """
    return np.eye(dimension, dtype=np.float32)


def create_random_matrix(dimension: int, scale: float = 0.1) -> np.ndarray:
    """
    Tạo ma trận ngẫu nhiên đối xứng
    
    Args:
        dimension: Kích thước ma trận
        scale: Tỷ lệ của noise
        
    Returns:
        np.ndarray: Ma trận ngẫu nhiên đối xứng
    """
    # Tạo ma trận ngẫu nhiên
    random_matrix = np.random.normal(0, scale, (dimension, dimension))
    
    # Làm cho ma trận đối xứng
    symmetric_matrix = 0.5 * (random_matrix + random_matrix.T)
    
    # Thêm identity matrix để đảm bảo positive definite
    matrix = np.eye(dimension) + symmetric_matrix
    
    return matrix.astype(np.float32)
