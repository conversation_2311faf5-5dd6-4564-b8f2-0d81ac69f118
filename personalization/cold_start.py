import numpy as np
import logging
from typing import List, Dict, Any, Optional, Tuple, Union

logger = logging.getLogger(__name__)

def initialize_user_matrix(
    job_embeddings: np.ndarray,
    resume_embeddings: np.ndarray = None,
    job_metadata: List[Dict[str, Any]] = None,
    resume_metadata: List[Dict[str, Any]] = None,
    user_profile: Optional[Dict[str, Any]] = None,
    strategy: str = "default"
) -> <PERSON><PERSON>[np.ndarray, Dict[str, Any]]:
    """
    Khởi tạo ma trận người dùng thông minh cho người dùng mới
    
    Args:
        job_embeddings: <PERSON> trận embeddings của jobs
        resume_embeddings: <PERSON> trận embeddings của resumes (optional)
        job_metadata: Metadata của jobs (optional)
        resume_metadata: Metadata của resumes (optional)
        user_profile: Thông tin profile người dùng (optional)
        strategy: <PERSON><PERSON><PERSON> lượ<PERSON> khởi tạo ('default', 'popularity', 'domain', 'clustering')
        
    Returns:
        <PERSON><PERSON>[np.ndarray, Dict[str, Any]]: (Ma trận đư<PERSON> khởi tạo, <PERSON>h<PERSON><PERSON> tin khởi tạo)
    """
    # Đảm bảo ít nhất có job_embeddings
    if job_embeddings is None or len(job_embeddings) == 0:
        error_msg = "Không có dữ liệu embeddings để khởi tạo ma trận người dùng"
        logger.error(error_msg)
        raise ValueError(error_msg)
    
    # Lấy kích thước embedding
    D = job_embeddings.shape[1]
    
    # Thông tin về việc khởi tạo
    init_info = {
        "strategy": strategy,
        "dimension": D,
        "job_embeddings_count": len(job_embeddings)
    }
    
    if resume_embeddings is not None:
        init_info["resume_embeddings_count"] = len(resume_embeddings)
    
    # Chọn chiến lược khởi tạo
    try:
        if strategy == "popularity" and job_metadata is not None:
            if resume_embeddings is None:
                logger.warning("Chiến lược popularity cần resume_embeddings, chuyển sang sử dụng chiến lược default")
                return np.eye(D), {"strategy": "default", "reason": "Missing resume_embeddings"}
            
            matrix = _initialize_by_popularity(job_embeddings, resume_embeddings, job_metadata, D)
            init_info["details"] = "Khởi tạo dựa trên mức độ phổ biến của công việc"
            
        elif strategy == "domain" and user_profile is not None and job_metadata is not None:
            matrix = _initialize_by_domain(job_embeddings, job_metadata, user_profile, D)
            init_info["details"] = "Khởi tạo dựa trên lĩnh vực/ngành nghề của người dùng"
            init_info["user_domain"] = user_profile.get("domain", "")
            init_info["user_skills_count"] = len(user_profile.get("skills", []))
            
        elif strategy == "clustering":
            if resume_embeddings is None:
                logger.warning("Chiến lược clustering cần resume_embeddings, chuyển sang sử dụng chiến lược default")
                return np.eye(D), {"strategy": "default", "reason": "Missing resume_embeddings"}
                
            try:
                matrix = _initialize_by_clustering(job_embeddings, resume_embeddings, D)
                init_info["details"] = "Khởi tạo dựa trên clustering các embeddings"
            except ImportError:
                logger.warning("Không thể sử dụng chiến lược clustering do thiếu thư viện scikit-learn")
                matrix = np.eye(D)
                init_info["strategy"] = "default"
                init_info["details"] = "Fallback do thiếu thư viện scikit-learn"
        else:
            # Mặc định: ma trận đơn vị
            matrix = np.eye(D)
            init_info["strategy"] = "default"
            init_info["details"] = "Sử dụng ma trận đơn vị"
        
        # Phân tích ma trận đã tạo
        init_info["matrix_norm"] = float(np.linalg.norm(matrix))
        init_info["is_symmetric"] = bool(np.allclose(matrix, matrix.T, rtol=1e-5, atol=1e-8))
        
        eigenvalues = np.linalg.eigvalsh(matrix)
        init_info["min_eigenvalue"] = float(np.min(eigenvalues))
        init_info["max_eigenvalue"] = float(np.max(eigenvalues))
        
        return matrix, init_info
        
    except Exception as e:
        error_msg = f"Lỗi khi khởi tạo ma trận người dùng với strategy={strategy}: {str(e)}"
        logger.error(error_msg)
        
        # Trả về ma trận đơn vị trong trường hợp lỗi
        return np.eye(D), {
            "strategy": "default",
            "error": error_msg, 
            "dimension": D
        }

def _initialize_by_popularity(
    job_embeddings: np.ndarray, 
    resume_embeddings: np.ndarray,
    job_metadata: List[Dict[str, Any]],
    D: int,
    top_n: int = 20
) -> np.ndarray:
    """
    Khởi tạo ma trận dựa trên các job phổ biến
    
    Args:
        job_embeddings: Ma trận embeddings của jobs
        resume_embeddings: Ma trận embeddings của resumes
        job_metadata: Metadata của jobs
        D: Dimension của embedding
        top_n: Số lượng job phổ biến nhất để xem xét
        
    Returns:
        np.ndarray: Ma trận người dùng được khởi tạo
    """
    logger.info("Áp dụng chiến lược khởi tạo dựa trên mức độ phổ biến...")
    
    # Xác định job phổ biến (giả định có trường popularity hoặc view_count)
    job_popularity = []
    for i, meta in enumerate(job_metadata):
        if 'view_count' in meta:
            job_popularity.append((i, meta.get('view_count', 0)))
        elif 'popularity' in meta:
            job_popularity.append((i, meta.get('popularity', 0)))
        else:
            job_popularity.append((i, 0))
    
    # Sắp xếp theo popularity giảm dần và lấy top_n
    job_popularity.sort(key=lambda x: x[1], reverse=True)
    popular_indices = [idx for idx, _ in job_popularity[:top_n]]
    
    if not popular_indices:
        logger.warning("Không có thông tin phổ biến, sử dụng các job đầu tiên")
        popular_indices = list(range(min(top_n, len(job_metadata))))
    
    # Tính ma trận dựa trên các cặp job-resume phổ biến
    M = np.eye(D)
    weight = 0.01  # Trọng số ảnh hưởng
    
    # Tính similarity giữa các job phổ biến và tất cả resume
    for job_idx in popular_indices:
        job_vec = job_embeddings[job_idx]
        
        # Tính similarity với tất cả resume
        sims = np.dot(job_vec, resume_embeddings.T)
        norms_job = np.linalg.norm(job_vec)
        norms_resume = np.linalg.norm(resume_embeddings, axis=1)
        sims = sims / (norms_job * norms_resume + 1e-8)
        
        # Lấy resume có similarity cao
        top_resume_indices = np.argsort(sims)[::-1][:5]
        
        for res_idx in top_resume_indices:
            if sims[res_idx] > 0.6:  # Ngưỡng similarity cao
                # Tạo ma trận đặc trưng tương quan
                feature_mat = np.outer(job_vec, resume_embeddings[res_idx])
                
                # Đóng góp vào ma trận người dùng
                M += weight * feature_mat
    
    # Đảm bảo đối xứng và PSD
    M = (M + M.T) / 2
    eigenvalues, eigenvectors = np.linalg.eigh(M)
    eigenvalues = np.maximum(eigenvalues, 1e-6)  # Đảm bảo PSD
    M = eigenvectors @ np.diag(eigenvalues) @ eigenvectors.T
    
    return M

def _initialize_by_domain(
    job_embeddings: np.ndarray,
    job_metadata: List[Dict[str, Any]],
    user_profile: Dict[str, Any],
    D: int
) -> np.ndarray:
    """
    Khởi tạo ma trận dựa trên ngành nghề/lĩnh vực của người dùng
    
    Args:
        job_embeddings: Ma trận embeddings của jobs
        job_metadata: Metadata của jobs
        user_profile: Thông tin profile người dùng
        D: Dimension của embedding
        
    Returns:
        np.ndarray: Ma trận người dùng được khởi tạo
    """
    logger.info("Áp dụng chiến lược khởi tạo dựa trên lĩnh vực/ngành nghề...")
    
    # Lấy ngành nghề từ profile người dùng
    user_domain = user_profile.get('domain', '').lower()
    user_skills = [s.lower() for s in user_profile.get('skills', [])]
    
    if not user_domain and not user_skills:
        logger.warning("Không có thông tin lĩnh vực trong profile người dùng, sử dụng ma trận đơn vị")
        return np.eye(D)
    
    # Tìm các job liên quan đến domain hoặc skills của người dùng
    relevant_indices = []
    for i, meta in enumerate(job_metadata):
        job_domain = meta.get('domain', '').lower()
        job_skills = [s.lower() for s in meta.get('skills', [])]
        
        # Kiểm tra nếu domain khớp
        if user_domain and user_domain in job_domain:
            relevant_indices.append(i)
            continue
            
        # Kiểm tra nếu có skill khớp
        if user_skills and job_skills:
            if any(skill in job_skills for skill in user_skills):
                relevant_indices.append(i)
    
    # Nếu không tìm thấy job liên quan, trả về ma trận đơn vị
    if not relevant_indices:
        logger.warning("Không tìm thấy job phù hợp với lĩnh vực/kỹ năng của người dùng")
        return np.eye(D)
    
    logger.info(f"Tìm thấy {len(relevant_indices)} job liên quan đến lĩnh vực của người dùng")
    
    # Tính ma trận dựa trên các job liên quan
    relevant_embeddings = job_embeddings[relevant_indices]
    
    # Tính covariance matrix của các job liên quan
    M = np.eye(D)
    if len(relevant_indices) > 1:  # Cần ít nhất 2 vector để tính covariance
        cov = np.cov(relevant_embeddings, rowvar=False)
        
        # Trộn với identity matrix để đảm bảo ổn định
        blend_factor = 0.3
        M = (1 - blend_factor) * M + blend_factor * cov
    else:
        # Nếu chỉ có 1 job liên quan, sử dụng outer product
        job_vec = relevant_embeddings[0]
        M += 0.1 * np.outer(job_vec, job_vec)
    
    # Đảm bảo ma trận đối xứng và PSD
    M = (M + M.T) / 2
    eigenvalues, eigenvectors = np.linalg.eigh(M)
    eigenvalues = np.maximum(eigenvalues, 1e-6)
    M = eigenvectors @ np.diag(eigenvalues) @ eigenvectors.T
    
    return M

def _initialize_by_clustering(
    job_embeddings: np.ndarray,
    resume_embeddings: np.ndarray,
    D: int,
    n_clusters: int = 5
) -> np.ndarray:
    """
    Khởi tạo ma trận dựa trên clustering các embeddings
    
    Args:
        job_embeddings: Ma trận embeddings của jobs
        resume_embeddings: Ma trận embeddings của resumes
        D: Dimension của embedding
        n_clusters: Số lượng clusters mong muốn
        
    Returns:
        np.ndarray: Ma trận người dùng được khởi tạo
    """
    logger.info("Áp dụng chiến lược khởi tạo dựa trên clustering...")
    
    # Import thuật toán clustering
    from sklearn.cluster import KMeans
    
    # Kết hợp job và resume embeddings
    all_embeddings = np.vstack([job_embeddings, resume_embeddings])
    
    # Giới hạn số lượng mẫu nếu quá lớn
    max_samples = 1000
    if all_embeddings.shape[0] > max_samples:
        indices = np.random.choice(all_embeddings.shape[0], max_samples, replace=False)
        all_embeddings_sample = all_embeddings[indices]
    else:
        all_embeddings_sample = all_embeddings
    
    # Điều chỉnh số lượng clusters
    actual_n_clusters = min(n_clusters, all_embeddings_sample.shape[0] // 5)
    if actual_n_clusters < 2:
        actual_n_clusters = 2
    
    # Chạy KMeans clustering
    kmeans = KMeans(n_clusters=actual_n_clusters, random_state=42, n_init=10)
    kmeans.fit(all_embeddings_sample)
    
    # Lấy centers của các cluster
    centers = kmeans.cluster_centers_
    
    # Tạo ma trận ban đầu là đơn vị
    M = np.eye(D)
    
    # Trọng số cho các centers
    weight = 0.05
    
    # Tạo ma trận dựa trên centers
    for i in range(actual_n_clusters):
        center = centers[i]
        for j in range(i, actual_n_clusters):
            other = centers[j]
            
            # Tính covariance between centers
            if i == j:
                # Tự covariance (outer product)
                M += weight * np.outer(center, center)
            else:
                # Cross-covariance
                M += 0.5 * weight * (np.outer(center, other) + np.outer(other, center))
    
    # Chuẩn hóa
    M = M / actual_n_clusters
    
    # Đảm bảo đối xứng và PSD
    M = (M + M.T) / 2
    eigenvalues, eigenvectors = np.linalg.eigh(M)
    eigenvalues = np.maximum(eigenvalues, 1e-6)
    M = eigenvectors @ np.diag(eigenvalues) @ eigenvectors.T
    
    return M

def analyze_user_matrix(user_matrix: np.ndarray) -> Dict[str, Any]:
    """
    Phân tích ma trận người dùng để hiểu các thuộc tính/đặc trưng của nó
    
    Args:
        user_matrix: Ma trận người dùng (D×D)
        
    Returns:
        Dict[str, Any]: Các thuộc tính của ma trận
    """
    try:
        properties = {}
        D = user_matrix.shape[0]
        
        # Kích thước
        properties["dimension"] = D
        
        # Kiểm tra tính đối xứng
        is_symmetric = np.allclose(user_matrix, user_matrix.T, rtol=1e-5, atol=1e-8)
        properties["is_symmetric"] = bool(is_symmetric)
        
        if not is_symmetric:
            logger.warning("Ma trận không đối xứng, điều này có thể gây ra vấn đề")
            # Đảm bảo đối xứng
            user_matrix = (user_matrix + user_matrix.T) / 2
        
        # Tính eigenvalues
        eigenvalues = np.linalg.eigvalsh(user_matrix)
        properties["min_eigenvalue"] = float(np.min(eigenvalues))
        properties["max_eigenvalue"] = float(np.max(eigenvalues))
        
        # Condition number (tỉ lệ eigenvalue lớn nhất/nhỏ nhất)
        eps = 1e-10  # Tránh division by zero
        condition_number = properties["max_eigenvalue"] / max(properties["min_eigenvalue"], eps)
        properties["condition_number"] = float(condition_number)
        
        # Kiểm tra PSD (Positive Semi-Definite)
        properties["is_psd"] = bool(np.all(eigenvalues >= -1e-8))
        
        if not properties["is_psd"]:
            logger.warning("Ma trận không dương bán xác định (PSD)")
        
        # So sánh với ma trận đơn vị
        identity = np.eye(D)
        properties["identity_deviation"] = float(np.linalg.norm(user_matrix - identity))
        properties["frobenius_norm"] = float(np.linalg.norm(user_matrix))
        
        # Thống kê eigenvalues
        properties["eigenvalue_mean"] = float(np.mean(eigenvalues))
        properties["eigenvalue_std"] = float(np.std(eigenvalues))
        
        if condition_number > 1000:
            logger.warning(f"Ma trận có condition number cao ({condition_number}), điều này có thể gây ra vấn đề số học")
        
        return properties
        
    except Exception as e:
        logger.error(f"Lỗi khi phân tích ma trận: {str(e)}")
        return {"error": str(e)}
