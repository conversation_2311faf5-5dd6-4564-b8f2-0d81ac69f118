import os
from flask import Blueprint, request
from extract.account import extract_and_insert_accounts
from extract.company import extract_and_insert_companies
from extract.position import extract_and_insert_positions
from utils import setup_logger
from utils.environment import Env
from utils.response import AppResponse
from extract.user import extract_and_insert_user, reset_user_data
from models.constant import Constant

extract_bp = Blueprint("extract", __name__, url_prefix="/api/v1/extract")


@extract_bp.route("/company", methods=["POST"])
def extract_data():
    """
    Extracts data from a CSV file and inserts it into the database.
    """
    body = request.get_json()
    reset = body.get("reset", False)
    flask_key = body.get("key", "")
    if flask_key != Env.FLASK_PASSWORD:
        return AppResponse.bad_request(message="Forbidden", status_code=403)

    # Path to the CSV file
    csv_file_path = os.path.join(
        os.getcwd(), "data", "excel", "job_results_simplified_1745048075(in).csv"
    )
    if not csv_file_path or not os.path.exists(csv_file_path):
        return AppResponse.bad_request(message="CSV file not found", status_code=400)

    try:
        logger = setup_logger()
        logger.info("Start extracting data...")

        company_account_map = extract_and_insert_accounts(csv_file_path, reset=reset)
        extract_and_insert_companies(csv_file_path, company_account_map, reset=reset)
        extract_and_insert_positions(csv_file_path, company_account_map, reset=reset)

        logger.info("Finished extracting data.")
        return AppResponse.success_with_message(message="Data extracted and inserted successfully!")
    except Exception as error:
        logger.error(f"Error during data extraction: {error}")
        return AppResponse.server_error(error=error)

@extract_bp.route("/user", methods=["POST"])
def extract_user_data():
    """
    Extracts user data from a CSV file and inserts it into the database.
    """
    body = request.get_json()
    reset = body.get("reset", False)
    flask_key = body.get("key", "")
    if flask_key != Env.FLASK_PASSWORD:
        return AppResponse.bad_request(message="Forbidden", status_code=403)

    try:
        logger = setup_logger()
        logger.info("Start extracting user data...")

        # Fetch the user role ID from the constants table
        USER_ROLE = Constant.query.filter_by(constant_name="User").first()
        if not USER_ROLE:
            raise ValueError("USER_ROLE not found in constants table.")
        
        reset_user_data(reset=reset, userConstant=USER_ROLE)

        json_folder_path = os.path.join(os.getcwd(), "data", "json", "parsed_output")
        if not os.path.exists(json_folder_path) or not os.path.isdir(json_folder_path):
            return AppResponse.bad_request(message="JSON folder not found", status_code=400)

        for json_file in os.listdir(json_folder_path):
            if json_file.endswith(".json"):
                json_file_path = os.path.join(json_folder_path, json_file)
                logger.info(f"Processing file: {json_file_path}")
                extract_and_insert_user(parsed_json_path=json_file_path, reset=reset, userConstant=USER_ROLE)

        logger.info("Finished extracting user data.")
        return AppResponse.success_with_message(message="User data extracted and inserted successfully!")
    except Exception as error:
        logger.error(f"Error during user data extraction: {error}")
        return AppResponse.server_error(error=error)