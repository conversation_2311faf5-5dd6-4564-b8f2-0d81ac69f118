import csv
from datetime import datetime

from models.application_position import ApplicationPosition
from models.application_skill import ApplicationSkill
from utils import get_instance, setup_logger

_, db = get_instance()


def extract_and_insert_positions(csv_file_path: str, company_account_map: dict, reset: bool = False) -> None:
    """
    Extracts job-related data from a CSV file and inserts it into the application_positions table.

    Args:
        csv_file_path (str): Path to the CSV file containing job data.
        company_account_map (dict): A dictionary mapping company names to their corresponding account IDs.
        reset (bool): If True, deletes all existing job positions before inserting new ones.
    """
    logger = setup_logger()
    try:
        logger.info(f"Starting job position data extraction from {csv_file_path}...")

        if reset:
            logger.info("Reset is True. Deleting all existing job positions...")
            ApplicationPosition.query.delete()
            ApplicationSkill.query.delete()  # Delete related skills as well
            db.session.commit()
            logger.info("All existing job positions have been deleted.")

        # Open the CSV file
        with open(csv_file_path, mode="r", encoding="utf-8") as file:
            reader = csv.DictReader(file)

            for row in reader:
                company_name = row.get("company_name", "").strip()
                if not company_name or not row.get("job_title"):
                    logger.warning(f"Skipping row due to missing company or job title: {str(row)[:100]}")
                    continue

                # Get the account_id for the company
                account_id = company_account_map.get(company_name)
                if not account_id:
                    logger.warning(f"No account_id found for company '{company_name}'. Skipping...")
                    continue

                # Combine salary fields into a single string
                salary = row.get("salary_range", "").strip()

                # Truncate the description to 10,000 characters with ellipsis if it exceeds the limit
                description = row.get("job_description", "").strip()
                if len(description) > 10000:
                    logger.warning(f"Description for job '{row['job_title']}' exceeds 10,000 characters. Truncating...")
                    description = description[:9997] + "..."

                # Create a new ApplicationPosition record
                application_position = ApplicationPosition(
                    account_id=account_id,
                    apply_position_title=row["job_title"],
                    salary=salary,
                    description=description,
                )
                db.session.add(application_position)
                db.session.flush()  # Flush to get the ID of the newly created ApplicationPosition

                # Insert ApplicationSkill records
                skills = row.get("skills", "").split(",")
                for skill in skills:
                    skill_name = skill.strip()
                    if skill_name:  # Skip empty skill names
                        application_skill = ApplicationSkill(
                            application_position_id=application_position.id,
                            skill_name=skill_name,
                            description=None,  # Add logic to extract skill-specific descriptions if needed
                        )
                        db.session.add(application_skill)

        # Commit all changes to the database
        db.session.commit()
        logger.info("Finished extracting and inserting job positions and skills from CSV.")
    except Exception as error:
        db.session.rollback()
        logger.error(f"Error while extracting and inserting job positions: {error}")
