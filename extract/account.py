import csv
from uuid import uuid4

from models.account import Account
from models.constant import Constant
from utils import fake_phone_numbers, get_instance, setup_logger
from utils.environment import Env

_, db = get_instance()


def extract_and_insert_accounts(csv_file_path: str, reset: bool = False) -> dict:
    """
    Extracts account data from a CSV file and inserts it into the database.

    Args:
        csv_file_path (str): Path to the CSV file containing account data.
        reset (bool): If True, deletes all existing accounts with the "Company" role before inserting new ones.

    Returns:
        dict: A dictionary mapping company names to their corresponding account IDs.
    """
    logger = setup_logger()
    company_account_map = {}

    try:
        logger.info(f"Starting account data extraction from {csv_file_path}...")

        # Read the company role from the constants table
        COMPANY_ROLE = Constant.query.filter_by(constant_name="Company").first()
        if not COMPANY_ROLE:
            raise Exception("Company role not found")

        if reset:
            logger.info("Reset is True. Deleting all accounts with system role 'Company'...")
            Account.query.filter_by(system_role=COMPANY_ROLE.constant_id).delete()
            db.session.commit()
            logger.info("All accounts with system role 'Company' have been deleted.")

        # Open the CSV file
        with open(csv_file_path, mode="r", encoding="utf-8") as file:
            reader = csv.DictReader(file)

            for row in reader:
                company_name = row.get("company_name", "").strip()
                if not company_name:
                    continue

                # Check if the account already exists
                existing_account = company_account_map.get(company_name)
                if existing_account:
                    logger.info(f"Account for company '{company_name}' already exists. Skipping...")
                    continue

                # Create a new account
                account = Account(
                    address=row.get("company_location", "N/A"),
                    email=f"{company_name.replace(' ', '').lower()}@example.com",
                    password=Env.DEFAULT_PASSWORD,
                    phone_number=fake_phone_numbers(),
                    refresh_token=uuid4().hex,
                    system_role=COMPANY_ROLE.constant_id,
                    avatar=row.get("company_logo", "N/A")
                )
                db.session.add(account)
                company_account_map[company_name] = account.account_id

        # Commit all changes to the database
        db.session.commit()
        logger.info("Finished extracting and inserting accounts from CSV.")
        return company_account_map
    except Exception as error:
        db.session.rollback()
        logger.error(f"Error while extracting and inserting accounts: {error}")
        return {}
