import csv
from datetime import datetime
from uuid import uuid4

from models.company import Company
from utils import get_instance, setup_logger

_, db = get_instance()


def extract_and_insert_companies(csv_file_path: str, company_account_map: dict, reset: bool = False) -> None:
    """
    Extracts company data from a CSV file and inserts it into the database.

    Args:
        csv_file_path (str): Path to the CSV file containing company data.
        company_account_map (dict): A dictionary mapping company names to their corresponding account IDs.
        reset (bool): If True, deletes all existing companies before inserting new ones.
    """

    logger = setup_logger()
    dict = {}

    try:
        logger.info(f"Starting company data extraction from {csv_file_path}...")

        if reset:
            logger.info("Reset is True. Deleting all existing companies...")
            Company.query.delete()
            db.session.commit()
            logger.info("All companies have been deleted.")

        # Open the CSV file
        with open(csv_file_path, mode="r", encoding="utf-8") as file:
            reader = csv.DictReader(file)

            for row in reader:
                company_name = row.get("company_name", "").strip()

                account_id = company_account_map.get(company_name)
                if not account_id:
                    logger.warning(f"No account_id found for company '{row.get('company_name', '').strip()}'. Skipping...")
                    continue

                # Check if the company already exists
                if dict.get(company_name):
                    logger.info(f"Company '{company_name}' already exists. Skipping...")
                    continue

                # Create a new company record
                company = Company(
                    account_id=account_id,
                    company_name=company_name,
                    company_url=row.get("company_url", "N/A"),
                    description=row.get("company_description", ""),
                    others=None,
                    established_date=datetime.now(),
                )
                db.session.add(company)
                dict[company_name] = True

        # Commit all changes to the database
        db.session.commit()
        logger.info("Finished extracting and inserting companies from CSV.")
    except Exception as error:
        db.session.rollback()
        logger.error(f"Error while extracting and inserting companies: {error}")
