import json
from datetime import datetime
from uuid import uuid4

from models.account import Account
from models.constant import Constant
from models.languages import Language
from models.user import User
from models.user_award import UserAward
from models.user_education import UserEducation
from models.user_experience import UserExperience
from models.application_position import ApplicationPosition
from models.application_skill import ApplicationSkill
from utils import get_instance, setup_logger, fake_phone_numbers
from utils.environment import Env

_, db = get_instance()

def parse_date(date_str, field_name):
    """
    Validates and parses a date string.

    Args:
        date_str (str): The date string to parse.
        field_name (str): The name of the field being parsed (for logging purposes).

    Returns:
        datetime or None: The parsed date, or None if the date is invalid.
    """
    if not date_str:
        return None
    try:
        return datetime.strptime(date_str, "%Y-%m-%d")
    except ValueError:
        logger = setup_logger()
        logger.warning(f"Invalid date format for field '{field_name}': {date_str}. Skipping...")
        return None


def extract_and_insert_user(parsed_json_path: str, reset: bool = False, userConstant: Constant = None) -> None:
    """
    Extracts user-related data from a parsed JSON file and inserts it into the database.

    Args:
        parsed_json_path (str): Path to the parsed JSON file containing user data.
        reset (bool): If True, deletes all existing user-related data before inserting new ones.
    """
    logger = setup_logger()

    try:
        logger.info(f"Starting user data extraction from {parsed_json_path}...")
        
        # Load the parsed JSON file
        with open(parsed_json_path, mode="r", encoding="utf-8") as file:
            user_data = json.load(file)

        if user_data.get("email") is None or user_data.get("email").strip() == "":
            logger.warning("Email is missing in the user data. Skipping this entry.")
            return

        # Create or update the Account
        account = Account(
            address=user_data.get("address") or "",
            email=user_data.get("email"),
            password=Env.DEFAULT_PASSWORD,
            phone_number=user_data.get("phone_number") or fake_phone_numbers(),
            refresh_token=uuid4().hex,
            system_role=userConstant.constant_id,
            avatar=None,
        )
        db.session.add(account)
        db.session.commit()

        # Create the User
        try:
            raw_summary_introduction = user_data.get("summary_introduction") or ""
            summary_introduction = (raw_summary_introduction[:997] + '...') if len(raw_summary_introduction) > 1000 else raw_summary_introduction
            user = User(
                account_id=account.account_id,
                date_of_birth=parse_date(user_data.get("date_of_birth"), "date_of_birth"),
                first_name=user_data.get("first_name") or "",
                last_name=user_data.get("last_name") or "",
                gender=user_data.get("gender") or True,
                social_media_link=user_data.get("social_media_link") or [],
                summary_introduction=summary_introduction,
            )
            db.session.add(user)
            db.session.commit()
        except Exception as error:
            db.session.rollback()
            logger.error(f"Error while creating User: {error}")

        # Insert UserEducation records
        try:
            for education in user_data.get("educations") or []:
                user_education = UserEducation(
                    account_id=account.account_id,
                    cpa=float(education.get("cpa")) if education.get("cpa") is not None else 0.0,
                    study_place=education.get("study_place") or "",
                    study_start_time=parse_date(education.get("study_start_time"), "study_start_time"),
                    study_end_time=parse_date(education.get("study_end_time"), "study_end_time"),
                    majority=education.get("majority") or "",
                    is_university=True,
                    description=None,
                )
                db.session.add(user_education)
            db.session.commit()
        except Exception as error:
            db.session.rollback()
            logger.error(f"Error while inserting UserEducation records: {error}")

        # Insert UserExperience records
        try:
            for experience in user_data.get("experiences") or []:
                user_experience = UserExperience(
                    account_id=account.account_id,
                    experience_start_time=parse_date(experience.get("experience_start_time"), "experience_start_time"),
                    experience_end_time=parse_date(experience.get("experience_end_time"), "experience_end_time"),
                    position=experience.get("position") or "",
                    work_place=experience.get("work_place") or "",
                    description=None,
                    experience_title=experience.get("position") or "Experience",
                )
                db.session.add(user_experience)
            db.session.commit()
        except Exception as error:
            db.session.rollback()
            logger.error(f"Error while inserting UserExperience records: {error}")

        # Insert UserAward records
        try:
            for award in user_data.get("awards") or []:
                user_award = UserAward(
                    account_id=account.account_id,
                    certificate_name=award.get("certificate_name") or "",
                    certificate_time=parse_date(award.get("certificate_time"), "certificate_time"),
                    description=award.get("description") or "",
                )
                db.session.add(user_award)
            db.session.commit()
        except Exception as error:
            db.session.rollback()
            logger.error(f"Error while inserting UserAward records: {error}")

        # Insert Language records
        try:
            for language in user_data.get("languages") or []:
                user_language = Language(
                    account_id=account.account_id,
                    language_name=language["language"]["constant_name"],
                    language_score=language.get("score") if language.get("score") is not None else "N/A",
                    language_certificate_name=language["language"]["constant_name"],
                    language_certificate_date=parse_date(language.get("certificate_time"), "certificate_time"),
                )
                db.session.add(user_language)
            db.session.commit()
        except Exception as error:
            db.session.rollback()
            logger.error(f"Error while inserting Language records: {error}")

        # Insert ApplicationPosition and ApplicationSkill records
        for application_position in user_data.get("application_positions") or []:
            isSuccess = False
            try:
                position = ApplicationPosition(
                    account_id=account.account_id,
                    apply_position_title=application_position["apply_position"]["constant_name"],
                    salary=None,  # Salary is not provided in the JSON
                    description=None,  # Description is not provided in the JSON
                    status=True,  # Default to active
                )
                db.session.add(position)
                db.session.flush()  # Flush to get the ID of the newly created ApplicationPosition
                db.session.commit()
                isSuccess = True
            except Exception as error:
                db.session.rollback()
                logger.error(f"Error while inserting ApplicationPosition: {error}")
                isSuccess = False

            # Insert ApplicationSkill records
            if isSuccess:
                for skill in application_position.get("skills") or []:
                    try:
                        application_skill = ApplicationSkill(
                            application_position_id=position.id,
                            skill_name=skill["skill"]["constant_name"],
                            description=None,  # Add logic to extract skill-specific descriptions if needed
                        )
                        db.session.add(application_skill)
                        db.session.commit()
                    except Exception as error:
                        db.session.rollback()
                        logger.error(f"Error while inserting ApplicationSkill: {error}")

        # Commit all changes to the database
        db.session.commit()
        logger.info("Finished extracting and inserting user-related data from JSON.")
    except Exception as error:
        db.session.rollback()
        logger.error(f"Error while extracting and inserting user-related data: {error}")

def reset_user_data(reset: bool = False, userConstant: Constant = None) -> None:
    """
    Resets user-related data in the database.
    """
    logger = setup_logger()

    # Reset user-related data if specified
    if reset:
        logger.info("Reset is True. Deleting all existing user-related data...")
        User.query.delete()
        UserAward.query.delete()
        UserEducation.query.delete()
        UserExperience.query.delete()
        Language.query.delete()

        # Get all account IDs of users with the specified system role
        user_account_ids = [account.account_id for account in Account.query.filter_by(system_role=userConstant.constant_id).all()]

        # Get all positions associated with these account IDs
        positions = ApplicationPosition.query.filter(ApplicationPosition.account_id.in_(user_account_ids)).all()
        position_ids = [position.id for position in positions]

        # Delete ApplicationSkill records based on position IDs
        ApplicationSkill.query.filter(ApplicationSkill.application_position_id.in_(position_ids)).delete(synchronize_session=False)

        # Delete ApplicationPosition records
        ApplicationPosition.query.filter(ApplicationPosition.id.in_(position_ids)).delete(synchronize_session=False)

        # Delete Account records
        Account.query.filter(Account.account_id.in_(user_account_ids)).delete(synchronize_session=False)
        
        db.session.commit()
        logger.info("All user-related data has been deleted.")

