{"email": "<EMAIL>", "address": "143 <PERSON>, Flushing, NY 11367", "phone_number": "(*************", "first_name": "Blazer", "last_name": "Bills", "gender": true, "date_of_birth": "1990-01-01", "summary_introduction": "Programming professional capable of managing technology projects with remarkable deadline sensitivity.", "social_media_link": ["https://www.linkedin.com/in/blazerbills", "https://blazerbills.com"], "system_role": {"constant_id": "a3c82035-aaaf-432a-bf5a-131a5244dafc", "constant_type": "0111001", "constant_name": "User"}, "educations": [{"study_place": "ZZZ Institute, New York City, NY", "study_start_time": "2020-01-01", "study_end_time": "2021-01-01", "majority": "Programming", "cpa": 3.5}, {"study_place": "College, Brooklyn", "study_start_time": "2018-01-01", "study_end_time": "2020-01-01", "majority": "Undergraduate Studies", "cpa": 3.0}, {"study_place": "TTT Instituto, Brooklyn, NY", "study_start_time": "2016-01-01", "study_end_time": "2018-01-01", "majority": "Undergraduate Studies", "cpa": 3.2}], "awards": null, "experiences": [{"experience_start_time": "2021-01-01", "experience_end_time": "2023-01-01", "experience_type": {"constant_name": "Work"}, "work_place": "XYZ Company, New York City, NY", "position": "Programming Developer"}], "languages": [{"language": {"constant_name": "English"}, "score": "Fluent", "certificate_date": "2021-01-01"}, {"language": {"constant_name": "Russian"}, "score": "Fluent", "certificate_date": "2021-01-01"}, {"language": {"constant_name": "Persian"}, "score": "Fluent", "certificate_date": "2021-01-01"}, {"language": {"constant_name": "Hebrew"}, "score": "Basic", "certificate_date": "2021-01-01"}], "application_positions": [{"apply_position": {"constant_name": "Software Engineer"}, "skills": [{"skill": {"constant_name": "HTML"}}, {"skill": {"constant_name": "SQL"}}, {"skill": {"constant_name": "JavaScript"}}]}]}