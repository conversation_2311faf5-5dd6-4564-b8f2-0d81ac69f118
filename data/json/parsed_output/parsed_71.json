{"email": "<EMAIL>", "address": null, "phone_number": null, "first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "gender": true, "date_of_birth": null, "summary_introduction": "Java Developer with experience in developing new applications.", "social_media_link": ["LinkedIn"], "system_role": {"constant_id": "a3c82035-aaaf-432a-bf5a-131a5244dafc", "constant_type": "0111001", "constant_name": "User"}, "educations": [{"study_place": "University of Science", "study_start_time": "2014-01-01", "study_end_time": "2018-01-01", "majority": "Computer Science", "cpa": null}], "awards": [{"certificate_time": "2016-10-01", "certificate_name": "Oracle Certified Associate, Java SE Programmer"}, {"certificate_time": "2016-10-01", "certificate_name": "Spring Professional Certification"}], "experiences": [{"experience_start_time": "2016-06-01", "experience_end_time": null, "experience_type": {"constant_name": "Work"}, "work_place": "<PERSON><PERSON><PERSON>", "position": "Senior Java Developer"}], "languages": [{"language": {"constant_name": "English"}, "score": null, "certificate_date": null}, {"language": {"constant_name": "Spanish"}, "score": null, "certificate_date": null}], "application_positions": [{"apply_position": {"constant_name": "Java Developer"}, "skills": [{"skill": {"constant_name": "Leadership"}}, {"skill": {"constant_name": "Problem Solving"}}, {"skill": {"constant_name": "Java"}}]}]}