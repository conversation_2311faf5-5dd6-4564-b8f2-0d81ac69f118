{"email": "<EMAIL>", "address": "Mumbai; India", "phone_number": "+91-9876543210", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "gender": true, "date_of_birth": null, "summary_introduction": "<PERSON> is a Meticulous React developer; skilled at building user interface components using the React JavaScript library including creating reusable UI elements; such as forms and buttons, and integrating them into the overall application: An excellent listener; with the ability to assess and help in tough situations.", "social_media_link": ["https://www.linkedin.com/raj"], "system_role": {"constant_id": "a3c82035-aaaf-432a-bf5a-131a5244dafc", "constant_type": "0111001", "constant_name": "User"}, "educations": [{"study_place": "Birla Institute Of Technology And Science, Pilani", "study_start_time": "2011-07-01", "study_end_time": "2013-05-01", "majority": "M:Tech in Computer Science", "cpa": null}, {"study_place": "Indian Institute Of Technology, Kanpur", "study_start_time": "2007-07-01", "study_end_time": "2011-04-01", "majority": "B.Tech in Computer Science", "cpa": null}], "awards": null, "experiences": [{"experience_start_time": "2021-11-01", "experience_end_time": null, "experience_type": {"constant_name": "Work"}, "work_place": "IG Drones, Mumbai", "position": "Senior React <PERSON>"}, {"experience_start_time": "2018-01-01", "experience_end_time": "2021-10-01", "experience_type": {"constant_name": "Work"}, "work_place": "Mercedes-Benz, Bangalore", "position": "Junior React <PERSON>"}, {"experience_start_time": "2014-11-01", "experience_end_time": "2017-12-01", "experience_type": {"constant_name": "Work"}, "work_place": "Dave Industries, New Delhi", "position": "Junior React <PERSON>"}, {"experience_start_time": "2013-06-01", "experience_end_time": "2014-09-01", "experience_type": {"constant_name": "Internship"}, "work_place": "DI Industries, Pilani", "position": "Intern"}], "languages": [{"language": {"constant_name": "English"}, "score": null, "certificate_date": null}, {"language": {"constant_name": "Hindi"}, "score": null, "certificate_date": null}], "application_positions": null}