{"email": "<EMAIL>", "address": "234, Ashok Nagar, Delhi 110009", "phone_number": "9999888877", "first_name": "<PERSON><PERSON>", "last_name": "<PERSON><PERSON>", "gender": true, "date_of_birth": null, "summary_introduction": "Qualified JAVA developer, with experience in software design; development and maintenance of java enterprise applications. Ability to analyze user needs and software requirements to determine feasibility of design with strong approach to perform testing and debugging processes within set timeframes & cost constraints.", "social_media_link": ["gitcom/me"], "system_role": {"constant_id": "a3c82035-aaaf-432a-bf5a-131a5244dafc", "constant_type": "0111001", "constant_name": "User"}, "educations": [{"study_place": "JIMS ENGINEERING MANAGEMENT AND TECHNICAL CAMPUS", "study_start_time": "2014-08-01", "study_end_time": "2018-05-31", "majority": "Information Technology", "cpa": null}, {"study_place": "Holy Angels Model School, Delhi", "study_start_time": "2012-04-01", "study_end_time": "2014-03-31", "majority": "Higher Secondary", "cpa": null}, {"study_place": "Holy Angels Model School, Delhi", "study_start_time": "2010-04-01", "study_end_time": "2012-03-31", "majority": "Senior Secondary", "cpa": null}], "awards": [], "experiences": [{"experience_start_time": "2021-01-01", "experience_end_time": "2021-06-30", "experience_type": {"constant_name": "Internship"}, "work_place": "AB Software Solutions, New Delhi", "position": "Java Programmer"}], "languages": [{"language": {"constant_name": "English"}, "score": null, "certificate_date": null}], "application_positions": [{"apply_position": {"constant_name": "Java Programmer"}, "skills": [{"skill": {"constant_name": "Java Programming"}}, {"skill": {"constant_name": "Troubleshooting and Bug fixing"}}, {"skill": {"constant_name": "Testing and QA"}}, {"skill": {"constant_name": "Ubuntu"}}, {"skill": {"constant_name": "C"}}, {"skill": {"constant_name": "C++"}}, {"skill": {"constant_name": "MySQL"}}, {"skill": {"constant_name": "HTML"}}, {"skill": {"constant_name": "PHP"}}]}]}