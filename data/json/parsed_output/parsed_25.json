{"email": "<EMAIL>", "address": "Los Angeles", "phone_number": "(*************", "first_name": "<PERSON>", "last_name": "<PERSON>", "gender": true, "date_of_birth": null, "summary_introduction": "Driven Backend Developer with 5 years of experience in building and optimizing server-side applications. Expertise in crafting scalable APIs and managing databases to enhance system performance. Strong collaborator with a focus on delivering high-quality software solutions that align with strategic objectives.", "social_media_link": ["WWWqwikresumecom"], "system_role": {"constant_id": "a3c82035-aaaf-432a-bf5a-131a5244dafc", "constant_type": "0111001", "constant_name": "User"}, "educations": [{"study_place": "State University", "study_start_time": "2019-01-01", "study_end_time": "2020-01-01", "majority": "Computer Science", "cpa": null}], "awards": null, "experiences": [{"experience_start_time": "2021-01-01", "experience_end_time": null, "experience_type": {"constant_name": "Work"}, "work_place": "BlueskyJnnovalions", "position": "Freelance Backend Developer"}, {"experience_start_time": "2020-01-01", "experience_end_time": "2021-01-01", "experience_type": {"constant_name": "Work"}, "work_place": "Cactus Creek Solutions", "position": "Backend Developer"}], "languages": [{"language": {"constant_name": "English"}, "score": null, "certificate_date": null}, {"language": {"constant_name": "Italian"}, "score": null, "certificate_date": null}, {"language": {"constant_name": "Swahili"}, "score": null, "certificate_date": null}], "application_positions": null}