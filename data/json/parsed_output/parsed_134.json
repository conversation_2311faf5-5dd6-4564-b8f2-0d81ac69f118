{"email": "<EMAIL>", "address": "Chicago, Illinois, US", "phone_number": "************", "first_name": "<PERSON>", "last_name": "<PERSON>", "gender": true, "date_of_birth": null, "summary_introduction": "Experienced ReactJS Developer with strong background in building efficient and scalable web applications. Proficient in front-end technologies, such as HTML, CSS, and JavaScript. Skilled in creating reusable and modular components, optimizing performance, and ensuring cross-browser compatibility. Passionate about staying updated with the latest trends and best practices in ReactJS development. Shown expertise collaborating with cross-functional teams to deliver high-quality software solutions.", "social_media_link": ["linkedin.com/resumekraft"], "system_role": {"constant_id": "a3c82035-aaaf-432a-bf5a-131a5244dafc", "constant_type": "0111001", "constant_name": "User"}, "educations": [{"study_place": "San Jose State University", "study_start_time": "2016-09-01", "study_end_time": "2021-01-01", "majority": "Software Engineering", "cpa": null}], "awards": null, "experiences": [{"experience_start_time": "2019-07-01", "experience_end_time": "2019-09-01", "experience_type": {"constant_name": "Work"}, "work_place": "Accenture Inc", "position": "ReactJs <PERSON>"}, {"experience_start_time": "2016-03-01", "experience_end_time": "2019-04-01", "experience_type": {"constant_name": "Work"}, "work_place": "Infosys", "position": "Software Engineer (Reactjs)"}], "languages": [{"language": {"constant_name": "English"}, "score": null, "certificate_date": null}], "application_positions": null}