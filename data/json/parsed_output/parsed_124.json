{"email": "<EMAIL>", "address": "Atlanta, GA 30306", "phone_number": "(*************", "first_name": "<PERSON>", "last_name": "Alphins", "gender": true, "date_of_birth": null, "summary_introduction": "Skilled and innovative Python developer; proficient in creating robust and scalable applications. Experienced in analyzing, designing, implementing code architecture. Demonstrated expertise in optimizing and improving application performance. Detail-oriented with a passion for writing clean and maintainable code. Proven track record of delivering high-quality software solutions within deadlines. Excellent team player with effective communication and collaboration skills. Strong problem-solving capabilities and continuous learner of emerging technologies.", "social_media_link": null, "system_role": {"constant_id": "a3c82035-aaaf-432a-bf5a-131a5244dafc", "constant_type": "0111001", "constant_name": "User"}, "educations": [{"study_place": "University of California", "study_start_time": "2015-08-01", "study_end_time": "2019-05-01", "majority": "Bachelor of Science in Computer Science", "cpa": null}], "awards": null, "experiences": [{"experience_start_time": "2019-07-01", "experience_end_time": null, "experience_type": {"constant_name": "Work"}, "work_place": "Airbnb", "position": "Python Developer"}, {"experience_start_time": "2018-06-01", "experience_end_time": "2018-08-01", "experience_type": {"constant_name": "Internship"}, "work_place": "Dropbox", "position": "Software Engineering Intern"}], "languages": [{"language": {"constant_name": "English"}, "score": null, "certificate_date": null}], "application_positions": null}