```json
{
  "email": "<EMAIL>",
  "address": "1373 Hannah Street Charlotte, NC 28217",
  "phone_number": "8283949655",
  "first_name": "<PERSON>",
  "last_name": "<PERSON>",
  "gender": true,
  "date_of_birth": null,
  "summary_introduction": "Experienced full-stack web developer with proven end to end development skills. Received an award for the best web developer of the company in 2018 for raising the user experience scores by 4896. Appreciated, and commended multiple times by the upper management for efficiency.",
  "social_media_link": [
    "https://github.com/LoganBenjamin",
    "https://linkedin.com/in/logan-benjamin"
  ],
  "system_role": {
    "constant_id": "a3c82035-aaaf-432a-bf5a-131a5244dafc",
    "constant_type": "0111001",
    "constant_name": "User"
  },
  "educations": [
    {
      "study_place": "Fort Hays State University",
      "study_start_time": "2016-01-01",
      "study_end_time": "2018-04-01",
      "majority": "Web Development",
      "cpa": null
    },
    {
      "study_place": "Stanford University",
      "study_start_time": "2012-01-01",
      "study_end_time": "2015-12-01",
      "majority": "Computer Science",
      "cpa": 3.7
    }
  ],
  "awards": [
    {
      "certificate_time": "2018-01-01",
      "certificate_name": "Best Web Developer of the Company"
    }
  ],
  "experiences": [
    {
      "experience_start_time": "2018-03-01",
      "experience_end_time": "Present",
      "experience_type": {
        "constant_name": "Work"
      },
      "work_place": "Proximo Int",
      "position": "Senior Web Developer"
    },
    {
      "experience_start_time": "2016-02-01",
      "experience_end_time": "2018-02-01",
      "experience_type": {
        "constant_name": "Internship"
      },
      "work_place": "Universoft Ltd.",
      "position": "Trainee Web Developer"
    }
  ],
  "languages": [
    {
      "language": {
        "constant_name": "French"
      },
      "score": "Working Knowledge",
      "certificate_date": null
    },
    {
      "language": {
        "constant_name": "English"
      },
      "score": "Native Speaker",
      "certificate_date": null
    },
    {
      "language": {
        "constant_name": "German"
      },
      "score": "B1",
      "certificate_date": null
    }
  ],
  "application_positions": [
    {
      "apply_position": {
        "constant_name": null
      },
      "skills": [
        {
          "skill": {
            "constant_name": "JavaScript"
          }
        },
        {
          "skill": {
            "constant_name": "Python"
          }
        },
        {
          "skill": {
            "constant_name": "JSON"
          }
        },
        {
          "skill": {
            "constant_name": "GraphQL"
          }
        },
        {
          "skill": {
            "constant_name": "Responsive Design"
          }
        },
        {
          "skill": {
            "constant_name": "Problem solving"
          }
        },
        {
          "skill": {
            "constant_name": "WordPress"
          }
        },
        {
          "skill": {
            "constant_name": "Adaptability"
          }
        },
        {
          "skill": {
            "constant_name": "HTML"
          }
        },
        {
          "skill": {
            "constant_name": "CSS"
          }
        },
        {
          "skill": {
            "constant_name": "MySQL"
          }
        },
        {
          "skill": {
            "constant_name": "C++"
          }
        },
        {
          "skill": {
            "constant_name": "PHP"
          }
        },
        {
          "skill": {
            "constant_name": "Critical thinking"
          }
        },
        {
          "skill": {
            "constant_name": "Visual thinking"
          }
        }
      ]
    }
  ]
}
```