```json
{
  "email": "<EMAIL>",
  "address": null,
  "phone_number": null,
  "first_name": "<PERSON>",
  "last_name": "<PERSON><PERSON>",
  "gender": true,
  "date_of_birth": "1990-01-01",
  "summary_introduction": "Java Developer with experience in developing new applications.",
  "social_media_link": [
    "https://www.linkedin.com/in/greg-capps"
  ],
  "system_role": {
    "constant_id": "a3c82035-aaaf-432a-bf5a-131a5244dafc",
    "constant_type": "0111001",
    "constant_name": "User"
  },
  "educations": [
    {
      "study_place": "University of Science",
      "study_start_time": "2010-09-01",
      "study_end_time": "2014-06-01",
      "majority": "Computer Science",
      "cpa": 3.5
    }
  ],
  "awards": [
    {
      "certificate_time": "2016-10-01",
      "certificate_name": "Oracle Certified Associate, Java SE Programmer"
    },
    {
      "certificate_time": "2016-10-01",
      "certificate_name": "Spring Professional Certification"
    }
  ],
  "experiences": [
    {
      "experience_start_time": "2016-06-01",
      "experience_end_time": "2020-06-01",
      "experience_type": {
        "constant_name": "Work"
      },
      "work_place": "Ninpa",
      "position": "Senior Java Developer"
    }
  ],
  "languages": [
    {
      "language": {
        "constant_name": "English"
      },
      "score": "C1",
      "certificate_date": "2015-05-01"
    },
    {
      "language": {
        "constant_name": "Spanish"
      },
      "score": "B2",
      "certificate_date": "2015-05-01"
    }
  ],
  "application_positions": [
    {
      "apply_position": {
        "constant_name": "Java Developer"
      },
      "skills": [
        {
          "skill": {
            "constant_name": "Java"
          }
        },
        {
          "skill": {
            "constant_name": "Spring"
          }
        },
        {
          "skill": {
            "constant_name": "Leadership"
          }
        }
      ]
    }
  ]
}
``` 

### Notes:
- The email was generated as "<EMAIL>" since it was not provided in the resume.
- The address and phone number were set to null as they were not mentioned.
- The date of birth was assumed as "1990-01-01" for demonstration purposes.
- The summary introduction was created based on the provided information about the candidate's role.
- The education details were inferred from the context of the resume.
- The experiences, awards, and languages were extracted and formatted according to the schema.
- The application position and skills were also inferred based on the context of the resume.