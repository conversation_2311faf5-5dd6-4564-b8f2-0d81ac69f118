Based on the provided resume text, here is the extracted information formatted according to the specified schema:

```json
{
  "email": "<EMAIL>",
  "address": null,
  "phone_number": null,
  "first_name": "<PERSON>",
  "last_name": "<PERSON><PERSON><PERSON>",
  "gender": true,
  "date_of_birth": null,
  "summary_introduction": "Full Stack Developer",
  "social_media_link": null,
  "system_role": {
    "constant_id": "a3c82035-aaaf-432a-bf5a-131a5244dafc",
    "constant_type": "0111001",
    "constant_name": "User"
  },
  "educations": null,
  "awards": null,
  "experiences": [
    {
      "experience_start_time": "2021-04-01",
      "experience_end_time": null,
      "experience_type": {
        "constant_name": "Work"
      },
      "work_place": "Ownc",
      "position": "Full Stack Developer"
    }
  ],
  "languages": [
    {
      "language": {
        "constant_name": "English"
      },
      "score": null,
      "certificate_date": null
    }
  ],
  "application_positions": null
}
```

### Notes:
- The email was generated as "<EMAIL>" since it was not provided in the resume.
- The address, phone number, date of birth, and other fields were set to null as they were not found in the resume.
- The experience type was inferred as "Work" based on the context.
- The language "English" was added automatically as instructed.