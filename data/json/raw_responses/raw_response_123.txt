```json
{
  "email": "info@resumekraftcom",
  "address": "Chicago, Illinois, US",
  "phone_number": "************",
  "first_name": "<PERSON>",
  "last_name": "<PERSON>",
  "gender": true,
  "date_of_birth": null,
  "summary_introduction": "I am a python developer, am interested in Automation using python: have Automotive domain knowledge and have experience designing Test Automation Frameworks like to build smart applications using AI and python.",
  "social_media_link": [
    "linkedin.com/resumekraft"
  ],
  "system_role": {
    "constant_id": "a3c82035-aaaf-432a-bf5a-131a5244dafc",
    "constant_type": "0111001",
    "constant_name": "User"
  },
  "educations": [
    {
      "study_place": "San Jose State University",
      "study_start_time": "2010-09-01",
      "study_end_time": "2014-06-01",
      "majority": "Electronics and Communication",
      "cpa": null
    }
  ],
  "awards": [
    {
      "certificate_time": "2015-06-05",
      "certificate_name": "Embedded system"
    }
  ],
  "experiences": [
    {
      "experience_start_time": "2018-01-01",
      "experience_end_time": "Present",
      "experience_type": {
        "constant_name": "Work"
      },
      "work_place": "Wipro Technologies",
      "position": "Senior Project Engineer"
    },
    {
      "experience_start_time": "2015-07-01",
      "experience_end_time": "2017-12-01",
      "experience_type": {
        "constant_name": "Work"
      },
      "work_place": "Wipro Technologies",
      "position": "Project Engineer"
    }
  ],
  "languages": [
    {
      "language": {
        "constant_name": "English"
      },
      "score": null,
      "certificate_date": null
    }
  ],
  "application_positions": [
    {
      "apply_position": {
        "constant_name": "Python Developer"
      },
      "skills": [
        {
          "skill": {
            "constant_name": "Python"
          }
        },
        {
          "skill": {
            "constant_name": "Automation"
          }
        },
        {
          "skill": {
            "constant_name": "Selenium"
          }
        },
        {
          "skill": {
            "constant_name": "C++ programming"
          }
        },
        {
          "skill": {
            "constant_name": "Machine learning"
          }
        }
      ]
    }
  ]
}
```