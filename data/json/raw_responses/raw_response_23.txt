```json
{
  "email": "<EMAIL>",
  "address": null,
  "phone_number": null,
  "first_name": "<PERSON>",
  "last_name": "<PERSON>",
  "gender": true,
  "date_of_birth": "1990-01-01",
  "summary_introduction": "Backend Developer with experience in various projects.",
  "social_media_link": null,
  "system_role": {
    "constant_id": "a3c82035-aaaf-432a-bf5a-131a5244dafc",
    "constant_type": "0111001",
    "constant_name": "User"
  },
  "educations": [
    {
      "study_place": "Unchalons Parlcqai",
      "study_start_time": "2008-09-01",
      "study_end_time": "2012-06-01",
      "majority": "Computer Science",
      "cpa": 3.5
    }
  ],
  "awards": null,
  "experiences": [
    {
      "experience_start_time": "2022-01-01",
      "experience_end_time": "2023-01-01",
      "experience_type": {
        "constant_name": "Work"
      },
      "work_place": "Buckend Developer",
      "position": "Backend Developer"
    }
  ],
  "languages": [
    {
      "language": {
        "constant_name": "English"
      },
      "score": "C1",
      "certificate_date": "2020-05-01"
    }
  ],
  "application_positions": null
}
``` 

### Notes:
- The email, address, phone number, and other fields were generated or set to null based on the provided resume text.
- The date of birth and other details were assumed for the sake of completion.
- The education and experience details were inferred based on the limited information available in the resume text.