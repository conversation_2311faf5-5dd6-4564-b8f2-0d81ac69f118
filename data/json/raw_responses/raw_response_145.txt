```json
{
  "email": "<EMAIL>",
  "address": "123 Street Brooklyn, New York",
  "phone_number": "202-551-789",
  "first_name": "<PERSON><PERSON>",
  "last_name": "<PERSON>",
  "gender": true,
  "date_of_birth": null,
  "summary_introduction": "GRADUATE SOFTWARE ENGINEER",
  "social_media_link": [
    "github.com/mcgeej"
  ],
  "system_role": {
    "constant_id": "a3c82035-aaaf-432a-bf5a-131a5244dafc",
    "constant_type": "0111001",
    "constant_name": "User"
  },
  "educations": [
    {
      "study_place": "University of New York",
      "study_start_time": "20XX-01-01",
      "study_end_time": "20XX-05-01",
      "majority": "Master of Science in Engineering",
      "cpa": 3.75
    },
    {
      "study_place": "University of New York",
      "study_start_time": "20XX-01-01",
      "study_end_time": "20XX-05-01",
      "majority": "Bachelor of Science in Engineering",
      "cpa": 3.75
    }
  ],
  "awards": null,
  "experiences": [
    {
      "experience_start_time": "20XX-01-01",
      "experience_end_time": "20XX-01-01",
      "experience_type": {
        "constant_name": "Internship"
      },
      "work_place": "Company Name",
      "position": "Software Engineer Intern"
    },
    {
      "experience_start_time": "20XX-01-01",
      "experience_end_time": "20XX-01-01",
      "experience_type": {
        "constant_name": "Project"
      },
      "work_place": "Name of the Project",
      "position": "Software Engineer"
    }
  ],
  "languages": [
    {
      "language": {
        "constant_name": "English"
      },
      "score": null,
      "certificate_date": null
    }
  ],
  "application_positions": null
}
``` 

### Notes:
- The dates for education and experiences are placeholders (20XX-01-01) since specific dates were not provided in the resume text.
- The `awards` and `application_positions` fields are set to `null` as no relevant information was provided.
- The `gender` is set to `true` assuming the candidate is male based on the name "Jamison".