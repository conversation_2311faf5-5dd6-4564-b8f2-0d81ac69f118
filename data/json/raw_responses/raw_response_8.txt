```json
{
  "email": "<EMAIL>",
  "address": null,
  "phone_number": null,
  "first_name": "<PERSON>",
  "last_name": "<PERSON>",
  "gender": true,
  "date_of_birth": null,
  "summary_introduction": "Angular Developer with experience in developing web applications using Angular.",
  "social_media_link": [
    "https://www.linkedin.com/in/andrea<PERSON><PERSON><PERSON>ald"
  ],
  "system_role": {
    "constant_id": "a3c82035-aaaf-432a-bf5a-131a5244dafc",
    "constant_type": "0111001",
    "constant_name": "User"
  },
  "educations": [
    {
      "study_place": "University of California, Los Angeles (UCLA)",
      "study_start_time": "2014-09-01",
      "study_end_time": "2018-05-01",
      "majority": "Computer Science",
      "cpa": null
    }
  ],
  "awards": null,
  "experiences": [
    {
      "experience_start_time": null,
      "experience_end_time": null,
      "experience_type": {
        "constant_name": "Work"
      },
      "work_place": "San Francisco, CA",
      "position": "Angular Developer"
    }
  ],
  "languages": [
    {
      "language": {
        "constant_name": "English"
      },
      "score": null,
      "certificate_date": null
    }
  ],
  "application_positions": null
}
``` 

### Notes:
- The email was generated based on the name provided, as it was not explicitly mentioned in the resume text.
- The address and phone number were not provided in the resume, so they are set to null.
- The date of birth was not mentioned, so it is also set to null.
- The summary introduction was created based on the context of the resume.
- The experience type was assumed to be "Work" based on the context provided.
- The GPA/CPA was not mentioned, so it is set to null.
- The awards and application positions were not mentioned, so they are set to null.