Based on the provided resume text, the extracted information formatted according to the specified schema is as follows:

```json
{
  "email": null,
  "address": null,
  "phone_number": null,
  "first_name": "<PERSON>",
  "last_name": "<PERSON>",
  "gender": true,
  "date_of_birth": null,
  "summary_introduction": null,
  "social_media_link": null,
  "system_role": {
    "constant_id": "a3c82035-aaaf-432a-bf5a-131a5244dafc",
    "constant_type": "0111001",
    "constant_name": "User"
  },
  "educations": null,
  "awards": null,
  "experiences": null,
  "languages": [
    {
      "language": {
        "constant_name": "English"
      },
      "score": null,
      "certificate_date": null
    }
  ],
  "application_positions": null
}
```

### Explanation:
- **Basic Information**: Extracted first name and last name from the resume. Other fields are set to null as they are not provided.
- **System Role**: Fixed value as specified.
- **Languages**: Added English as a language since the resume is in English.
- **Other Fields**: Set to null as there is no information available in the provided text.