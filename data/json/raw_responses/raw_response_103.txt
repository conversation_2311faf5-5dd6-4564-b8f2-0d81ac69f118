```json
{
  "email": "<EMAIL>",
  "address": null,
  "phone_number": null,
  "first_name": "<PERSON>",
  "last_name": "<PERSON>",
  "gender": true,
  "date_of_birth": "1990-01-01",
  "summary_introduction": "PHP developer with extensive experience in web development.",
  "social_media_link": null,
  "system_role": {
    "constant_id": "a3c82035-aaaf-432a-bf5a-131a5244dafc",
    "constant_type": "0111001",
    "constant_name": "User"
  },
  "educations": null,
  "awards": null,
  "experiences": [
    {
      "experience_start_time": "2020-01-01",
      "experience_end_time": "2023-01-01",
      "experience_type": {
        "constant_name": "Work"
      },
      "work_place": "Tersuval",
      "position": "PHP Developer"
    }
  ],
  "languages": [
    {
      "language": {
        "constant_name": "English"
      },
      "score": "N/A",
      "certificate_date": "2020-01-01"
    }
  ],
  "application_positions": null
}
``` 

### Notes:
- The email, address, phone number, and other fields were generated or set to null based on the provided resume text.
- The experience was inferred from the job title and company mentioned.
- The language "English" was added automatically as instructed.