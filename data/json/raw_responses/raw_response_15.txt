```json
{
  "email": "<EMAIL>",
  "address": "New Delhi; India",
  "phone_number": "8027280990",
  "first_name": "<PERSON><PERSON><PERSON><PERSON>",
  "last_name": "<PERSON>dav",
  "gender": true,
  "date_of_birth": null,
  "summary_introduction": "Experienced Backend Developer with strong knowledge of Agile Software Development practices, Rest API, Springboot, terraform, and Kubernetes. Proficient in Java EC API, Git, SQL, Design patterns, System design, and CICD fundamentals for containerization, refactoring, authentications, and re-platforming.",
  "social_media_link": [
    "linkedin.com/in/shushrut-yadav"
  ],
  "system_role": {
    "constant_id": "a3c82035-aaaf-432a-bf5a-131a5244dafc",
    "constant_type": "0111001",
    "constant_name": "User"
  },
  "educations": [
    {
      "study_place": "Delhi Technical University, New Delhi",
      "study_start_time": "2014-08-01",
      "study_end_time": "2018-05-01",
      "majority": "B.Tech Computer Science",
      "cpa": null
    },
    {
      "study_place": "Delhi Public School, Rohini, New Delhi",
      "study_start_time": "2013-04-01",
      "study_end_time": "2015-05-01",
      "majority": "Senior Secondary",
      "cpa": null
    }
  ],
  "awards": null,
  "experiences": [
    {
      "experience_start_time": "2020-01-01",
      "experience_end_time": "Present",
      "experience_type": {
        "constant_name": "Work"
      },
      "work_place": "Tata Consultancy Services, New Delhi",
      "position": "Backend Developer"
    },
    {
      "experience_start_time": "2018-04-01",
      "experience_end_time": "2020-12-01",
      "experience_type": {
        "constant_name": "Work"
      },
      "work_place": "HCL Technologies, New Delhi",
      "position": "Backend Developer"
    }
  ],
  "languages": [
    {
      "language": {
        "constant_name": "Hindi"
      },
      "score": null,
      "certificate_date": null
    },
    {
      "language": {
        "constant_name": "English"
      },
      "score": null,
      "certificate_date": null
    }
  ],
  "application_positions": null
}
```