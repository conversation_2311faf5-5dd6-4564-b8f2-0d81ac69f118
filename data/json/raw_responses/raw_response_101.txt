```json
{
  "email": "<EMAIL>",
  "address": "Chicago, Illinois, US",
  "phone_number": "************",
  "first_name": "<PERSON>",
  "last_name": "<PERSON>",
  "gender": true,
  "date_of_birth": null,
  "summary_introduction": "I have decided to change my career to programming: have always been interested in programming and last year attended a programming course. During course learnt about basics of PHP, HTML, CSS, XML, MySQL, object-oriented programming, Javascript: Moreover since then developed my skills My goal is to improve my knowledge and experience in programming.",
  "social_media_link": [
    "linkedin.com/resumekraft"
  ],
  "system_role": {
    "constant_id": "a3c82035-aaaf-432a-bf5a-131a5244dafc",
    "constant_type": "0111001",
    "constant_name": "User"
  },
  "educations": [
    {
      "study_place": "San Jose State University",
      "study_start_time": "2014-01-01",
      "study_end_time": "2018-01-01",
      "majority": "Finance, Accounting and Insurances; Master's Degree",
      "cpa": null
    },
    {
      "study_place": "Northeastern University",
      "study_start_time": "2011-01-01",
      "study_end_time": "2013-01-01",
      "majority": "Bachelor of Finance and Accounting",
      "cpa": null
    }
  ],
  "awards": null,
  "experiences": [
    {
      "experience_start_time": "2017-04-01",
      "experience_end_time": "2019-09-01",
      "experience_type": {
        "constant_name": "Work"
      },
      "work_place": "MoneyGram International",
      "position": "Senior Compliance Analyst/Compliance Analyst"
    },
    {
      "experience_start_time": "2016-01-01",
      "experience_end_time": "2016-12-31",
      "experience_type": {
        "constant_name": "Work"
      },
      "work_place": "HCL Technologies",
      "position": "KYCIAML Analyst for Deutsche Bank"
    },
    {
      "experience_start_time": "2014-01-01",
      "experience_end_time": "2016-01-01",
      "experience_type": {
        "constant_name": "Work"
      },
      "work_place": "Citibank International Limited",
      "position": "AML Analyst"
    }
  ],
  "languages": [
    {
      "language": {
        "constant_name": "English"
      },
      "score": null,
      "certificate_date": null
    },
    {
      "language": {
        "constant_name": "Arabic"
      },
      "score": null,
      "certificate_date": null
    },
    {
      "language": {
        "constant_name": "French"
      },
      "score": null,
      "certificate_date": null
    },
    {
      "language": {
        "constant_name": "German"
      },
      "score": null,
      "certificate_date": null
    }
  ],
  "application_positions": null
}
```