```json
{
  "email": "info@resumekraftcom",
  "address": "Chicago, Illinois, US",
  "phone_number": "************",
  "first_name": "<PERSON>",
  "last_name": "<PERSON>",
  "gender": true,
  "date_of_birth": null,
  "summary_introduction": "Seeking position in progressive and career-driven company that gives scope to enhance my knowledge; skills and to reach the pinnacle in the field of programming with dedication and hard work.",
  "social_media_link": [
    "linkedin com/resumekraft"
  ],
  "system_role": {
    "constant_id": "a3c82035-aaaf-432a-bf5a-131a5244dafc",
    "constant_type": "0111001",
    "constant_name": "User"
  },
  "educations": [
    {
      "study_place": "San Jose State University",
      "study_start_time": "2020-02-01",
      "study_end_time": "Present",
      "majority": "Master's in Data Science",
      "cpa": 4.0
    },
    {
      "study_place": "Northeastern University",
      "study_start_time": "2008-08-01",
      "study_end_time": "2012-05-01",
      "majority": "Bachelor's in Computer Science",
      "cpa": 3.3
    }
  ],
  "awards": null,
  "experiences": [
    {
      "experience_start_time": "2020-05-01",
      "experience_end_time": "Present",
      "experience_type": {
        "constant_name": "Work"
      },
      "work_place": "Saint Peter's University",
      "position": "Graduate Research Assistant"
    },
    {
      "experience_start_time": "2020-02-01",
      "experience_end_time": "2020-05-01",
      "experience_type": {
        "constant_name": "Internship"
      },
      "work_place": "Saint Peter's University",
      "position": "Graduate Research Assistant"
    },
    {
      "experience_start_time": "2012-09-01",
      "experience_end_time": "2017-11-01",
      "experience_type": {
        "constant_name": "Work"
      },
      "work_place": "HSBC",
      "position": "Software Engineer"
    }
  ],
  "languages": [
    {
      "language": {
        "constant_name": "English"
      },
      "score": null,
      "certificate_date": null
    }
  ],
  "application_positions": null
}
```