```json
{
  "email": "<EMAIL>",
  "address": "Chicago, Illinois, US",
  "phone_number": "************",
  "first_name": "<PERSON>",
  "last_name": "<PERSON>",
  "gender": true,
  "date_of_birth": null,
  "summary_introduction": "A hardworking and passionate job seeker with strong organizational skills eager to secure an entry-level job position where could learn under working professionals to gain knowledge and improve my skills simultaneously by giving some positive input to the company. Also an Enthusiastic Cloud Computing learner eager to contribute to team success through hard work, attention to detail and excellent organizational skills. Motivated to learn; grow and excel in Cloud Industry.",
  "social_media_link": [
    "linkedin.com/resumekraft"
  ],
  "system_role": {
    "constant_id": "a3c82035-aaaf-432a-bf5a-131a5244dafc",
    "constant_type": "0111001",
    "constant_name": "User"
  },
  "educations": [
    {
      "study_place": "San Jose State University",
      "study_start_time": "2020-04-01",
      "study_end_time": "2024-07-01",
      "majority": "Bachelor of Technology",
      "cpa": null
    },
    {
      "study_place": "High School",
      "study_start_time": "2008-04-01",
      "study_end_time": "2020-06-01",
      "majority": null,
      "cpa": null
    }
  ],
  "awards": [
    {
      "certificate_time": "2023-01-01",
      "certificate_name": "AWS for Beginners"
    },
    {
      "certificate_time": "2023-01-01",
      "certificate_name": "Introduction to C Programming"
    },
    {
      "certificate_time": "2023-01-01",
      "certificate_name": "Mastering Communication and Public Speaking Skills"
    },
    {
      "certificate_time": "2023-01-01",
      "certificate_name": "NoSQL Database"
    }
  ],
  "experiences": [
    {
      "experience_start_time": "2022-07-01",
      "experience_end_time": "Present",
      "experience_type": {
        "constant_name": "Work"
      },
      "work_place": "Infotech Private Limited",
      "position": "Python Developer"
    },
    {
      "experience_start_time": "2023-11-01",
      "experience_end_time": "2023-12-01",
      "experience_type": {
        "constant_name": "Internship"
      },
      "work_place": "Internpe",
      "position": "Python Programming Intern"
    }
  ],
  "languages": [
    {
      "language": {
        "constant_name": "English"
      },
      "score": null,
      "certificate_date": null
    },
    {
      "language": {
        "constant_name": "French"
      },
      "score": null,
      "certificate_date": null
    },
    {
      "language": {
        "constant_name": "Arabic"
      },
      "score": null,
      "certificate_date": null
    },
    {
      "language": {
        "constant_name": "German"
      },
      "score": null,
      "certificate_date": null
    }
  ],
  "application_positions": null
}
```