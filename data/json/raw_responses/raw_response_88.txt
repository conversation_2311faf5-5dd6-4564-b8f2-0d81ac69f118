```json
{
  "email": "<EMAIL>",
  "address": "37 Englewood E, Boston MA 02108",
  "phone_number": "************",
  "first_name": "<PERSON>",
  "last_name": "B<PERSON>ur",
  "gender": true,
  "date_of_birth": null,
  "summary_introduction": "Full Stack Developer adept in working in both front-end and back-end development processes; Highly skilled in programming design development; and implementation of functional specifications.",
  "social_media_link": null,
  "system_role": {
    "constant_id": "a3c82035-aaaf-432a-bf5a-131a5244dafc",
    "constant_type": "0111001",
    "constant_name": "User"
  },
  "educations": [
    {
      "study_place": "Boston University",
      "study_start_time": "2010-05-01",
      "study_end_time": null,
      "majority": "Computer Science",
      "cpa": null
    },
    {
      "study_place": "Northeastern University",
      "study_start_time": null,
      "study_end_time": null,
      "majority": "Software Engineering",
      "cpa": null
    }
  ],
  "awards": [
    {
      "certificate_time": null,
      "certificate_name": "Full Stack Developer Professional Certificate"
    }
  ],
  "experiences": [
    {
      "experience_start_time": null,
      "experience_end_time": null,
      "experience_type": {
        "constant_name": "Work"
      },
      "work_place": "Excel Technology Group",
      "position": "Full Stack Developer"
    },
    {
      "experience_start_time": null,
      "experience_end_time": null,
      "experience_type": {
        "constant_name": "Work"
      },
      "work_place": "$ & C Staffing",
      "position": "Full Stack Developer"
    }
  ],
  "languages": [
    {
      "language": {
        "constant_name": "Romanian"
      },
      "score": null,
      "certificate_date": null
    },
    {
      "language": {
        "constant_name": "English"
      },
      "score": null,
      "certificate_date": null
    },
    {
      "language": {
        "constant_name": "German"
      },
      "score": null,
      "certificate_date": null
    }
  ],
  "application_positions": null
}
```