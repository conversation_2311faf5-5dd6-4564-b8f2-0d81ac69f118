```json
{
  "email": "null",
  "address": "SOME STREET NO.240 NY BUILDING NUMBER",
  "phone_number": "00 122 4555 666",
  "first_name": "<PERSON>",
  "last_name": "<PERSON>",
  "gender": true,
  "date_of_birth": "null",
  "summary_introduction": "An accomplished software engineer specializing in Java and Java EE software design and development with extensive experience in the full life cycle of the software design process including requirements definition, prototyping, proof of concept design, interface implementation, testing, and maintenance.",
  "social_media_link": [
    "WAMWRESUMESDOT COM"
  ],
  "system_role": {
    "constant_id": "a3c82035-aaaf-432a-bf5a-131a5244dafc",
    "constant_type": "0111001",
    "constant_name": "User"
  },
  "educations": [
    {
      "study_place": "The George Washington University",
      "study_start_time": "2008-01-01",
      "study_end_time": "2012-01-01",
      "majority": "M.S Computer Science",
      "cpa": null
    }
  ],
  "awards": [
    {
      "certificate_time": "2017-01-01",
      "certificate_name": "Oracle Certification Java Professional Java SE Programmer"
    },
    {
      "certificate_time": "2016-01-01",
      "certificate_name": "Oracle Certified Expert, Java EE Web Component Developer"
    }
  ],
  "experiences": [
    {
      "experience_start_time": "2015-07-01",
      "experience_end_time": "null",
      "experience_type": {
        "constant_name": "Work"
      },
      "work_place": "DataOne",
      "position": "Senior Java Developer"
    },
    {
      "experience_start_time": "2014-02-01",
      "experience_end_time": "2015-06-01",
      "experience_type": {
        "constant_name": "Work"
      },
      "work_place": "HGV Solaris, Inc.",
      "position": "Software Engineer"
    }
  ],
  "languages": [
    {
      "language": {
        "constant_name": "Java"
      },
      "score": "null",
      "certificate_date": "null"
    },
    {
      "language": {
        "constant_name": "JavaScript"
      },
      "score": "null",
      "certificate_date": "null"
    },
    {
      "language": {
        "constant_name": "English"
      },
      "score": "null",
      "certificate_date": "null"
    }
  ],
  "application_positions": [
    {
      "apply_position": {
        "constant_name": "Senior Java Developer"
      },
      "skills": [
        {
          "skill": {
            "constant_name": "Java"
          }
        },
        {
          "skill": {
            "constant_name": "Java EE"
          }
        },
        {
          "skill": {
            "constant_name": "SQL"
          }
        },
        {
          "skill": {
            "constant_name": "Hibernate"
          }
        },
        {
          "skill": {
            "constant_name": "Oracle"
          }
        }
      ]
    }
  ]
}
```