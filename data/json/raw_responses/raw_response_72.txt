```json
{
  "email": "mnfa@4wikIsumccom",
  "address": null,
  "phone_number": "(012,-156-7*9",
  "first_name": "<PERSON>",
  "last_name": "<PERSON>",
  "gender": true,
  "date_of_birth": null,
  "summary_introduction": "Java Software Engineer with extensive experience in application development, system analysis, testing, and maintenance.",
  "social_media_link": null,
  "system_role": {
    "constant_id": "a3c82035-aaaf-432a-bf5a-131a5244dafc",
    "constant_type": "0111001",
    "constant_name": "User"
  },
  "educations": null,
  "awards": null,
  "experiences": [
    {
      "experience_start_time": "2022-04-01",
      "experience_end_time": "2022-12-31",
      "experience_type": {
        "constant_name": "Work"
      },
      "work_place": "ABC Corporation",
      "position": "Java Software Engineer"
    },
    {
      "experience_start_time": "2025-01-01",
      "experience_end_time": "2025-12-31",
      "experience_type": {
        "constant_name": "Work"
      },
      "work_place": "Delu pmonan",
      "position": "Java Software Engineer"
    }
  ],
  "languages": [
    {
      "language": {
        "constant_name": "English"
      },
      "score": null,
      "certificate_date": null
    }
  ],
  "application_positions": null
}
``` 

### Notes:
- The address, date of birth, and other fields were not provided in the resume, so they are set to null.
- The experiences were inferred from the text, with start and end dates approximated based on the context provided.
- The summary introduction was created based on the information in the resume.
- The language "English" was added automatically as instructed.