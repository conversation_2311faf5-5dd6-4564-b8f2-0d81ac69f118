```json
{
  "email": "<EMAIL>",
  "address": null,
  "phone_number": null,
  "first_name": "<PERSON>",
  "last_name": "<PERSON>",
  "gender": true,
  "date_of_birth": null,
  "summary_introduction": "Results-oriented Web Application Developer and Online Business Developer with considerable training and enterprise experience in web site design, coding, and testing.",
  "social_media_link": null,
  "system_role": {
    "constant_id": "a3c82035-aaaf-432a-bf5a-131a5244dafc",
    "constant_type": "0111001",
    "constant_name": "User"
  },
  "educations": [
    {
      "study_place": "College",
      "study_start_time": "2002-08-01",
      "study_end_time": null,
      "majority": "Master of Science in Internet Engineering",
      "cpa": null
    },
    {
      "study_place": "College of Charleston",
      "study_start_time": "1994-06-01",
      "study_end_time": null,
      "majority": "Bachelor of Arts in Music Theory and Composition",
      "cpa": null
    }
  ],
  "awards": null,
  "experiences": null,
  "languages": [
    {
      "language": {
        "constant_name": "English"
      },
      "score": null,
      "certificate_date": null
    }
  ],
  "application_positions": [
    {
      "apply_position": {
        "constant_name": "Web Application Developer"
      },
      "skills": [
        {
          "skill": {
            "constant_name": "Ruby"
          }
        },
        {
          "skill": {
            "constant_name": "PHP"
          }
        },
        {
          "skill": {
            "constant_name": "Java"
          }
        },
        {
          "skill": {
            "constant_name": "HTML"
          }
        },
        {
          "skill": {
            "constant_name": "CSS"
          }
        },
        {
          "skill": {
            "constant_name": "JavaScript"
          }
        },
        {
          "skill": {
            "constant_name": "SQL"
          }
        },
        {
          "skill": {
            "constant_name": "SEO/SEM"
          }
        }
      ]
    }
  ]
}
```