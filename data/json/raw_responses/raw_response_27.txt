```json
{
  "email": "ivankak<PERSON><EMAIL>",
  "address": "California, USA",
  "phone_number": "******-346-8751",
  "first_name": "<PERSON><PERSON>",
  "last_name": "<PERSON><PERSON><PERSON>",
  "gender": true,
  "date_of_birth": null,
  "summary_introduction": "Determined and result-oriented back-end developer with 5+ years of experience in codewriting and optimizing web pages and mobile applications. Highly proficient in more than 5 coding languages, seeking to continue improving my coding skills as a back-end developer in Tron Technology Inc.",
  "social_media_link": null,
  "system_role": {
    "constant_id": "a3c82035-aaaf-432a-bf5a-131a5244dafc",
    "constant_type": "0111001",
    "constant_name": "User"
  },
  "educations": [
    {
      "study_place": "Worcester Polytechnic Institute",
      "study_start_time": "2011-01-01",
      "study_end_time": "2015-01-01",
      "majority": "Computer Science",
      "cpa": 3.95
    }
  ],
  "awards": null,
  "experiences": [
    {
      "experience_start_time": "2018-01-01",
      "experience_end_time": null,
      "experience_type": {
        "constant_name": "Work"
      },
      "work_place": "Shop.ly",
      "position": "Back-end Developer"
    },
    {
      "experience_start_time": "2015-01-01",
      "experience_end_time": "2017-12-31",
      "experience_type": {
        "constant_name": "Work"
      },
      "work_place": "Shoppable.com",
      "position": "Back-end Developer"
    }
  ],
  "languages": [
    {
      "language": {
        "constant_name": "English"
      },
      "score": null,
      "certificate_date": null
    }
  ],
  "application_positions": null
}
```