 CONTACT                                <PERSON>
    info@resumekraft com                NODE JS DEVELOPER
    202-555-0120
    Chicago, Illinois, US
 in linkedin com/resumekraft            SUMMARY
 SKILLS                                 Iam energetic and passionate PHPINode developer looking for an opportunity
                                        to enhance my abilities in a reputable organization: have working experience in
 PHP; MySQL                             PHP; Codeigniter, Laravel and Nodejs and bit with Reactjs with MySQL
                                        and interested to explore thisjourney:
 Mvc Codeigniter                        EXPERIENCE
 Mvc laravel                            NODEJS DEVELOPER                                         Mar 2021  Present
 Node JS Node express                   Infosys
                                            Developed and deployed fullstack applications using Nodejs and related
 JavaScript                                 frameworks
                                            Collaborated with crossfunctional teams to gather requirements and define
 React Js                                   project scope
                                            Designed and implemented RESTful APIs and microservices using Express
 Git(github) Svn(Tfs, bitbucket)            or Koa
                                            Utilized NoSQL databases such as MongoDB or Redis for data storage and
 Bootstrap HTMLICSS                         retrieval
                                            Integrated thirdparty APIsᵃⁿᵈservices t0 enhance functionality and user
                                            experience
PERSONAL SKILLS                             Implemented authentication and authorization using techniques such as
                                            JWT or OAuth
Self-starter                                Optimized application performance through code optimizations and caching
                                            strategies
                                            Implemented unit tests and participated in code reviews to maintain code
Analytical                                  quality andreliability
                                            Collaborated with frontend developers to integrate serverside rendering or
Excellent Communication                     realtime updates using frameworks like React or Angular
                                            Resolved bugs and issues reported by QA or endusers in a timely manner
                                            Engaged in continuous learning and kept uptodateʷⁱᵗʰ
Organizer                                   and best practices in Nodejs development:          the latest trends
 REFERAL                                Sr PHP Develoer                                        Apr 2017 May 2021
                                        CS info tech
                                            Experience with relational databases andlor NoSQL databases (DvnamoDB;
 References Available Upon                  Redis, CosmosDB; Mongo, 53,etc)
 Request                                    Develop highly interactive web applications utilizing JavaScript HTMLS,
                                            CSS, JSON; Angular JS and integrating Restful API's, external web services
 EDUCATION                                  ensuring high performance on Mobile and Desktop Provide SEO solutions
                                            for single page apps
 Masters Of Computer                        Meet with project stakeholders discuss their vision; ideas and create
                                            compelling digital experiences. Translate design patterns into application
 Sclence                                    architectures
 Mar 2008 Mar2010                           Create custom general use modules and components extending the
 San Jose State University                  elements and modules of core AngularJS; Writes non-blocking code using
                                            advanced techniques such as multi-threading; javascript dependency