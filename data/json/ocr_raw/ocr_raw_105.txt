CONTACT                              Jackson Harris
 0  resumekraft@gmail com             PHP DEVELOPER
    1-202-555-0130
    University of Florida,
    Gainesville, FL
in https://www linkedin com/i         SUMMARY
    nijackson                         Tam energetic and passionate PHP developer looking for an opportunity to
SK<PERSON><PERSON>                                enhance my abilities in a reputable organization. haveworking experience in
                                      PHP, Codeigniter, Laravel and Node expressᵃⁿᵈ  bit about React:js with
PHP, MySQL                            MySQL and interested to explore this journey:
Mvc Codeigniter                       EXPERIENCE
Mvc laravel                           PHP Codeigniter developer                            May 2018 Present
Node JS,Node express                  Info Tech Solutions
                                         Working on Project estimates
JavaScript                               Implement code based on project specifications
                                         Setup and create a new table within MySQL.
React Js                                 Testing and QA Handover document.
                                         Giving feedback to the development group to improve Agile Project
Git(github) Svn(Tfs; bitbucket)          management methods.
Bootstrap HTMLICSS                    PHP Program Developer                               Apr 2017 May2018
                                      New-land Solutions Ltd
                                         Communicate with product and engineering team members to define and
PERSONAL SKILLS                          develop new software product concepts
                                         Improvement of system architecture by refactoring old legacy code to the
Self-starter                             latest technology.
                                         Coordinated with the QA testing team for end-to-end unit testing and post-
Analytical                               production testing:applications that converted raw data from design
Excellent Communication                  Coded user frendly
                                         engine to easily understandable graphical formats.
Organizer                             PHPDeveloper                                            2016 Mar 2017
                                      Atlantic Soft Technologies
                                         Build across-platform activity feed client for the social software product
                                         Build mobile applications for multiple platforms
                                         Implemented application security and data assurance software_
                                         Analysis of requirements and used systematic approaches to implement
                                         code.
                                      EDUCATION
                                      Masters Of Computer Science                          Feb 2012 Feb 2016
                                      University of California, Los Angeles
                                      REFERAL
                                      References Available Upon Request