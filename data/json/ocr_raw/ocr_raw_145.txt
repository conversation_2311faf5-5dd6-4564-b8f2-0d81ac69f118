                    JAMISON MCGEE
                          GRADUATE SOFTWARE ENGINEER
      202-551-789 jamisonmcgee@gmailcom  123 Street Brooklyn; New York - githubcom/mcgeej
 EDUCATION
 MASTER OF SCIENCE IN ENGINEERING (MSE)                           MON 2OXX    MAY 20XX
University of New York; New York, GPA 3.75, Cum Laude
      Courseworks: Software Foundations, Computer Architecture; Algorithms,
      Artificial Intelligence, Comparison of Learning Algorithms; Computational Theory
 BACHELOR OF SCIENCE IN ENGINEERING (BSE)                         MON 20XX    MAY 20XX
University of New York; New York, GPA 3.75, Cum Laude
      Courseworks: Operating System, Database, Algorithms; Programming Language
      Computational Architecture; Calculus III, Engineering Entrepreneurship;
TECHNICAL SKILLS
      LANGUAGES: Java, Phyton, Ctt, HTML, CSS, JavaScript, PHP;
      FRA<PERSON>WORKS, Django, NodeSJ, React;
      TOOLS: jQuery, Unix, Git, Selenium
      DATABASE: SQL (MySQL, PostgreSQL); NoSQL, AWS
 PROJECTS & RELEVANT EXPERIENCES
Software Engineer Intern                                         MON 20XX    MON 20XX
 Company Name   Location; State
      Utilized .NET framework to create over 70 automated tests for Cloud Historian team;
      Created, developed, and integrated testing software for RF engineering applications:
      Developed and tested space systems software in all phases ofthe program, including initial
      design, coding, testing, and integration:
 Name of the Project, wwWgithubcomlname                          MON 20XX    MON 2OXX
      Be concise and describe the scope and the work you've done for this project:
      Describe the tasks and achievements to demonstrate your ability to deliver results and your
      problem-solving skills:
      Proiects are a areat wav to show to HR one that vou are passionate about vour work and