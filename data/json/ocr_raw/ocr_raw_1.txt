                                                             JESSICA FRANK
                                                                 ANGULAR DEVELOPER
                                         SUMMARY
CONTACT                                  Highly skilled and dedicated Angular Developer with 4+ years of experience in
    info@@resumekraftcom                 designing and developing  calable web applications. Proficient in utilizing
                                         Angular CLI; Typescript; HTML and CSS,to deliver exceptional user experiences.
   202-555-0120                          Proven ability to collaborate effectively with cross-functional teams and meet
   Chicago, Illinois, US                 project deadlines. Strong problem-solving abilities and collaborative mindset;
                                         adept at working in agile methods
in linkedin com/resumekraft              EXPERIENCE
EDUCATION                                Frontend Developer                                         Dec 2020 . Present
B.E Computer Science                     Ben Tech Solutions
                                                       andmaintained web applications using HTML, CSS,
Engineering                                  Developed                                                 and
Aug2014 -Apr2018                             JavaScript
San Jose State Unlverslty                    Created responsive designs and userfriendly interfaces that enhanced the
                                                 experience
                                             user
SKILLS                                       Collaborated with UX/UI designers to implement visually appealing designs
                                             into the code
Technologles:                                Implemented and managed frontend libraries and frameworks such as
                                             Angular or React
                                             Ensured crossbrowser compatibility    optimized website performance
Angular; Angular Material, HTML 5,                                             and
                                                               testing and debugging to identify and fix any issues or
CSS, Javascript;Typescript,Node              Conducted website
Js, Rest API, Bootstrap, Angular             bugs
Unit Testing; Google Analystics;             Collaborated with backend developers to integrate frontend code with
Mixpanel; Keycloak, Unity WebGL,             serverside logic
SEO, Highcharts                              Stayed updated with emerging frontend technologies and best practices
                and
Methodologies:      Tableau:                 Conducted code reviews and provided feedback to improve code quality
                                             Resolved technical issues and provided support to endusers
Agile and Scrum:                             Documented code and maintained version control using tools like Git
Verslon Control:                         Frontend Developer                                        Oct 2018 Mar 2020
Git.                                     Congruent Solutions
Editor:                                      Developed userfriendly and responsive web applications using HTML; CSS_
                                                 JavaScript.
Visual Studio Code; Tableau:                and.
Other Tools and Technologles:                Collaborated with UX/UI designers to implement visual and interactive
                                             elements on the website_
NPM; Jira, FTP Client:                       Responsive web design and mobilefirst approach to ensure optimal
                                             performance across different devices:
                                             Utilized frameworks such as React js, AngularJS, or Vuejs to build dynamic
                                             and interactive user interfaces:
                                             Implemented AJAX and RESTful APIs to fetch and manipulate data from
                                             backend servers.
                                             Conducted code reviews and provided constructive feedback to ensure code
                                             quality and maintainability:
                                             Optimized website performance by minifying code; compressing images,
                                             and leveragingcaching techniques.
                                             Ensured crossbrowser compatibility and performed testing on different
                                             devices                             ccessibility standards for improved
                                             Ensured the website adhered to web
                                                bility by individuals with disabilities.
                                             usa