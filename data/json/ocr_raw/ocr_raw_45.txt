                              LOUIS LANNISTER
                                    FULL STACK DEVELOPER
                         Inspired .NET Full Stack Developer with 10+vears of
                         extensive knowledgein Back-end Development; DevOps &
                         CI/cD areas with willingness to learn and master NoSQl
                         and Testing, DDDITDD. DevOps = CI/CD expert;
 WORK EXPERIENCE                       EDUCATION
2017-2019 Design Square Inc            2012-2015 University of Columbia
Full Stack Developer                   Software Development
      Added and maintained                  Balanced and met multiple
    backend/frontend tools tOr canvas     deadlines
    and weight based editor                 Maintained  4.0GPA
      Containerised many internal           Worked on vatiety of
    services to docker /dockere
    compose for consistent working        assignments andgroup projects
    environments                            Learned differentcoding languages
      Created cross platform library:
 2015-2017 Tech MortisCorp:              SKILLS & EXPERTISE
 Jr: Developer                               Networking
      Learned how t0 add                     Data Analysis
    backend /frontend tools for              CSS
     canvas                                  PYTHON
      Improved a cross-platform              Time management
     libraryto queue worktasks               CRUD; EFMS SQL Server
                                             SSiS,SSRS SOL
   CONTACT                                 REFERENCES
  1100 South Street                         Anne Pullman
   Chicago                                  CFOat Design Square Inc.
                                            667 007 4545
  955 005 5656                              <PERSON>@hotmail.com                   CSS Instructor at University Of
                                            Columbia
                                            001 500 5000