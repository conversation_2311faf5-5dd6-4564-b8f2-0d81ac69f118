                                           Profile
                                             Full Stack Developer adept in working in both front-end and back-end development
  Ingrid <PERSON>uc<PERSON>                               processes; Highly skilled in programming design development; and implementation
                                             of functional specifications;
   FulL stacK Developer                    Employment History
 Details                                     Full Stack Developer; Excel Technology Group_ Boston
             Drive
 37 Englewood E                                   Performed coding; debugging, and unit testing tasks  support of projects:
 Boston MA 02108                                  Provided ongoing maintenance for assigned applications and systems;
 617.234-9090                                     Applied current programming standards and methodologies to all relevant
 bucur_ ingrdeegmailcom                          projects    activities.
                                                         and _
                                                  Upgraded and maintained both the back-end and front-end systems.
Skills                                           Worked alongside web developers, marketing teams,and engineers to ensure
 Software Development                            the right look and vital features were achieved
                                                  Remained adaptable and committed to stavingon top of growing technologies
 SQL ReacEjs Nade js                             and engineering trends:
                                                  Developed scalable web-based user interfaces and components using Angular;
Complex Problem Solving                          C#and NET corc;
                                                 Increased database admin effectiveness bv 25 percent:
 Design and Development of APIs             Full Stack Developer; $ & C Staffing; Boston
 Development of Serverless
 Architecture                                     Collaborated witha team of engincers, developers, and analysts t0 design and
                                                       leading hiring software:
                                                 create
 Advanced Communication                           Developed and designed front end web architecture with high responsiveness
 Agile Project Management                        of applications:
                                                  Worked with other departments to address improvements; internal tools; and
                                                 implementation issues:
Languages                                         Consistently applied best practices for writing clean; secure, readable, and
                                                 scalable code:
Romanian                                          Participated in meaningful and thorough code reviews.
                                                 Hired, trained, and led         Team of eight full stack developers
 English                                                               successful
                                                 Helped to increase company revenue by 40 percent between 2014-2018.
 German'                                    Education
                                             Master of Science in Computer Science; Boston University, Boston
                                             Bachelor of Science in Software Engineering; Northeastern University;
                                             Boston
                                                             May *010
                                           Certifications
                                             Full Stack Developer Professional Certificate
                                            1011