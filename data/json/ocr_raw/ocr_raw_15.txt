                                 SHUSHRUT
                                 YADAV
PERSONAL INFO                    Backend Developer
Address
New Delhi; India                 Experienced Backend Developer  with strong knowledge of Agile Software
Phone                            Development practices, Rest API; Springboot;   terraform, and Kubernetes:
8027280990                       Proficient in Java EC API; Git; SQL, Design patterns; System design; and CICD
Email                            fundamentals for containerization; refactoring; authentications;  and  re-
shushrutyadav@gmailcom           platforming:
Linkedln                         EMPLOYMENT HISTORY
linkedin com/in/shushrut-yadav
SKILLS                           Backend Developer Jan 2020   Present
                                 Tata Consultancy Services, New Delhi
Object-Oriented Web Applications   Use frameworks to build service-side software and integrate APIs and cloud
                                   computing:  compile  data,
User Interface Design              Analyze and              codes  and processes to identify       areas
Web Development                    improvement and resolve issues_
                                   Work jointly with frontend developers and other team members to set up
JavaScript                         objectives; as well as design cohesive; functional scripts:
                                   Record data and report findings to the appropriate parties:
CSS                                Create security settings, restore and back Up technologies for databases and
                                   websites
HTML                             Backend Developer Apr 2018  Dec 2020
Git                              HCL Technologies, New Delhi
SQL                                Translated application storyboards and use cases into functional
                                   applications:
PHP                                Designed, built; and maintained efficient, reusable, and reliable code:
                                   Integrated data storage solutions {{may include databases, key-value stores;
Python Java                        blob stores; etc:
                                   Ensured the best   possible performance, quality; and  responsiveness of
COURSES                            applications:
                                   Identified bottlenecks and bugs; and devise solutions to mitigate and
Advanced Backend Web               address these issues                   quality, organization,
Development                        Helped  in  the  maintenance of  code                           and
Udemy                              automatization:
2021
LANGUAGES                        EDUCATION
                                 B.Tech Computer Science 2018
Hindi                            Delhi Technical University, New Delhi
English                          Senior Secondary 2015
                                 Delhi Public School, Rohini;, New Delhi