                        MICHAEL WOODROW
                        SENIOR JAVA DEVELOPER
                         ADRESS                                               CONTACT          WEBSITE
                         SOME STREET NO.240 NY BUILDING NUMBER                00 122 4555 666  WAMWRESUMESDOT COM
                        PROFESSIONAL PROFILE
  An accomplished soltware engineer specializing  Java and Java EE soltware design and development with
  extensive experience in the full life cycle of the software design process  including requirements definition,
  protoryplng: proof of concepl design; interace Implementaulon. testing: and malntenance.
SKILLS                             WORK EXPERIENCE
LANGUAGES:                         SENIOR JAVA DEVELOPER
 Java                              DaLaOne                                                     07/2015 Present
 JavaScript                          Designed, developed, documented, debugged, tested and supported the
 Java Swing                         DataOne Java EE Web appllcatlon_
 SQL                                  crenied custom moques services and algorithms within the solutions
 CiC-+                             architecture Monitored ensured resolutions app performance issues
 XMLXSLHTML                          Provided technical expertise for         team ol engineers researching radio
DATABASE:                          frequency identification (RFID) solution for tracking commadities being
                                   loaded and unloaded 0n rallcars:
Hibernate                            Built scalable solutions for JavaWebLogic, Hibemate, MS SQL & Oracle
JDBC                               technologies:
 Oracle                            SOFTWARE ENGINEER
Microsolt SQL                      HGV Solaris, Inc.                                           02/2041 06/2015
Server                                Designed, developed, and tested Java GUI applications for the HGV
SOFTWNARE:                          Solars project
 Eclipse                              Designed and developed system Ior automatic jumpstart Installatlon of
 ClearCase                          the Solaris operating system and custom software onto      network of Sun
 ClearQuest                         machines
Microsoit Visual                      Maintained online AFDI and JEDI software and tested software for
Ct+                                 security standards:
FRAMEWORKS:                           Solved help desk problems and communicaled solulions     customers
                                      Panticipated code reviews and process improvement
Java EE                             EDUCATION
 Spring                             The George Washington University
Stripes                             M,S Computer Science                                       2008 2012
 Struts
JQuery                              Activities and Societies;
AJAX                                  GW Graduate Computlng Club (GCC)
JNI                                   Golden Key International Honour Society
JNDI                                  Institute 0f Electrical and Electronics Engineers (IEEE)
RMI                                 CERTIFICATIONS
SOAP
WEB APP SERVERS:                    Oracle
JBoss                               Oracle Certification Java Professional Java SE Programmer 2017
WebLogic                            Oracle Certified Expert , Java EE         Web Component Developer 2016
 Tomcat