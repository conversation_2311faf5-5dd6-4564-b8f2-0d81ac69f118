<PERSON>
JR SOFTWARE ENGINEER
 SUMMARY                                                                                CONTACT
 Software developer with 15+ months experience in Ruby and various machine              <EMAIL>
 learning tools. Aiming to actively contribute to the growth of an organization         202-555-0120
 where my analytical, functional and technical skills could be utilized to the fullest  Chicago, Illinois, US
 extent_                                                                                linkedin com/resumekraft
 EXPERIENCE                                                                             SKILLS
 JR SOFTWARE ENGINEER                                                                   Ruby
Copart Indla Technology Centre  Aug²⁰¹⁸ Present                                         Twilio - ML Tools
 PROJECT: Copart Connect-Checklot 2.0 (US)                                              Amazon Lex & AWS
    Description: Build an IVR system for the company with the feature of
    identifying caller details beforehand.                                              Google DialogFlow
    Environment: Ruby,NodeJS                                                            WebTechnologies (HTML, CSS3
    Tools: <PERSON><PERSON>lio, RubyMine,Postman                                                     Node js, JavaScrpt)
    Version Control: Git                                                                PHP
 PROJECT: Copart Connect-Checklot 1.0 (US & UK)                                         Advanced & Core JAVA
    Description: Build IVR system for the company providing basic details               MySQLIPL SQL
    leveraging the work for yard representatives_                                       C, Ct+
    Environment: NodeJS                                                                 Git Version Controlling
    Tool: Twilio
 EDUCATION
 PG- D<PERSON><PERSON>OMA IN ADVANCE COMPUTING
 San Jose State University Feb 2018 - Aug2018
 BE COMPUTER SCIENCE
 Northeastern Unlversity Jul 2013 - May 2017
 PROJECTS
 Netafim Irrigation India Pvt Ltd, Vadodara
 Intern Software Engineer
 PROJECT: Downtime Tracking Portal
    Description: Developed an online portal from the grass root level to provide a
    systematic method to track the Downtime of the Network across all the state
    branches of the company in India
    Environment: PHP, JQuery, Javascript, Mysql, LDAP
    Tools: SublimeText; Notepadt+