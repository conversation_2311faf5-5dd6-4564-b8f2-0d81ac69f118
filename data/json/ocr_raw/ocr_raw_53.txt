                                                                       Full-Stack Developer | Software Engineer
 About Me
 am a Full-Stack Developer with 7 years of experience in developing software for astrophysical research: During my
graduate research; leveraged advanced simulations and analytical techniques to produce groundbreaking results in the field
of astrophysics; providing new insights into planet formation: While valued the problem-solving; public-speaking  &nd
 communication skills that academia provided, | discovered a passion for software development: Throughout my studies,
 became proficient in HTML, CSS, SCSS, Javascript; Reactjs,and Node js, and have since continued to hone my skills through
various projects and experiences: am now seeking a position at an ethically driven company where can apply my expertise
in software development to create meaningful solutions:
 PROJECTS & EXPERIENCE
 Research Assistant; Department of Astronomy- Python | Fortran90 | C++                                     2019 2023
        Conducted simulations that produced over 100 GBs of data and ran on over 2000 processors, utilizing my
        knowledge of shell-scripting to manage and submitjobs to a High-Performance Computing center:
        Utilized Python and Ct+ to generate publication-grade data visualizations that effectively communicated the results
        of our simulations to peers and enabled scientists to make informed statistical decisions regarding their analysis of
        real-world data_
        Successfully reproduced the density trend of Kuiper belt objects in a project that was awarded the NASA Emerging
         Worlds grant and led to the acquisition of a Master'$ of Science degree:
 Human Rights First: Asylum Report Generator
 Full-Stack Developer React Axios GitHub Website                                                                  2023
        Improved functionality of UI/UX by developing new features that provide users with valuable statistical information
         on asylum office grant rates:
        Fixed critical bugs that were preventing users from accessing visual tools designed to provide insights on
        demographic trends and acceptance rates of asylum office grants This resulted in a more reliable and user-friendly
        experience for all users.
 Foothills Property Group
Full-Stack Developer HTML SCSS | Javascript PHP | GitHub | Website                                             2022-2023
        Developed a visually stunning custom WordPress theme using HTML, SCSS, JavaScript, and PHP to significantly
        elevate the company'$ online presence, resulting in a more engaging and memorable user experience for visitors The
            r~friendly UI/UX,designed using Figma, received widespread praise from clients and stakeholders; positioning the
         user-
        company as a leader in their industry Additionally; | leveraged SEO tools to optimize the website for search engine
        performance; resulting in increased visibility higher search rankings, and a substantial boost in traffic:
 TECHNICAL SKILLS
 Front End: HTML, SCSS,JS,React; Redux/ReduxToolKit; Context API;, Yup, styled-components, Bootstrap; MaterialUl
 Back End: Node js, Express, PHP Flask; Django, SQL, MongoDB, Heroku; Jinja, Knex; SuperTest
 Additional: Python; C++,Git CLI, GitHub, VS Code; Debugging; Jest, Cypress, Deployment; Latex; English; Spanish
 EDUCATION
Full-Stack Web Development Program                                                                            March 2023
Astronomy MSc                                                                                              February 2023
 Physics B.S_ Astrophysics BS:_                                                                                 May 2018