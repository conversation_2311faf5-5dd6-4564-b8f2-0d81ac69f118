               <PERSON> Brandon
                MERN STACK DEVELOPER
        info@resumekraft com     202-555-0120      Chicago, Illinois, US  il linkedin com/resumekraft
      SUMMARY                                                             SKILLS
Highly skilled MERN Stack developer bringing X years of experience in     Responsive Web
designing and developing robust web applications_Proficient in front-     Design          E
end and back-endtechnologies, including React js, Node js, MongoDB        Back-end and
and Expressjs. Strong understanding of responsive design principles       Database skills
and cross-browser compatibility_ Proven ability to meet tight deadlines   Front-end Skills
and deliver high-quality code_ A collaborative team player, dedicated to  HTML; CSS
ensuring seamless user experiences and designing scalable solutions.      Analytical thinking
                                                                          Communication Skills
      EXPERIENCE
MERN Stack developer                                                       LANGUAGES
Accenture Mar 2019   Present                                              English
   Experience indeveloping   maintaining web applications using
   MERN Stack (MongoDB,  and      React:js, Node.js)                      French
                        Express js                                        Arabic
   Proficient in frontendtechnologies such as HTML, CSS,and               German
   JavaScript
   Strong knowledge of database design, implementation, and
   optimizationusingMongoDB                                                EDUCATION
   Experience in designing and developing RESTful APIs
   and Express js                                 using Node js           Diploma (Polytechnic)
   Proficient in React js for building user interfaces and state                polytechnic
   management                                                             Government
   Familiarity with modern frontend build pipelines and tools             kolhapur
                                                                          Jun 2019 May
   Experience in implementing testdriven development (TDD) and                    2020
   writing unit tests using frameworks such as Jest or Mocha              Bachelor of Engineering
   Ability to debug and resolve issues inexisting codebase                              technology
   Strong problemsolving skills and attention to detail                   Sinhgad institute of
   Collaborate with crossfunctional teams to define_design, and ship      Ionavala
   new features                                                           Jun 2021 Jun 2023
   Conduct code reviews and provide constructive feedback for team
   members                                 bring
   Stay updated with emerging technologies and   new ideas t0
   the development team
            agile development environment, attending daily standup
   Work in an
   meetings and providing updates on progress
            application  optimized for maximum performance and
   Ensure the
   scalability
      PROJECTS
Amazon e-commerce website clone
Design front-end project of Amazon clone website