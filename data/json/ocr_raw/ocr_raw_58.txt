 AJAY MEHTA
 JavaProgrammer & Software Developer
 Phone: 9999888877
 Email: ajay@email com
          gitcom/me
 Git Profile:
 Address: 234, Ashok Nagar, Delhi 110009
 Qualified JAVA developer, with experience in software design; development and maintenance of java enterprise applications Ability to
 analyze user needs  software requirements to determine feasibility of design with strong approach t0 perform testing debugging
 processes within se= timeframes & cOSt constraints:
EDUCATION
 2018                       B. Tech (Information Technology)  JIMS ENGINEERING MANAGEMENT AND TECHNICAL CAMPUS
 2014                       Higher Secondary   Holy Angels Model School, Delhi
 2012                       Senior Secondary  Holy Angels Model School, Delhi
EXPERIENCE         INTERNSHIP
                           Java Programmer    AB Software Solutions, New Delhe
                             Design and implement scalable    user-friendly modules for healthcare software including Registration &
                              Appointment module, Surgery work bench ad Anesthetic work bench modules in compliance with the
                              pre-designated specifications
                              Complete module development ad testing by coordinating requirements, schedules, and activities
                              Troubleshoot development and production problems across multipl environments & platforms
                              Create as well as execute unit test and test plans. Support continuous improvement and addition of new
                              features by investigating alternatives and presenting these for review by senior developers
                              Ensure the best possible performance; quality, and responsiveness of the modules; identify bottlenecks
                              and bugs, and devise solutions to these problems while maintaining high quality standards
                              Monitor current  future trends, technology and information that will positively atfect organizational
                              projects; pplies and integrates emerging technological trends t0 new and existing systems architecture
                              Contribute to team meetings collaborate with senior management to discuss various modules'
                              specifications and translate business requirements into technica solutions
PROJECTS
                            Android Based Currency (Recognition system for Blind)
                              Created with the objective of helping the blinds in detecting the currency denominations easily
                            Career Epilogi (Career guidance system)
                              Developed  career guidance system that provided available career options according to aptitude test
                              results. Technology: PHP, Java  Backend: MySQL
PROFESSIONAL ENHANCEMENTS
                              Course: Android Development
                              Training: Java Training Program
                              Attended  day workshop on Android
SKiLLs      TECHNOLOGY           FUNCTIONAL )
        Java Programming   Troubleshooting and Bug fixing     Testing and QA  (8, 7}, Ubuntu  C C++ MySQL   HTML; PHP
EXTRA-CURRICULAR
                            Volunteering
                              Volunteered for awareness program on "Women\'sDay organized at Kanjirapally in March 2013
                              Participated in various Intercollegiate fests and acted as NSS volunteer at AmalJyothi College of
                              Engineering
                            Participations Sports
                              Coordinator of thecollege photography competition FOCUZ held in March 2014
                              First Prize winner at inter-collegiate skit and third prize winner at college Dance competition