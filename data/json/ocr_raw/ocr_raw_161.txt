<PERSON>
UI DEVELOPER
 SUMMARY                                                                             CONTACT
  have around 7.5 years of experience in the IT industry Web development am          <EMAIL>
 an aspirant in development of Responsive, Adaptable & Multi-platform User           202-555-0120
 Interface for Websites Experience  With problem identifications and resolution in   Chicago, Illinois, US
 both developmental and production environments My core strength lies in my          linkedin com/resumekraft
 ability to translate customer requirements into creative solutions with effective
 communication and experience in theday"todayactivity of profession                  SKILLS
 EXPERIENCE & PROJECTS                                                               Angular 2-6-7
 Associate Consultants                                                               Bootstrap Framework and
 Delta Technologies    Jan 2016  Present                                             Material design
    Jio Money Ops Portal is SPA (Single Page application) for daily business         Nodejs
    need: It consolidates data from multiple systems and provides a better way       JavaScript
    to manage different entity (Customer; Merchant; Dealer, and Business Users)  Jquery
    from Onboarding to Termination
    Jio Payment Bank Process Monitoring tool: This application tracks all            HTMLS
    the information in real-time about every stage (failed, pending; approved etc:)  CsS3
    of an activity and provides information in one place. (This Project Used
    Technology - angular-7,nodeSql, unit tests via Karma )                           LANGUAGES
    Active Space Data Explorer Ul: This project aims to provide an
    alternative solution for MDM database Ul:                                        English
    Heart Beat Monitoring: This Project provides live details of allthe services     French
    used in jio 4G jiomoney and jio payment bank: (This Project Used
    Technology-Javascript Html5,Css3 )                                               Arabic
 Tool Developed:                                                                     German
    Created Request interceptor that intercept request; responseᵃⁿᵈsave the
    response for producing the same when service is not available.                   CORE COMPETENCIES
    Developed tool to deploy and Nodejs, Java and Static code to test server for     Technical Expertise:
    testing before moving them to higher environment                                 Operating Systems :Linux
    Created various prototypes, responsive web (html) pages for portal               (Ubuntu), Windows:
    development
    Strongly contributed in converting existing application into New Responsive      Web Servers Apache HTTP ,
    Design with help of Twitter Bootstrap Framework:                                 Tomcat 7 .
    Jio Money is digital wallet Jio Money' s core product and the end to end         PHP Frameworks
    systems that support it are operational                                          Codelgniter:
    Customers can use Jio Money's digital wallet, a Jio Money card or any other      Databases MySql
    creditldebit card directly from the wallet to pay merchant seamlessly and
    instantly. Jio Money digital wallet will also give consumers the option of
    opening a Digital Bank Account or linking their existing account This Project    EDUCATION
    Used Technology:Java, Spring; Java script; JQuery, Bootstrap Business
    Works, Business Process Management Agile Methodologv: Daily Scrums               MCA
    and sprint releases IDE: Eclipse Server:Tomcat Database Business Process         San Jose State University
    Management Client:Reliance Industries Ltd. Role & Responsibilities: L3           Mar 2011 May 2014
    Rescores Production and Coding & Designing the User Interface, Coordinate        Annamalai  University
    with support and fulfillment team to resolve client issues:                      B.C.A