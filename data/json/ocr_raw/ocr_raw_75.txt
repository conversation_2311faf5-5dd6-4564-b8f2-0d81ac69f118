              RICHARD EDWARD
                   MEAN STACK DEVELOPER
SUMMARY                                                                       CONTACT
A collaborative team player with  track record of delivering multiple
concurrent change requests in fast-paced environments. Seeking an                info@resumekraft com
opportunity to switch skill set towards MEAN stack development and further       202-555-0120
improve HTMLS and CSS3 skills as the future full stack developer: Flexible       Chicago, Illinois, US
and can adapt to different work environments Ability to maintain high         in linkedin com/resumekraft
standards of accuracy and work under pressure
EXPERIENCE                                                                    SKILLS
Associate Engineer                                 Aug:2018 - Apr 2020           Angular
Atos                                                                             NodeJs
    Successfully ran test cases to upgrade PCRF platform along with the          Mongo DB
    integration of Development and environment teams_
    Handled Various drops of Volte service for customer:                         Mongoose
    Performed API Testing in porting for migrating customers from one            Express
    mobile network to other:                                                     Java script
    Improved durability; reliability and quality by conducting thorough quality  Type '
    assessments and recommending change:                                           Script
    Kept records such as work logs,customer feedback; product malfunction        Manual Testing
    repairs and quality reports safely stored, in-line with company policies_    Functional Testing
PROJECTS                                                                         Regression Testing
                                                                                 Unit Testing
Ecommerce Website                                  Dec 2020 - Feb 2021           API Testing
Angular Developer
    Implemented Various concepts of Angular using Firebase as backend:        LANGUAGES
    Used Routing Module for navigating users
    Used Authentication and Authorization to specify the roles for currently  English
    logged in user:                                                           French
    Used Auth-Guard concepts to secure routes:
Movie rental Application                           Aug2020 Oct 2020           Arabic
Backend                                                                       German
    Explored the features and flexibility of Nodejs along with express        EDUCATION
    framework:
    Used Mongo db for storing data and explored the key concepts of
    authorization and authentication:                                         BE
    Learned how to use different libraries like Joi, Morgan, config and many  Aug2014 - Jun 2018
    more on personal interest:                                                San Jose State University
    Implemented Unit and Integrated testing concepts using Jest:
    Explored the differences between TDD and BDD and implemented the
    same:                                                                     HOBBIES
Chat ApplicationusingNode-js and                    Oct 2020 - Oct 2020       Dancing