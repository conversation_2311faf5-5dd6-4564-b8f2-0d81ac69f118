                                            KYLIE LILLIAN
                                        FREELANCE UI DEVELOPER
 <EMAIL>      ******-555-0114     Manhattan, New York, US  il https:I wWW linkedin com/in/kylie
       SUMMARY                                                      SKILLS
Enthusiastic computer scientist eager to contribute to team     Technologies Android, Selenium, Django,
successthrough hard work, attention to detail and excellent     Bootstrap, GitHub
organizational skills. Proficiency in several programming       Languages Python, Java, MySQL,C++
languages Motivated to learn,growand excel in the real world.   HTML, CSS
Aspiring inDeveloping new, and improving existing; computer-    IDE Pycharm, Intellij idea, Android Studio,
basedtechnologies, systems, and solutions_                      NetBeans,; Visual Studio
       OBJECTIVE                                                Soft-Skills Adaptability, Verbal
                                                                Communication, Creativity, Team-oriented,
                                                                Problem Solving
Self-motivated student with the ability to create new ideas to
foster thegoalsof a company: Desiring togainan internship           HOBBIES
position to utilize my abilities and skills in ensuring
efficiency and learn at the same time to gain experience and  Computing
further my knowledge in the field of computer science:        Coding
        EDUCATION                                             UI Designing
                                                              Blogging
Computer Science                                                    CERTIFICATION
University of Southern California  Oct 2017 Present
Expected graduation May 2021                                  Python and Java Programming Training
        EXPERIENCE                                            WAAW Foundation 2019-07-22
                                                              Learned to be familiar with basic
Self Employed                                                 programming concepts
Front-end Developer  Jul 2018  Present
Technology    Html,; CSS, Bootstrap, PHP, MySQL
Delivered performance-driven and user-centric website which
aims to enlighten the world about the latest technology
revolution ranging from how to tackleproblems without
consulting a professional for help and also to present the latest
technology news to the doorsteps of its
Self Employed    File Manager Utilities
Back-end Developer Nov 2019     Present
Technology   Python, Tkinter, GitHub
    Successfully implemented scripts to imitate the extensive
    functions of a robust file management system: