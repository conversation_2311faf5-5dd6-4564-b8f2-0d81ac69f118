     NIKHIL ARORA
    UX/UI Developer
    Talented UI/ UX Manager with expertise in developing      friendly Ul and reviewing          PERSONAL INFO
                                                        User-
    existing applications for UX effectiveness Skilled   creating and  implementing              Address
interaction models, user flows, and user interface specifications as well as generating          Ahmedabad, India
    design wreframes and     specs: Experienced   in creating      User experience that          Phone
    produces outcomes and connects emotionally from start to finish:                             9289949522
     EMPLOYMENT HISTORY                                                                          Email
     2019       UX/UI Developer                                                                  nikhilarora@gmailcom
     Present    Uplers, Ahmedabad                                                                WEBSITES & SOCIAL LINKS
                   Translate concepts into user flows, wireframes and prototypes that lead
                   to intuitive user experiences                                                 Behnace
                   Assist in identifying design problems and devising solutions that help users  SKILLS
                   easily solve problems
                   Assist in applying custom application controls and iconography.               UX & UI Design
                   Assist   simplifying and turning complex information into  intuitive
                   graphics, charts, graphs, and other forms of visual representation
                   Assist    creating rapid prototypes to validate design concepts with          Usability Protocols
                   stakeholders and customers
                   Contribute in building  user experience pattern library, to maintain
                   consistency in behaviors acrossmultiple products.                             Web Site Analytics
     2016       UX/UI Developer
     2019       ShikshaTriangle, Ahmedabad                                                       Prototyping
                   Worked with a small, tight-knit team in the design ideation process_ and
                   translated game design ideas and concepts into clear user flows and           Persona Creation
                   wireframes
                   Generated interactive prototypes to demonstrate   user flows     and
                   complex functionality.                                                        Affinity Diagramming
                   Developed look and feel of <PERSON><PERSON> and ensured consistent design aesthetic
                   and functionality throughout the product
                   Provided and iterated mockups of new features andscreens_ Partnered           Animation/Motion Graphics
                   with engineering team to implement designs
                   Participated in user research and translated observations into improved
                                                                                                 Photoshop/Illustrator
    EDUCATION
     2017       Master of Computer Applications                                                  Requirements Definition
                VIPS
     2015       Bachelors of Computer Science                                                    LANGUAGES
                Indian Institute Of Technology Delhi                                             Hindi
    COURSES
                Coursera UX/UI Design Specialization                                             English
                Skillshare Introduction to Axure RP for UX/UI Designer
    PROJECTS                                                                                     Korean
                Projects Web App Design
                   Developed the app's signup flow , in-app displays,and dashboard,
                   presenting the completed work as a case study with sketches_
                   wireframes, and a high-fidelity prototype mocked up on a device_