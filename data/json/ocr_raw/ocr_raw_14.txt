                                       MASON WILSON
                                               Angular Developer
                        support@qwikresume com   (123) 456 7899  LosAngeles   wwwqwikresumecom
  PROFESSIONAL SUMMARY                                                  SKILLS
  Innovative Angular Developer with 10 years of experience in building  N/a
  responsive web applications Skilled in Angular; TypeScript, and
  RESTful APIs, focus on delivering high-performance solutions that     Angular Development
  meet user needs: My strong collaboration with design and
  development teams enhances project efficiency and drives the
  successful implementation of cutting-edge technologies:               Restful Services
  WORK EXPERIENCE                                                       Dependency Injection
  Junior Angular Developer                       Jan / 2019-Ongoing     Routing
  Quantum Solutions LLC                                4 Phoenix AZ
  1 Adapted user interfaces for modern web applications utilizing
   Angular and TypeScript
 2. Designed and developed scalable application architecture to         INTERESTS
   support future growth:
 3. Develop dynamic web applications using Angular framework and           Art                    Volunteering
   TypeScript:
4. Conducted thorough performance testing to ensure application            Hiking                 Yoga
   reliability and speed
 5. Collaborated with UXIUI designers to create intuitive user          STRENGTHS
   experiences:
 6. Optimized existing Angular codebase, enhanc   maintainability
   and performance:                           cing                          Criticality     Detail-orientedl
  7. Participated in code reviews and mentoring junior developers to
   foster team growth:                                                       Diplomacy        "Enthusiasm
  Angular Developer                           Jan / 2015-Jan / 2019
  Silver Lake Enterprises                               Seattle; WA     LANGUAGES
 1 Developed and maintained high-quality Angular applications for
   Allstate Corporation:
 2. Utilized RESTful services for seamless backend integration with       English        Swahili       Japanese
   Angular applications
 3. Created responsive web pages using AngularJS, HTML, CSS, and
   Bootstrap.                                                           ACHIEVEMENTS
   Implemented dynamic features using Angular components and
   services to enhance user interaction:                                     Led the development of a high-traffic web
 5. Developed custom directives and components for reusable UI              application, improving load times by 30%_
   elements
                                                                             Implemented a new component library
                                                                            that reduced development time by 25%.
  EDUCATION
  Bachelor of Science in Computer Science           Jan / 2012-Jan / 2015
  Tech University                                       1 Phaenix AZ
  Focused on software development methodologies and web
  technologies:
  Powered by  Qwlkresume                                                                              WWW.qwikresume.com