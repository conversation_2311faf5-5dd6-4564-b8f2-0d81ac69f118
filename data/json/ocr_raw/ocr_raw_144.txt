Shashi                                  66
 A<PERSON>arwal                                 Software Engineering graduate with fundamental knowledge of software
                                           design, development and testing: Prior experience in Software Testing
 Software Engineer                      Automation, designing developing; and implementing test plans, test cases,
                                           and test processes Stronginclination towards exploring; learning and
                                               understanding newer business domains and new technology:
  9806754930
  <EMAIL>              Software Engineer                                 Mar 2017 Aug 2021
                EMPLOYMENT              HCL
                HISTORY                 Mumbai
                                             charge of gadget development
                                           Developed new functionality for the kernel in many areas
                                           Collaborated with other teams on the implementation of certain kernel
                                           features(e.g: tool development for said features)
                                           Identified and fixed kernel problems that affect Chrome OS platforms
                                           and functionalities
                                           Participated in feature planning meetings, cross-team meetings to
                                           cooperate with other developers, and manage third-party contractor
                                           and vendor relationships as needed:
                                        Software Engineer Intern                         Feb 2020 Dec 2020
                                        Capgemini
                                        Mumbai
                                           Assisted the Network Management Systems team with documentation,
                                           specifications, designs, test plans; proposals, software design;
                                           development; and testing
                                           Worked closely with the team in early product phases to facilitate agile
                                           development methodology
                                           Participated in software development using Agile & Scrum development
                                           process
                                           Assisted in defining automating; andexecuting development tests in
                                           support of the feature/functionality being developed
                EDUCATION               B. Tech                                          May 2016 Jun 2020
                                        Don Bosco
                                        Mumbai
                                        I2th CBSE                                        May 2014  Apr 2016
                                        St. James School
                SKILLS                  Java; Automated Testing, Node js development; Ul Design; JavaScript; UX
                                        Design; Debugging; Manual Testing; Scrum
                PROJECTS                Poker Simulation
                                           Built a full stack web app to allow users to visualize outcomes of pokers
                                           against opponents_
                                           Used knowledge in python to simulate different outcomes under
                                           different scenarios_
                                        Cryptocurrency Price Tracker
                                           Incorporated API calls to several applications and stored data efficiently
                                           Utilized D3js to allow users to dynamically visualize
                                            over time periods.                          price movements
                LANGUAGES               Hindi,English