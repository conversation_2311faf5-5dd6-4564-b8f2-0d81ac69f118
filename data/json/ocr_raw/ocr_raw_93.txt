  HARPER LEWIS
  Senior PHP Developer
    <EMAIL>   (123) 456 7899  Los Angeles
     WWW qwikresume.com
      PROFESSIONAL SUMMARY                                                     SKILLS
  Seasoned Senior PHP Developer with 10 years of experience in             Advanced Php Development
  designing and implementing high-performance web applications_
  Proficient in PHP, MySQL, and modern frameworks; am skilled at           Authorization
  optimizing application performance and enhancing user
  experiences. My leadership in project execution and team                 Data Migration
  collaboration drives innovative solutions that align with business
  objectives
      WORK EXPERIENCE                                                      Server Management
                                                                           Linux
  Senior PHP Developer                            Feb / 2019-Ongoing
  Maple Leaf Consulting                                 Toronto, ON
   Designed and developed client websites utilizing PHP, MySQL,                 INTERESTS
   JavaScript, and Laravel framework, enhancing functionality and             Home Brewing
   user experience_                                                                              Wildlife
 2. Implemented AJAX and JSON to streamline data handling and                                    Conservation
   improve page load times_                                                   Running            Public Speaking
 3. Expanded product features and functionalitiesusingdiverse
   programming languages and frameworks.
 4. Reviewed project requirements to create detailed technical
   specifications, ensuring project alignment with client needs:                STRENGTHS
 5. Led WooCommerce development projects, building scalable e-
   commerce solutions and optimizing performance_
 6. Created dynamic websites using Laravel 5.2 framework; focusing             Willingness      Wisdom
   on responsive design and user engagement:
  7. Documented code development processes and maintained version              Zeal     Ingenuity
   control for seamless collaboration:
  SeniorPhpDeveloper                        Feb / 2015-Feb / 2019               LANGUAGES
  Gactus Creek Solutions                            4 Phoenix, AZ
 1. Utilized AWS services for deploying and managing scalable web
   applications_
 2. Developed new features and resolved bugs for WWWokaziiro,
   enhancing user engagement on a leading e-commerce platform.
 3. Engineered various projects, including an algorithm for optimizing       English      Arabic      Swahili
   product recommendations based on user data.
 4. Participated in critical programming decisions, focusing on
   performance tuning and caching solutions                                     ACHIEVEMENTS
 5. Employed tools such as SVN and Git for version control, ensuring
   code integrity and collaboration:                                           Increased application performance
                                                                               by 30% through code optimization
      EDUCATION                                                                and database tuning:
                                                                               Led a team of developers to deliver
  Bachelor of Science in Computer                 Feb /  Feb                   a complex e-commerce platform
  Science                                         2012   2015                  within a tight deadline.
  State University                                      Phoenix AZ
  Focused on software development, database management; and web
 technologies
Powered by  Qwlkresume                                                                             mwWqwlkresumne comi