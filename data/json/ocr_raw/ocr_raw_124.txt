                                      ANDREW ALPHINS
                                                     Python Developer
                                                    Atlanta, GA 30306
(555) 555-6789                                                                                      alphins@email
                                                                                                                     com
                                                      PROFILE
      Skilled and innovative Python developer; proficient in creating robust and scalable applications. Experienced in analyzing;
    designing;   limplementing codearchitecture. Demonstrated expertise in optimizing   limproving application performance:
             and                                                                        and
    Detail-oriented with & passion for writing clean and maintainable code. Proven track record of delivering high-quality software
    solutions within deadlines Excellent team player with effective communication and collaboration skills. Strong problem-solving
                                capabilities and                                         technologies:
                                                continuous learner of emerging
                                          EMPLOYMENT HISTORY
    Python Developer; Airbnb                                                                             Jul 2019  Present
                                                                                                                Atlanta, GA
      Developed and maintained Python-based web applications, improving system performance by 25%.
      Collaborated with cross-functional teams to design and implement new features and functionalities.
       Conducted code reviews andprovided mentorship to junior developers, fostering =  culture of continuous
     learning and innovation.
    Software Engineering Intern, Dropbox                                                              Jun 2018  Aug =²⁰¹⁸
                                                                                                        San Francisco, CA
       Assisted indevelopment of Python scripts for dara processing
                                                          andauromation;
      Participated in Agile development processes; contributing to sprint planning and retrospectives:
      Implemented unit tests and conductedbugfixes, ensuring high-quality codedelivery:
                                                   EDUCATION
    University of California                                                                          Aug :²⁰¹⁵  May 2019
 Bachelor of Science in Computer Science                                                                       Berkeley; CA
                                                       SKILLS
 C++                                                            React
 Python                                                         Git
Django                                                          PHP
Java                                                            HTML & CSS
                                                                       Solving          Analytical Skills
JavaScript                                                      Problem-=               and
Node:js                                                         Communication and Collaboration