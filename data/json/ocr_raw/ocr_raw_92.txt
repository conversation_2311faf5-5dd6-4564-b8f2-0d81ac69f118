  SOPHIA BROWN
  Backend Engineer
    support@qwikresumecom   (123) 456 7899  LosAngeles
        qwikresume
    WWW:          com
     PROFESSIONAL SUMMARY                                                  SKILLS
  Results-oriented Backend Engineer with 7 years of experience in       C#
  crafting and optimizing high-performance APIs and backend
  services Adeptalutilizing modern database Lechnologies to             Gcp
  enhance application efficiency and system reliability Committed
   fostering collaboration across teams t0 deliver innovative
  t0                                                                   Performance Tuning
  software solutions that meet both technical specifications and
  businessgoals.
      WORK EXPERIENCE                                                  Security Best Practices
  Backend Engineer                            Mar /2021-Ongoing        Authentication
  Quantum Solutions LLC                           4 Phoenix,AZ
  1.Designed and developed scalable backend systemsusingJava                INTERESTS
   and Spring Boot, ensuring optimal performance and relabilicy:
                            leading                                             Brewing     Wildlife
 2. Implemented RESTful APIs,       [Oimproved integration                Home
   with front-endapplications and third-party services                                      Conservation
 3. Optimized database interactionsusingSQL and NoSQL                     Running
   technologies, significantly enhancing data retrieval speeds                              Public Speaking
4. Collaborated with DevOps teams to automate deployment
   processes usingCICD pipelines
 5. Write clean, maintainable,and efficient code following best             STRENGTHS
   practices
 6. Analyzed system performance metrics to identify bottlenecks            Willingness      Wisdom
   and implement effective solutions
  7. Documented backend processes and architectural decisions to
   maintain project transparency and team alignment                        Zeal     Ingenuity
  Backend Engineer                           Mar / 2018-Mar 2021             LANGUAGES
  Crescent Moon Design                            4 Portland, OR
  1 Participated in agile development practices, contributing t0
   sprintplanning and retrospectives to enhance team
   productivity:
 2.Developed and maintained backend applications using Nodejs
   and Express; ensuring high availabilicy and scalability
 3Designed and implemented microservices using Docker and               English     Italian      Arabic
   Kubernetes, facilitating seamless deployment and scaling:
 4. Collaborated with cross-functional teams t0
                                          gather
   requirements and translate them into technical specifications             ACHIEVEMENTS
5. Utilized Git for version control, ensuring code integrity and
   facilitating team collaboration                                         Successfully reduced API response
                                                                           time by 40% through optimization
     EDUCATION                                                             of backend processes
                                                                           Implemented a microservices
  Bachelor of Science in Computer              Mar /  Mar /                architecture that improved system
  Science                                      2015   12018                scalability by 30%.
  University of California                         Portland, OR
  Focused on softwaredevelopment; algorithms, and database
  management
Powered by Qwlkresume