                  RACHEL FRANK
                      BACK-END DEVELOPER
SUMMARY                                                                           CONTACT
     amaself-reliant, dependable,and pproachable individual who is
    dedicated to pursuing excellence in my professional journey: am                 info@resumekraft com
    committed to working diligently to attain my goals. My adaptability and         ************
    strong organizational skills allow me to quickly grasp and master the latest    Chicago, Illinois; US
    web and software technologies:
    Adaptable quickly; and organized well. Interested in learning the latest web  in linkedin com/resumekraft
    & softwaretechnologies quickly:
 PROJECTS                                                                         SKILLS
Cinema App                                              Jun 2023  Jul 2023        C++
    Developed in PHP      MySql:
                     and                                                          Algorithm
    This application is developerd for booking cinema tickets and watching
    trailers in the website_                                                      Java
    Clients whom visits the site can ask the admin in FAQ for any kind of
    questions can book tickets rate movies can select movies by genre etc.        OOP
    The administration side is intentionally designed with two distinct roles:
    administrators who have access to the specific site they are responsible
    for; and superadmins who oversee all users and have the authority to          JavaScript
    manage bans and blockades across the entire platform and admins that
    menages one page and cant update or delete other users taht has the           node js
    same roleslikehis but superadin is just one person and can delete users
    admins has teh freedom to perform any CRUD operation in
                                                           anyusers_              Python
Base64                                                 Feb 2023  Mar 2023
    Developed in python
    Base64 is an form of encoding that uses text transmit data_                   react js
    Base64 is used to encode the all form of textslimages in inbox and send to
    another users with this form of encoding for security issues                  PHP
    The development approach closely resembles the Python base64 class
    but with the encoding and decoding logic hard-coded into the system           Engineering
White Stenography                                      Feb 2023  Mar 2023         IOS
    Developed in python:
    White steganography is employed to send covert messages that remain           MySql
    incomprehensible to third parties_
    The underlying logic of this encryption system is as follows: Each space      MongoDB
    represents a 0 bit; and a tab represents a bit; When concatenated
    together; they are converted into ASCII characters, ultimately forming a
    coherent sentence_
CPU                                                    Oct 2022  Dec 2022         LANGUAGES
    Developed in Verilog                            Verilog                       English
    CPU 16 bits is developed with all the functionality in and it can be          French
    used for sum; substraction, product and divison of numbers with 16 bits