<PERSON> Michael                                                                CONTACT
REACTJS DEVELOPER                                                               info@resumekraft com
                                                                                202-555-0120
SUMMARY                                                                         Chicago, Illinois, US
                                                                               in linkedin com/resumekraft
 Experienced ReactiS Developer with                    building
                                   strong background in                        SKILLS
                        applications. Proficient in front-end
 efficient and scalable web       JavaScript Skilled in creating
technologies, such as HTML, CSS, and                                           Php(Hypertext Preprocesser)
 reusable and modular components_optimizing performance, and ensuring
cross-browser compatibility: Passionate about staying updated with the         JavallavaFXJSP_)
latest trends and best practices in ReactJS development: Shown expertise
   collaborating with cross-functional teams to deliver high-quality software  Android
 solutions;
EXPERIENCE                                                                     Bootstrap
ReactJs Developer                                    Jul 2019 - Sep 2019       WordPress
 Accenture Inc
    Developing user interface components using React js concepts,              MySQL
    libraries; and frameworks
    Building
            reusable components and frontend libraries for future use          HTML CSS | JS
                                         highquality code
    Translating designs and wireframes into                                    Networking
    Optimizing components for maximum performance across    vast array
    of webcapable devices and browsers
    Conducting code reviews and ensuring best practices and standards          EDUCATION
    are followed
    Collaborating with team members and stakeholders to define and             Software Engineering
    implement features
    Participating in architectural and design decisions                        Sep 2016 - Jan 2021
    Troubleshooting; debugging; and resolving software defects and issues      San Jose State University
    Keeping uptodate with the latest industry trends best practices_ and
    emerging technologies in frontend development and Reactjs
    Mentoring and guiding junior developers in the team
    Collaborating with backend developers, designers, and other
    stakeholders to seamlessly integrate frontend and backend
    functionalities
Software Engineer(Reactjs)                          Mar 2016 Apr 2019
Infosys
 Laminaar Aviation Infotech, as contract; based worked for this company
Worked as a Ul Design    Development on an ARMS project:
                     and
Projects: ARMS Service, ARMS Insight Development; and LNT Web
 Application:
 1. Common Admin NG Project: May 21 t0 dec 22
Brief: Converting Arms Service Project into React There are almost 100+
                 applications and we 12+ Reactjs developers have
 forms in Windows
 converted them into React applications: s0 when the plane time has come,
 when it will go time departureand Auditing; like these kinds of work have
done using functionality, API integration.
 Role: Module Developer