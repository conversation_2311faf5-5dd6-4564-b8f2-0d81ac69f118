                                                 RICHARD BRANDON
                                                              UI DEVELOPER
CONTACT                              SUMMARY
                                     Passionate front-end web developer with 10 years of experience using
  <EMAIL>               HTML,CSS and JavaScript to build all aspects of the user experience and user
   202-555-0120                      interface for client-facing landing pages: Specializes in using jQueryand
  Chicago, Illinois, US              Angular.
in linkedin com/resumekraft          EXPERIENCE
SKILLS                               Senior consultant II                                  Oct 2012 Present
                                     Accenture
HTMLS, CSS3                          currently working as frontend Ul developer from the past years
Javascript; Jquery                   Senior web developer                                Sep 2007 Mar 2012
                                     Infosys
Angular 2+ & React JS                This was my first company where worked as a Ul web developer for 4 years,
                                     have taken 4 months break in between and rejoined the company again and
Bootstrap; <PERSON><PERSON><PERSON>                     worked till the company closed in 2012
SVN, TFS; GIT                        PROJECTS
Responsive web, Stenciljs            AG Golf                                             Nov 2019 Jan 2020
                                     Front end developer
(Secondary Skills: c#t, aspnet       Customer want to create an automated purchase order process from issuing;
core; entity framework)              receiving; reconciliation and production in OSOM database to connect with
                                     activate inventory system QB financial System and kuebix shipping system:
Sharepoint Branding                  Responsibilities:
                                         Developed the Html for the layouts
                                         Responsive web designs, custom Ul components:
LANGUAGES                            Tools: visual studio code, Photoshop, Html5, Css3, angular6, sass
English                              Ace Sports Admin                                    Apr 2019 Oct 2019
French                               Front end developer
<PERSON>                               This application is about managing events for organizations or communities
German                               eachorganization will have unique portal with different set of roles as Admin,
                                     Players, Coach and tourneys. Admin can create different types of events like
                                     Seasons, camps, other events and tournaments, this events can be free or paid.
EDUCATION                            In paid events user allowed to select different payments like single time or
                                     recurring during registration process this charges will be collected: Player can
Bsc computer science                 register to those events from public portals of an organizations. Admin can
Jul 1998 Aug2001                     create coacheslmentors, they can forms teams from the players who registered
San Jose State University            for that event: Here players allowed to acceptldeny his place in the team
                                     Responsibilities:
                                         Converted designs into Html: