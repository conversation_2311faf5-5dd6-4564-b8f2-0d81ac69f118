                                                    RICHARD SCOTT
                                                            WEB DEVELOPER
CONTACT                             SUMMARY
                                        Self-independent; reliable and friendly individual who works hard to achieve
  info@resumekraft com                  his goals.
  202-555-0120                          Adaptable quickly, and organized well. Interested in learning the latest web &
  Chicago, Illinois, US                software technologies quickly:
                                        Able to work well in teams as well as individually: My future goal is to
in linkedin com/resumekraft             become a senior full-stack developer:
SK<PERSON>LS                              EXPERIENCE
PHP OOPs                            Web Developer                                       Apr 2018 Present
                                    Graficon
Yii 2 MVC                           Developing PHP and JS Web Apps_
                                    Software Eng                                       Nov 2017 Nov 2017
PHPUnit                             Foxmedia
                                    One month training after graduation (2 Projects in React & Redux)-
JavaScript
Node js                             PROJECTS
                                    Costs Calculator for a socks company                Jan 2017 Apr 2017
JavaSE Opp's                            Developed in PHP & MySql (Yii 2 framework Adv-template)
                                        Calculate the price of socks products  to issue price lists and invoices.
                                                                       and
C#, C++                                 Customers who visit the online showroom can ask for a price list:
 MySQL, SQL Server                      The sales representative, by logging in, canprovide the price list, add new
                                        products, and issue invoices with shipping cost fees and commission fees if
                                       they are required:
Transact SQL                        Website similar to YouTube                         Nov 2016 Dec 2016
Json, Rest API                          Developed in PHP & Mysql Yii 2 framework basic template):
                                        Registered users can add videos, like, dislike_ comment.
SW Pattern                              Admin manage the whole website_
 Git                                Medicine E-commerce                                Apr 2015 Apr 2015
                                        Developed in OOP; ADO.NET in C#t:
                                        The user can add, delete, and update a medicine & a customer:
 LANGUAGES                              Also, the user can issue the sales invoice_
                                        Multiple users can login to this application and onlytheadministrator can
                                             report to know the login  logout (date & time) for each user:
                                        see a                    and
English
French                              EDUCATION
Arabic
German                              British Bachelor Degree                            Sep 2014 May 2017
                                    San Jose State University
                                    Computing program study focusing on Web/Software develpment and Network