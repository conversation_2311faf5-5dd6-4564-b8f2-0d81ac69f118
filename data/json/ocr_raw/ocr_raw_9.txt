                                                         VICTOR EDWARD
                                                              ANGULAR DEVELOPER
CONTACT                                 SUMMARY
                                        Highly skilled and motivated Angular Developer with year 4 months of
   info@resumekraftcom                  experience in designing and developing robust & responsive web applications
   202-555-0120                         Strong expertise in frontend development, JavaScript, TypeScript; HTML & CSS:
   Chicago, Illinois, US                Demonstrated ability to collaborate with cross-functional teams, effectively
in linkedin com/resumekraft             communicate technical concepts, and deliver projects within deadlines Seeking
                                        an opportunity to contribute my expertise and passion for coding to dynamic
                                        and innovative team
SKILLS                                  EXPERIENCE
   Angular 2 & higher version           Angular Developer                                        Nov2022  Present
   JavaScript, TypeScript, RESTful      Certiview IT & Management Solutions Pvt Ltd, Hyderabad
   APIs                                 Project Health-care Management System
   HTMLS, CSS3,SCSS, Ajax,              Environment Angular 13, HTMLS, CSS3, Bootstrap   Prime-ng_ Web API
   jQuery, AJAX                         Description: Health-care Management System is aimed to revolutionize
   Bootstrap; Angular Material          healthcare processes & enhance patient care: This project focused on
   Prime-Ng                             developing  web-based application that facilitated efficient management of
   GIT, Agile; Scrum                    medical records; appointment scheduling; and communication within healthcare
                                        organizations_
EDUCATION                               Responsibilities:
                                            Developing the code as per the requirements:
BE Electronics &                            Interaction with the client to understand the requirement:
Telecommunication                           Engage in requirement specification process for new software functionality:
San Jose State University                   Ensure design consistency with client's development standards &
                                           guidelines
                                            Enhanced legacy application by building new components in Angular 13.
                                        POC Ecommerce Shopping Application:
                                        Environment: Angular 9, HTML, CSS Bootstrap 4, Angular Material, TypeScript;
                                        Web API
                                        Description: This application is developed tobuy& sell products online It aims
                                        to provide user-friendly interface    functionality, and a secure
                                                                        robust
                                        environment for conducting E-Commerce transactions:
                                        Responsibilities:
                                            Used various Angular custom directives & developed reusable components
                                            & templates that can bere-used at various places in theapplication.
                                            Created Typescript reusable components &services to consume REST APIs
                                            using Component-based architecture provided by Angular.
                                            Build Product Listing and Inventory Management Component from scratch.
                                           Implemented HTTP requests using RXIS Observable library: