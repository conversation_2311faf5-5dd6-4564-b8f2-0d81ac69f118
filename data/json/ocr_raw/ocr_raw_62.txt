                <PERSON>
                JAVA DEVELOPER
         info@resumekraftcom        202-555-0120      Chicago, Illinois, US in  linkedin com/resumekraft
      SUMMARY                                                               SKILLS
  A Java FullStack Developer passionate about web development          Java
  with a focus on Backend and am highly interested in gaining          Spring
  knowledge about best practices andbuilding quality software          Hibernate
      EXPERIENCE                                                       Angular
  Java Developer                                                            EDUCATION
  Softbinator Technologies Mar 2020 Present
  Webapplications development                                          US Project Semester   ERASMUS +
                                                                       San Jose State University
  Java Developer                                                                          Jan 2017 Jun 2017
  Expleo  Nov 2019 Mar 2020                                            Industrial Design
             application development
  Internal web                                                          Northeastern University
  Engineer in Product & Development Design                                                Oct 2013 Jul 2017
  Expleo  Nov 2017 - Nov 2019                                               LANGUAGES
  Plastic components development
  Work placement    ERASMUS +                                          English
  SENTECH Malta FP Ltd:  Jun 2016 Sep 2016                             French
  Develop new parts for gyroscopes on milling machine; lathe or        Arabic
  CNC                                                                  German
dl    PROJECTS                                                              HOBBIES
  Luxon                                                                Adobe Photoshop
  Software engineer Jul 2020 Present                                    MMA   Mixed martial arts
  Fintech project focused on integration with gambling companies       Do It Yourself projects
  Responsibilities: implementation, testing new features based on      Car/parts restoration
  customer requirements,bugfixing and refactoring
  Technologies: Java 11, Spring Boot / Security, Hibernate; MariaDb,        PERSONAL SKILLS
  Liquibase, <PERSON>is, <PERSON><PERSON>, Gradle
  PowerBank                                                               Desire to learn
  JavaDeveloper Mar 2020 Jul 2020                                         Attention to details
  Web application based on REST microservices that is supposed to         Adaptability
  manage rents of portable power banks                                    Criticalthinking
  Responsibilities: requirement gathering, implementation; testing        Problem solving
  andbugfixing
  Technologies: Java 8, Angular 8, Spring Boot Security(OAuth,
  JWT), Hibernate, MariaDb, Redis, Docker, Gradle;
  WebCM
  JavaDeveloper Nov 2019 Mar 2020
  Web application for creating and managing skills matrix for
  employees and projects_