          <PERSON>
          Soltware Developer
Profile                                                                    Details
"Leading Software Developer and Engineer with 8 years of work              143 Main Ave; Orlando, FL, 32804 ,
experience leading developing tcams different settings (including remote,  United States; 890-555-0401
international teams).enjoy coding; testing; ard integration Seeking anev   resumeviking com  templates}
challenges and opportunities leverage  am an experienced Certified         DatePlace ofbirth
ScrumMaster (CSM), having successfully worked with service-oriented (SOA)  05/10/1983
architectures and web services HoldsBS in CS with many recent study        Flagstaff,AZ
certifications
Employment History                                                         Nationality
                                                                           USA
Software Developer at Johnson     Johnson; San Francisco, CA               Driving license
November 2017  November 2019                                               Full
Johnson   Johnson is   Fortune 500 Medical Device and Manufacturing
company    the US As   Software Developer;work on their eCommerce          Skills
platform  an Agile environment My daily responsibilities include:          MongoDB
     Participating in daily stand up meetings; led by our Scrum Master
     Utilizing the MEAN stack to enhance and maintain our eCommerce        Express JS
     platform                                                              AngularJs
     Conducting code peer reviews with other members in my team
     Participating in product demos                                        Mode_JS
     Documenting all code changes, following J&J s change protocols        Swilt
Software Developer at PIH Unlimited, San Francisco_
May 2016  November 7017                                                    Java
    Software Developer at PIH Unlimited worked on a small Agile team in    Python
 startup environment to prototype and     mobile   pplications. My daily
                                     build
responsibilities included:
     Brainstorming with team members t come Up with new mobile
     application concepts
     Working with stakeholders to      functional and technical
                                gather5
     requirements
     Creating wireframes and prototypes to test our ideas
     Writing code to develop iOS and Android applications, primarily using
     Java and Swift
     Participating in MVP and product demos
     Utilizing automated and manual methods to test our code
     Facilitating releases of software upgrades
IT Intern at Fidelity National Financial, San Francisco, CA
January ?014May ?016
At Fidelity NationalFinancial, ! participated  an IT internship, during
whichrotated between their infrastructure; data analytics, and software
engineering departments. My daily activities included:
     Shadowing senior team members to get a feel for their day-to-day
     responsabilities
     Taking on small software development projects then presenting my
     work to the leadership team
     Assisting with process improvements, making suggestions on
     workflow changes where needed