 <PERSON>                                  A Software Engineer with entry-level experience specializing in application
                                           development , data analytics, cloud architecture , and Angularjs. A strong history of
                                           developing platforms to enhance data-driven decision making: Adept at
Lang                                       identifying innovative solutions in collaboration with diverse teams and project
                                           stakeholders
 Software Engineer                          Professional Experience
 4759 Sunnydale Lane                        Software Engineer
   Plano, Texas, United States, 75071       Enhanced Data Science Corp , Boston, MA May 2020 Present
   <EMAIL>
   123-456-7890                                Conduct datamodeling to analyze the performance of existing information
  Education                                    systems for clients within the insurance industry valued at S1OOK-S250k
                                               Design    ~facing analytics dashboards and build new features in
                                                     user-
                                                coordination with the data science team to improve data collection processes
  Bachelor of Science in Software              for end users
 Engineering                                  Support the build-out f data ingestion frameworks and develop monitoring
  Boston University, Boston, MA                 solutions to improve data quality and integrity by 50%
 September 2016 May 2020                    Academic Experience
   Key Skills                              Software Engineering Projects
    Software Development                    Boston University, Boston, MA May 2020                   scheduling and
    Data Analytics                             Designed and developed a mobile application to aid users in  testing
    Big Data                                  monitoring appointments with real-time alerts, which included
    Data Science                              functionality; identifying bug fixes and programming using Python
    Cloud Architecture                          Developed a fantasy football application using JavaScript to enable users to
                                               analyze and compare player statistics in real-time to determine scoring
                                               percentage and trade value
 Certifications
  Certified Software Development
 Professional
  Expected 2022
 Google Data Analytics
 Certification
   June 2021