 Fiachra
 McNamara
80 Keyboard Click Lane, Cork,        (353) 800 293 9001
T12, Ireland                           ffmcnamara@gmailcom
 Front End Developer                   Skills                              Languages
 Experienced Front End Developer with   C++                       Expert   English              Native speaker
 over 10 years experience proficient   Bootstrap                  Expert   Irish              Highly proficient
in all stages of advanced web           React                     Expert
 development and passionate about
creating solutions for clients. Adept  HTMLS                      Expert
                 technologies,
in an assortment of                     Git                       Expert
including Java, Tomcat, and Microsoft  JavaScript                 Expert
SQL Server: Knowledgeable in user
interface, testing, and debugging
 processes_
 Employment History                                                        Education
    Front End Developer                    Front End Developer                 Bachelor of Science in
 DOTDOTDublin, Cork                  Kearney Solutions, Cork                  Software Engineering
January 2020   Present               July 2012  December 2019              University College Cork, Cork
    Developed a responsive website         Developed a custom theme for    September 2011 _ June 2015
    that is optimized for both desktop           management system
       mobile devices, increasing user     a content        the
    and                                    (CMS) that improved look and
   engagement by 45%                       feel of the website
    Implemented a single-page              Utilized version control systems
   application using React and Redux       such as Git to manage code
    that improved overall performance      changes and collaborate with other
    and usability                          developers
    Employed AJAX and JSON                 Developed unit tests to ensure code
    technologies to create a seamless    integrity and reduce the number of
    data exchange between the            bugs found in production
    front-end and back-end               Integrated third-party APls to
    Integrated third-party APIs to         provide additional functionality and
    provide additional functionality and   improve user experience
    improve user experience
 '11      '12     '13      '14      '15      '16               18      '19      20       '21      '22     '23