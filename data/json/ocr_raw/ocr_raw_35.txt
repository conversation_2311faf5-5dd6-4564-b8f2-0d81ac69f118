Work ExPERIENCE                                            Email  Linkedin  Github
Software Engineer                                                                      Nov 2022   Present
  Developed and managed web applications using ReactJS, Redux, RTK Query; and TypeScript to ensure
  improved code quality and long-term maintainability.
  Migrated the front-end code base front CRA to VITE helping the productivity of developers by reducing
  development server start time by 20% and production by 8%
   Employed React Router to establish protected routes for login pages and admin dashboards, resulting in
   enhanced user access control and strengthening overall system security.
   Streamlined and restructured Redux store, optimizing application s state management; reduced load time by 25%
  and enhanced overall performance       API _endpoints using RTK Query and leveraging caching
   Optimized system efficiency by integrating
  methodologies, leading to 40% decrease in API calls and notable improvement in system performance
   Revamped responsive design for mobile devices, enhancing front-end user experience; resulting in an increase
  in mobile tratfic _
 PRoJECTS
 Game Store   React, TailwindCsS_ Framer-Motion Gilhub
   Designed and created  responsive and user-friendly Game store app with an extensive game catalogue
   Successfully integrated core functionallties, including game listing cart management, and favourites selection, t0
   elevate user engagement and overall experience
   Acquired experience in utilizing the Framer Motion library to incorporate captivating animation effects, enriching
  the app's visual appeal and interactivity
 E-Commerce Store    React, TailwindCSS Typescript; NodeJs ExpressJS  Github
   Primarily focused on Frontend development, utilizing ReactJS, Typescript, and TailwindCSS
   Integrated backend API seamlessly through RTK Query and leveraged its caching capabilities for optimization_
   Created user interfaces for product listings, shopping cart functionality, and authentication processes_
   Designed and implemented Redux Toolkit to establish centralized state management system
   Utilized TypeScript for enhanced type safety and improved code quality
   Developed and deployed user authentication features utilizing JWT-based login, signupandpassword reset
  functionality;
SKILLs
Languages: C C+t, Java, JavaScript; TypeScript
Web Technologies and Frameworks: HTML, CSS; ReactJs TailwindCSS, Redux
 Deployment and Version Control: Docker; Git;
 Databases: SQL, Firebase , MongoDB
 Project Management Tool: Jira
 Coding Profiles: Hackerrank; Codechet; Geektorgeeks
 EDUCATION
 Bachelor of Engineering in Computer Engineering CGPA: 8.5                                   2018   2022
 ACHIEVEMENTS
   Runner-up In GitHub Hackathon The Code Geek Fest
   6-Star coder at HackerRank
   3-Star coder at CodeChef (Max Rating 600)
   Secured Znd Rank in October dawn 2020 college coding contest
   Coding Score of 822 on Geeksforgeeks