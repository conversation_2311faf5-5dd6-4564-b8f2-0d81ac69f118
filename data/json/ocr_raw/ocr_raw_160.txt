  OLIVIA SMITH
  Ul Engineer
    supporteqwikresumecom    (123) 456 7899 Los Angeles
    wwwqwikresumecom
      PROFESSIONAL SUMMARY                                                      SKILLS
  Enthusiastic Ul Engineer with 5 years of experience in designing and      Htmls
  developing engaging user interfaces using HTMLS, CSS3, and
  JavaScript frameworks excel in crafting responsive, user-centric          Nodejs Environment
  applications that enhance overall usability and performance My
  expertise in Agile methodologies fosters collaboration and drives
  continuous improvement; ensuring exceptional user experiences             Kendo Ui Framework
  across various platforms:
                                                                            Jenkins Ci/cd'
      WORK EXPERIENCE                                                       Mocha Testing Framework
  UlEngineer                                     Mar / 2022-Ongoing
  Seaside Innovations                            4 Santa Monica; CA
  1 Conduct code reviews and provide constructive feedback                       INTERESTS
   Designed mobile Ul architecture using Kendo Ul, Core JavaScript;            Home Brewing
   CSS3, and SASS to optimize performance for high-volume                                        Wildlife
   applications                                                                                   Conservation
  3 Implemented large-scale HTMLS and JavaScript applications;                 Running            Public Speaking
   ensuring cross-platform compatibility with Kendo UI:
 4 Enhanced user experience by optimizing performance and usability
   for mobile applications
 5. Developed single-page applications utilizing Kendo Ul; Core                 STRENGTHS
   JavaScript; HTMLE
                   5, and CSS3
 6. Created and integrated web services APIs to dynamically populate                             Wisdom
   Ul components using jQuery AJAX                                              Willingness
 7. Collaborated with UX designers to enhance user flows, resulting in
     25% reduction in task completion time                                      Zeal     Ingenuity
  Ul Engineer                                Mar / 2020-Mar / 2022
  Crescent Moon Design                               Portland;, OR               LANGUAGES
   Participated in requirements gathering and contributed to
                          application features:
   designing user stories for
   Engaged in strategic meetings to select appropriate development
   frameworks and technologies for projects
 3 Recommended accessibility development tools and provided
   resources for team training:                                               English    Japanese      IItalian
 4 Developed web applications in accordance with WCAG 2.0
   standards and W3C guidelines
 5. Consulted on accessibility issues and contributed tothe                      ACHIEVEMENTS
   development of an Accessibility Management Platform:
                                                                                Enhanced application performance
                                                                                by
      EDUCATION                                                                   '30%through optimized Ul;
                                                                                components
  Bachelor of Science in Computer                 Mar    Mar                    Led the redesigniof a major
  Science                                        2018     2020                  eCommerce platform; resulting in a
  University of Technology                              Portland, OR            25% increase in user engagement:
  Focused on software development and user interface design
  principles:
Powered by   Qwlkresume                                                                              WWWqwikresume com