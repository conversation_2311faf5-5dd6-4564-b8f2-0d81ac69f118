   A NNABELLE


67 Nethaji Nagar
   Andheri
   Mumbai
      022- 623 7238
      annabelle@xyzcom

   EDUCATION

  Bachelor's Degree in Science in
University of California
  SKILLS
    Focus on code quality,
reusability and testability
    Good analytical and problem
solving skills that are coupled
  with strong communication
   Ability to write test cases and
coverage
     Database (Sybase) strong SQL
and Stored procedure concepts,
   Strong Core Java, Spring &
 Hibernate, Multithreading;
 Design Patterns, OO Concepts
     Cloud experience AWS
 desirable
    Develop fast and scalable web
applications that are pleasure to
  use
   Strong mathematics background
   Proficiency with at least one of
thefollowing frameworks ~laravel,
zend, symfony or cakephp
    Strong team player




    SENIOR
    FULL STACK DEVELOPER             BANGLORE
   05/2014 Present
     Design and develop the next-generation system's
 performance monitoring and capacity management
   infrastructure for VMware's virtualization products
     Developing Ul, management layer and Services,
 and APIs
    Taking technical ownership for key components
and working through functional, performance and
   scale issues
      Build consensus among different teams working on
  software development projects
   FULL STACK DEVELOPER              BANGLORE
   07/2007 02/2014
     You will work together with our development
  team to extend the functionality and performance of
   our applications
      Provide third level assistance and support on
   developed applications
     Work cross-functionally with various Intuit teams:
product management; QA/QE, various product lines,
   or business units to drive forward results
     The Full Stack Developer will be working as part
  of a small team to design and deploy phenomenal web
  applications and platforms
     Provide ongoing feedback to upper management
  on the current status of projects

JUNIOR
    FULL STACK DEVELOPER                   DELHI
    10/2000 06/2007

        Developing unitlintegration tests
       Spring framework, IOC, MVC
       Spring framework, IOC  basic knowledge
       Applications development
        Unit testing frameworks (JUnit; Mockito etc )
       Sometimes developing automation Ul tests
        Unit testing frameworks (JUnit; Mockito)