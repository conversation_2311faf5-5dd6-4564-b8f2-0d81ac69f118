                                                   RACHEL FRANK
                                                        PYTHON DEVELOPER
  CONTACT                          SUMMARY
                                    am a python developer, am interested in Automation using python: have
    info@resumekraftcom            Automotive domainknownledge and  have experience designing Test
    202-555-0120                   Automation Frameworks  like to build smart application using Al and
    Chicago, Illinois, US          python
 in linkedin com/resumekraft       EXPERIENCE
 SKILLS                            Senior Project Engineer                           Jan 2018 Present
                                   Wipro Technologies
    Python                             Working as a Python developer in Test-suite Scheduler Application:
    Automation                         have developed python package for HPALM functionalityusingREST
    Selenium                           APIs querry:
                                       Developed the recovery framework for Test Bench Crashes which has
    Ct+ programming                    improved the TestBench Utilization for Software-Build Execution:
    Machine learning                   Automated the JIRA Tasks such as Issue creation, Check for Duplicate
                                       Tickets
TOOLS                                  Developed Selenium python based automation framework for enabling
                                       CICD for Application
                                       Automated the task for finding any duplicate test cases in test rail
    GITHUB                             which has resulted in decrease the manual effort and improved
    TESTRAIL                           requirement-test case mapping
    JIRA                           Project Engineer                                 Jul 2015 Dec 2017
    HPALM                           Wipro Technologies
                                       Developed the test scripts to test the infotainmnet system:
  EDUCATION                            Developed the Test Automation framework for Android-Auto,
                                       AppleCarplay; In_Vehicle-Software-Update Feature Testing:
 Bachelor of Engineering
 Sep 2010 Jun 2014                 CERTIFICATION
 San Jose State University
 Electronics and Communication     Embedded system                                       2015-06-05
                                   Vector India
                                   C, Linux internals, micro processors, socket programming
                                   PERSONAL PROJECTS
                                   Stock Price Prediction
                                   https:Ilgithub com/simple-stockpredictions/bloblmaster/Linear_models -
                                   SPipynb
                                   Credit card fraud detection
                                   https:Ilgithubcom/Credit-
                                   CardFraud/bloblmaster/Credit_card_prediction ipynb