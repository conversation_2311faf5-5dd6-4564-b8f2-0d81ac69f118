                             VICTOR BRANDON
                                         BACK-END DEVELOPER
 SUMMARY                                                  EDUCATION
 Highly skilled Back-End Developer with 5+ years of       Bachelor in Computer Science
 experience in designing and developing complex web       San Jose State University Nov 2015     Sep 2021
 applications. Proficient in Python, PHP. and Javawith
 strong understanding of database management              EXPERIENCE
 systems. Proven track record of delivering high-quality
 projects on time and within budget: Excellent problem-   Back-End Developer
 solving and communication skills with strong             IBM Jun 2022     Present
 attention t0 detail team player who thrives in fast-         Experience developing and maintaining serverside
 paced environments                                           applications
 CONTACT                                                      Strong knowledge of programming languages such
                                                              as Java; Python; Ruby, or Nodejs
   info@resumekraftcom                                        Proficiency  database management and query
                                                              optimization (e.g-MySQL MongoDB)
    ************                                              Experience with web frameworks such as Django,
    Chicago, Illinois, US                                     Spring   Express
 in linkedin comfresumekraft                                  Understanding of version controsystems (e.g: Git)
                                                              Testing and debugging skills to identify and fix
                                                              issues in code
 SKILLS                                                       Familiarity with frontend technologies and ability
                                                              collaborate with frontend developers to integrate
   Programming Languages (e.g- Java, Python)                  userfacing elements with serverside logic
   Database Management (e.g- SQL)                             Design and implementation of data storage
   Serverside Frameworks (e.g-Nodejs)                         solutions and ensuring data security and integrity
                                                              Collaboration with crossfunctional teams t0 define;
   RESTful APIs                                               designand ship new features
   Version Control (e.g,, Git)                                Continuous monitoring and performance
   Web Security                                               optimization of applications to ensure scalability and
                                                              efficiency
   Cloud Computing                                            Troubleshooting and resolution of serverside
   Performance Optimization                                   performance bottlenecks
   Debugging and Troubleshooting                              Up to date knowledge of industry trends and best
   Agile Development                                          practices t0 continually improve the development
                                                              process and stay current with emerging
                                                              technologies:
LANGUAGES
 English
 French
 Arabic
 German