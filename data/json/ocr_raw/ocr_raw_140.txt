                                                                                         <PERSON> Dover
                                                                                                Software Developer
                                                                                          <EMAIL>
                                                                                    Vancouver; BC | (555) 555-1234
  Entry-Level Software Developer at ABC Development; with experience in designing software solutions, conducting code reviews,
    collaborating with cross-functional teams  efficiency in reducing software bugs by 20% and enhancing team
   and                                   Proven
   productivity:
  Work Experience
 Software Development Intern                                                                     Sep 2023  Present
 CoderHaven  Burnaby; BC
         Assisted senior developers in designing and implementing software solutions across 5 teams.
        Participated in code reviews  provided feedback for code improvements.
                                 and
         Collaborated with cross-functional teams t0 troubleshoot and resolve technical issues_
        Contributed to the development of internal tools and utilities to improve team productivity by saving 10 hours a week:
                        bugs                       testing   debugging procedures
         Reduced software    by 20% through rigorous      and
 Projects
 Online Bookstore Web Application                                                                          Present
        Developed a fully functional online bookstore using React-js for the front end and Nodejs for the back end:
        Implemented user authentication and authorization features using JWT tokens
                                     \'storing "                   reducing stock return by 20%.
        Integrated a MySQL database for     book and user information
 Task Manager Android App                                                                                  Present
         Created an Androidapplication for managing tasks using Java and Android Studio.
                                  addingediting, and deleting tasks along  settingreminders, and improved user
        Implemented features such as                                  with
        experience by 30%.
        Utilized SQLite database for storing task details locally on the device.
 Core Skills
 Java, Python, JavaScript; Git; Visual Studio Code, Eclipse, HTML, CSS, React js, SQL, MySQL; Linux , Debugging;
Troubleshooting; Problem-Solving; Communication Skills, Collaboration Skills, Analytical Thinking
Education
University of British Columbia                                                                            Jun 2024
      Bachelor of Science Computer Science