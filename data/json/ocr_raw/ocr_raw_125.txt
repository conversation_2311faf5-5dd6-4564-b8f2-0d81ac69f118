        (166) 111 1311
      +1
     <EMAIL>
     linkedin com/thomas
      Www Github Com/Thomas
 SUMMARY
  9+ years experienced Senior
 Python Developer armed
  with a PCPP Certification and
 highly skilled in building;
 maintaining; and testing
 back-end & front-end

  features and integrating
 them into applications:
 Proficient in liaising with
  cross-functional teams t0
 resolve technical & design
 issues and achieve 10090
  user/client satisfaction.
SKILLS
 Backend & Frontend Components
Integration



                 THOMAS SCOTT
                   Senior Python Developer

EXPERIENCE
SENIOR PYTHON DEVELOPER
ELIGHTWAY SOLUTIONS LLP     SF, CA  Jun '17 - Jun '20
   Front & Back-end Components Integration & Coding

   Built, maintained, tested & troubleshot back-end features in
   Python and integrated front-  components into applications
                             end
   Collaborated with UXIUI designing team of 25 to implement
   design into the code and manage testing &
                                          bug fixes
   Applied software enhancements and suggest improvements to
   the management
PYTHON DEVELOPER
 BLUE BEAR ENTERPRISES   SF, CA  Jun '13 - Jun '17
 Coding & Application Designing
   Wrote reusable; testable effective & scalable code while
    ensuring 00% accuracy and compliance with code standards
   Integrated user-facing elements into applications and designed