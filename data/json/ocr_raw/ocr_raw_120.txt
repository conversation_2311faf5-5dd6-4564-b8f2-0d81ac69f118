<PERSON>
PYTHON DEVELOPER
 SUMMARY                                                                         CONTACT
 Experienced Python developer with proven track record in developing efficient   info@resumekraftcom
 and scalable applications. Proficient in Python frameworks such as Django and   202-555-0120
 Flask_as well as database management using SQL and MongoDB Skilled in           Chicago, Illinois, US
 implementing RESTful APIs and integrating third-party libraries Strong          linkedin com/resumekraft
 problem-solving and debugging skills. Committed to continuously improving
 code quality and staying updated with the latest industry trends;
                                                                               SKILLS
 EXPERIENCE                                                                      Problem solving
 Python developer                                                                Communication
 Codsoft   Jul 2023 Aug 2023                                                     Leadership
     Developing and maintaining Pvthonbased applications and software            Active listening
     Writing clean and efficient code using Python programming language
     Collaborating with crossfunctional teams to design; develop, and implement  Python Programming
     software solutions                                                          Data Analysis
     Participating in code reviews and providing constructive feedback to peers  Web Development
     Troubleshooting and resolving issues and bugs in Python code                       earning
     Integrating data storage solutions and databases with Python applications   Machine
     Creating and maintaining technical documentation for Pythonbased projects   ObjectOriented Programming
     Developing and implementing software testing strategies and procedures      Algorithms and Data Structures
     Keeping uptodate with the latest trends and best practices in Pvthon      Testing and Debugging
     development
     Working with project managers and stakeholders to understand and analyze    Git Version Control
     project requirements                                                        Database Management
     Optimizing and improving the performance and scalability of Python          Problem Solving
     applications:
 Web developer                                                                 LA<PERSON><PERSON><PERSON>ES
 Accenture   Jul 2023  Aug2023
     Experience in building responsive websites and web applications using       English
     HTMLCSS, and JavaScript                                                     French
     Proficient in frontend frameworks such as React; Angular, or Vuejs
     Familiarity with serverside languages such as PHP, Pythonor Nodejs          Arabic
     Experience in working with databases such as MySQL or MongoDB               German
     Knowledge of version control systems like Git
     Ability to create and consume APIs
     Understanding of web performance optimization techniques                  HOBBIES
     Strong problemsolving skills and the ability to debug and fix issues in a  Reading
     timely manner
     Testing and debugging of web applications across different browsers and     Writing
     devices                                                                     Music
     Deployment and maintenance of websites on web servers or cloud hosting
     platforms                                                                EDUCATION
 PROJECTS                                                                      PG in Python
 Netflix clone                                                                   San Jose State University
 Netflix-Clone  Jul 2023  Aug2023                                                Nov 2021   Jan2023