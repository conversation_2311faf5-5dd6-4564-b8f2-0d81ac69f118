                                     <PERSON>
                                       Network Engineer
                                       1515 PacificAve,Los Angeles, CA 90291, United States
                                      **********    email@emailcom
Place of birth                        Profile
San Antonio                          Network Engineer bringing 8 years of experience in network design, network
                                     administration, escalation support; and systems administration. Adept in
Driving license                      delivering technical support, managing network operations, and maintaining
Full                                 enterprise-wide area networks across multi-platforms and high uptime Data
                                     Center environments:
Skills
Port Address Translation             Employment History
(PAT)                                Senior Network Engineer; Standard Bank, Arkalyk
Network Address                      January 2021    Present
Translation (NAT)                    Responsible for optimizing network performance and increased reliability by
Access Control List (ACL)            maintaining hardware and software; analyzing technical issues, and ensuring
                                     system users' availability for a 1500 user facility:
Wireshark                                 Managed the development; installation, and management of
                                         organization-wide localarea network, network segment, widearea network,
 MPLS                                    and internet system:    ticketing support to resolve all issues for 550+
                                         Delivered Tier 1 network
                                          users
Hobbies                                  Executed the maintenance window changes for multiple locations
Rock Climbing; Music, Drama              performing troubleshooting of routing, and switching issues
                                         Implemented and migrated a major client to VPLS circuits, which resulted
Languages                                in robust and seamless connectivity between two different carriers.
English                                  Inherited an MPLS project that was off-track; liaised with the parent
                                         company to deliver a solution with increased availability, security, and
Spanish                                  visibility ahead of the project schedule:
                                     Network Engineer; <PERSON>, Knoxville
                                     January 2018    November 2020
                                     Responsible for Upgrading Windows NT/2OOO and Novell NetWare 5.1
                                     environments and installing configuring, and maintaining Cisco routing and
                                     switching equipment in a 1500 user environment:
                                         Ensured smooth end-to-end communication by delivering support for Video
                                         Conferencing System (Cisco VCS/TMS)
                                         Designed, configured, and installed all Data Center Network Devices to
                                         support 1500+ users, including ran cables, routers, ports,
                                         Successfully resolved routing issues within multiple contexts of the Cisco
                                         ASA firewall.