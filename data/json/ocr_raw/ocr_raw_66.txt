                                     <PERSON>
                                     Java Developer
                                     1515 Pacific Ave; Los Angeles, CA 90291, United States
                                     3868683442     email@emaiLcom
@3  Instagram,                       Profile
    Resume Templates,                Experienced and well-organized computer science graduate with @ B.S. from
    Resume Builder                   UCLA (GPA 3.8). Enthusiastic about joining (insert company name) as a junior
Place of birth                       Java Developer to design game-changing e-commerce solutions Established a
San Antonio                          custom-made e-commerce cart module on RESTful. 6-month Java development
Driving license                      internship experience at Oracle:
Full                                 Employment History
Skills                               Java Developer; IST Programming, Oklahoma City
SQL and ORM                          January 2020   Present
J2EE framework                       Accomplished a 34% decrease in the app's memory consumption by removing
Xquery, XSL                          duplicate strings via Garbage Collection Logs analysis and refactoring the code:
                                          Design, implement; and maintain Java applications that are typically
DevOps tools (Jenkins,                    high-volume and low-latency, necessary for mission-critical systems.
Docker)                                   Deliver excellent availability and performance_
                                          Contribute to every phase of the development lifecycle:
Languages                                 Write code that is designed well; practical, and testable:
English                                   Conduct analysis of software, debugging, testing, and programming:
                                          Manage Java andJavaE.E: application development
Hobbies                                   Ensure designs conform with stipulations_
Chess, Debate, Drama                      Prepare and produce releases of software parts.
                                          Transform any requirements into stipulations.
                                          Supporting constant improvement.
                                          Investigate options and new technology:
                                          Present for architectural reviews_
                                          Designing; implementing, and maintaining phases of java application:
                                          Taking part in architectural development and software activities.
                                     Java Developer; YtoK, Mamaroneck
                                     January 2017   December 2019
                                     Discovered and enhanced code inefficiencies, leading to a 42% increase in apps
                                     stability:
                                          Identify issues with production and non-production application.
                                          Developing; testing, implementing, and maintaining application software:
                                          Recommending changes to develop recognized java application processes:
                                          Developing technical designs for application development.
                                          Developing application codes for java programs.
                                          Performing inclusive analysis and design for program changes, gaining and
                                          transferring data of application changes.
                                          Reviewing and preparing documents. Writing up design documents (e.g;,
                                          SRS, SDD)