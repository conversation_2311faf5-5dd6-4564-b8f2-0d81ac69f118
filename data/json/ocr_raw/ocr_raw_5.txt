 OLIVIA SMITH
 Angular Developer
   support@qwikresume com    (123) 456 7899 LosAngeles
   wwwqwikresume.com
 PROFESSIONAL SUMMARY
 Accomplished Angular Developer with a decade of experience in crafting dynamic web applications Proficient in Angular;
 TypeScript; and RESTful services; | excel in delivering scalable solutions while collaborating with cross-functional teams My
 commitment to clean code and performance optimization drives the development of user-centric applications that
 enhance business efficiency:
 WORK EXPERIENCE
 Senior Angular Developer                                                                           Jan / 2019-Ongoing
 Maple Leaf Consulting                                                                                     Toronto; ON
 1. Collaborated with product owners and enterprise architects to gather requirements and design high-performance
  Angular applications:
 2. Mentored junior developers, reviewing code and ensuring adherence to best practices before deployment:
3. Designed and implemented business logic for customer qualification processes, significantly improving offer accuracy:
4. Integrated REST and SOAP web services for seamless data exchange:
5. Leveraged Java and Spring technologies to develop robust backend services
6. Utilized modern web technologies including HTMLS, CSS3,and JavaScript to create responsive user interfaces
 7. Employed Angular and messaging services like Kafka to enhance application architecture:
 Angular Developer                                                                                    Jan/ 2015-Jan / 2019
 Silver Lake Enterprises                                                                                     Seattle; WA
 1. Developed a video sales presentation application using Angular; enhancing user interaction and engagement
 2 Collaborated closely with design teams to ensure alignment between technical capabilities and user experience:
3. Optimized application performance through rigorous testing and debugging processes
4. Documented technical specifications and user guides to support application rollout:
5. Managed SharePoint applications using Microsoft Power tools, streamlining business processes:
 EDUCATION
 Bachelor of Science in Computer Science                                                           Jan / 2012-Jan 2015
 University of Technology                                                                           4 Santa Monica, CA
 Focused on software development and web technologies; gaining a strong foundation in programming and application
 design:
SKILLS
 Angular Framework            Typescript                    RESTful APIs                  Htmls
 ACHIEVEMENTS
    Implemented a reusable component library that reduced development time by 30%.
    Led a team project that improved application performance by 25% through code optimization:
    Developed a custom Angular directive that enhanced user experience, resulting in a 15% increase in user engagement
 Powered by  Qwikresume                                                                                WWW.qwikresume.com