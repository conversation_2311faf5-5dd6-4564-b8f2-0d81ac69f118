                                                 <PERSON><PERSON><PERSON>ng
                                                 I n t e r n        d e v e l o p e r
                                                 Skilled Flutter Developer with expertise in building high-performance,
                                                 cross-platform mobile apps. Proficient in Flutter, Dart, and clean
                                                 architecture, delivering user-centric solutions efficiently.
                                                 Experience
 Skill                                               RikkeiSoft - Junior Mobile Developer
  Languages & Scripting: Dart, Java                  Duration: 06/10/2023 - 05/04/2024
                                                     - Develop Android and iOS applications using Dart Flutter, RESTful API, Dio, HTTP,
  Framework, library: Flutter, Spring                Provider pattern, GetX pattern, ...
  Understand OOP, Design
  Pattern(MVC. MVVM), Asynchronous                    Sucodev Việt Nam - Remote Mobile Developer
  programming, RESTful API                            Duration: 10/05/2023 - 05/10/2023
  Databases: SQLite, SQL Server, MySQL                - Develop Android and iOS applications using Dart Flutter, RESTful API, Dio, HTTP, Bloc
  Have knowledge about Data Structure                 pattern, GetX pattern, ...
  and Algorithm                                  Project
  Analysis & Design tools: Figma, Canva
  Version control: Git                               JobSwipe
  Additional tools: Slack, Docker                    Link source code: https://github.com/dhv-jobswipe
  Operating Systems: MacOS, Windows,                 Number of members: 3
  Linux                                              Project duration: 28/10/2022 - 29/11/2022
  Soft skills: Independent and teamwork              Description: A system that supports the connection between company recruiters and job
  working skill, problem solving,                    seekers to create an application that simplifies the recruitment process and allows job
                                                     seekers to quickly connect with recruiters by using a swipe function, similar to Tinder,
Education                                            from both the recruiting company’s and the job seeker's perspectives.
                                                     Slide:https://www.canva.com/design/DAGFODgGh0s/K9ZsWpWFBdBoWCduymSw/edi
                                                     tutm_content=DAGFODgGh0s&utm_campaign=designshare&utm_medium=link2&utm_so
Danang University of Science and                     urce=sharebutton
Technology: Major - Information                      Responsibilities: Flutter app developer, Backend developer
                                                     Technology in use: Flutter, Spring Boot
technology (Japanese language) - Fourth-             Necopia game - Unihack 2023
year
GPA: 3.23                                            Link source code: https://github.com/give-it-your-best-shot/Necopia-game.git
                                                     Number of members: 3
Language                                             Project duration: 27/07/2023 - 28/07/2023
                                                     Description:
                                                     - Raise a virtual pet in a simulated environment.Users will perform daily tasks to take care
English - Conversational English                     of their pets (daily login, feeding, bathing, etc.).
                                                     - The living environment of the animal changes based on the user’s current location's
Japanese - JLPT N3                                   environment (Through this, users are reminded to take measures to cope with the impacts
                                                     of climate change.).
French - B1                                          Slide: https://docs.google.com/presentation/d/1yu5P9QyjIRsGqDTvcJK7h02TXUW-
                                                     MYg1ZvTX2y1hQiE/edit#slide=id.g256a0c92114_3_19
Contact                                              Responsibilities:
                                                     - Team leader
                                                     - Flutter app developer
Phone                                                Technology in use: Flutter, Flame
+84787614533                                          Saigon tour
                                                      Link source code: https://github.com/BlenDMinh/SaigonTour
Email                                                 Number of members: 2
<EMAIL>                                  Project duration: 28/10/2022 - 29/11/2022
                                                      Description: A travel application that integrates login, payment, tour booking, search, etc.
Github                                                Responsibilities: Flutter app developer
https://github.com/give-it-your-best-shot             Technology in use: Flutter, Spring Boot