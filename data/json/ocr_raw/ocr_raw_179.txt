           ELLIOT                        PROFESSIONAL EXPERIENCE
      ALDERSON                                 Locktree
                                               UI Developer                                                        SF, US
Full Stack JavaScript Developer                M  Mar '17 Present
                                               Locktree is aleading international technology consultancy specializing in user innovation and
     +1 (333) 555 7777                          experience for companies running SAP
                                                  Working closely with each client tofully understand their requirements  deliver
     <EMAIL>                         accordingly                                                 and
     SF, US                                      Ensuring that clients can maintain highly scalable applications with front-end of their
                                                 large responsive websites
SUMMARY                                           Gained an in depth understanding of sizable codebases & boosted website traffic
                                                 from 120+ countries
       Full stack JavaScript developer with      Fixed 60+bugs to ensure smooth delivery and functioning of the applications
        hands on experience of designing;      Project Management & Delivery
      developing and maintaining web as well       Successfully delivered projects for multiple clients depending on legacy environment:
         as mobile applications Possesses         Jaguar Landrover: Extjs 4.0, Jquery, HTML, CSS, Java SQL
     diverse experience of creating multiple       Transport for US: Angularjs, HTML, CSS, JavaScript; SQL, used the TDD approach
   highly scalable applications using             Gas: React, HTML, CSS,JS, used the TDD approach
     different tech stacks: Adept at               Freshfield: Extjs 4.0, Jquery, HTML, CSS, Java SQL
 understanding the requirements of the
     clients and delivering projects
          accordingly: Demonstrates the        Blonk
     capability of maintaining code by fixing  Full Stack JavaScript Developer (Contract)
 bugs as well as conducting end to end                                                                             SF, US
     testing: Highly skilled at collaborating     Aug '16 Mar '17
       with team members to ship beautiful         Blonk is a recruitment startup with a Tinder like approach forhiring CXOs: They serve clients like
        products within deadlines. Looking     Intel; L'Oreal etc
      forward to applying the acquired gamut
         of skills in a challenging role:        Tech Stack:
KEY SKILLS                                         Meteor, CSS3, HTMLS, icoMoon, Node, NPM (private & public) JSON, 00
                                                   JavaScript; Responsive front end, Atmosphere package manager, underscorejs
      Fixing & Troubleshooting * Client            Blaze, Stripe & git version control
  Bug           Stable codebase End               Played a key role in developing recruitment oriented web app & hybrid mobile apps
 Requirements                                    (incl. MVP) from ground up in Meteor
                  to end Testing                   App was successful in securing funding worth USD 230k forthe startup
    Mobile Apps Web Apps Highly                 Successfully integrated the Stripe payment gateway
   Scalable Applications Stakeholder                                          testing
                    Management                   Conducted end to end testing & unit to ensure performance, quality, and
                                                             applications
    Process Optimization Solutions               responsiveness of
             Architecture & Delivery               Used eslint as a linter for the javascript code & jshint for static code analysis
                                                  Collaborated with a team of 5 engineers to define, design, and ship new features &
TECHNICAL SKILLS                                  correct multiplebugs
            Angular React Meteor CSS3          Superpixels
 HTMLS Bootstrap MongoDBJquery                 Full Stack JavaScript Developer (Contract)                    New York, US
                MySQL ES6 REST API
       Node NPM JSON OOJS .                       Feb '16 Aug 16
  Atmosphere package manager GIT                Superpixel is a leading web development agency which specialises in blockchain and web
     version control ' JAVA * Python            development projects:
                                                 Tech Stack:
                                                        Meteor, CSS3, HTMLS, icoMoon, Node; NPM (private & public) JSON, 00
                                                   JavaScript; Responsive front end, Atmosphere package manager, underscorels,
                                                  Blaze, Stripe, git version control, fullcalendar
                                                   Instrumental in creating the web apps & hybrid mobile apps from scratch for 2
                                                startups: Crew.do & MantaPlay