                     JOHANN BACH
                                FRONT-<PERSON>ND DEVELOPER
            CONTACT             CAREER OBJECTIVE
    j<PERSON><PERSON><PERSON>@emailcom          Front-End Developer with proven experience at Zillow and HubSpot in
         (123) 456-7890           lplng companies create and maintain better code base for reusability:
           Portland, OR          Capable of continuous learning from senior developers while still
    bachtothefuture com         nurturing junior developers Experience in driving projects forward as the
linkedin com/in/jo-bach         development team leader; facilitating projects from concept to launch:
                                 Passionate about learning and development with a desire to apply skills
                                on a larger development team at Redfin:Eager to tackle more complex
        EDUCATION               problems and continue to find ways to maximize user efficiency:
        Bachelor of Science      WORK EXPERIENCE
           Computer Science     Front-End Developer
       University of Oregon
                  2014 2018      HubSpot
                 Eugene, OR      May2020 June 2021      Remote
                                      Developed membership event, and legal platform technology
               SKILLS                 solutions, and automated internal processes
                                      Generated S50K+ in annual adrevenue as system administrator
                  Languages           of a large network of websites_
                    HTML              Designed and implemented web applications along with 3rd-party
                                             integrations as web team liaison for all inter-
                        CSS           software
                 JavaScript           departmental and customer-facing projects.
                     Python           Developed _ node.js server tovalidate membership andtrack
                                      digital badges being used, saving the company $22.5K
                  Libraries           Mentored  team members, enabling them to achieve professional
                      React          growth and personal goals
                     jQuery     SoftwareEng
                      Redux      Zillow         ineer
                 Frameworks     July 2018 - March 2020      Portland, OR
                                      Developed    estate valuation analytics, and platform technology
                 Angular;js                    real
                    Node:js           solutions as part of  person team:
                                      Designed and developed Zestimate; which manages. volume of
                    Testing           6oOk+ valuations per month:
                                      Reduced average valuation review time by 46%,  saved service
                       Jest                                                    and
                                      providers an average of 84% due to click-fee reductions_
                                      Rebuilt 4-year-old SaaS application in React 15 and Redux with full
                                         experience redesign torelease beta MVPin 8 months_
                                      user
                                     Integrated an automated property valuation model fed by machine
                                     learning Python data.
                                     Created non-technical descriptions of operations and workflow to
                                      enable non-coding team to function with minimal interruption;