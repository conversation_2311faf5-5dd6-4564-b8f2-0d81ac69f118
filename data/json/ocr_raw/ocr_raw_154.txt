AMAN                               66
SINHA                              Demonstrated background in object-oriented and functional
                                   programming paradigms Solid understanding of high-level
Front EndDeveloper                 product requirements, HTML standard semantics, and
                                   implementation of ARIA-compliant controls_
8027280990                         Technical proficiency: JavaScript, HTMLS, CSS3, AngularJS, SASS
<EMAIL>
New Delhi, India
Linkedln
              EMPLOYMENT           Frontend Developer                         Jul 2020 Present
              HISTORY              Tata Consultancy Services
                                   New Delhi
                                      Develop new user-facing featuresusingmodern SPA front-end
                                      frameworks (ie; Vue js/ Angular/React) and Javascript:
                                      Validate input before submitting to the back end
                                      Bridge the gap   between  graphic  design  and technical
                                      implementation_
                                      Translate UI/Ux design wireframes to actual code_
                                      Implement  solutions by developing the Vue js and JavaScript
                                      solutions that will be verified under a CI/ CD pipeline enforcing
                                      coding standards of 90% code coverage:
                                      Perform code reviews, and ensure team solution success and
                                      adherence to department standards using Azure Devops_
                                   Frontend Developer                             Aug 2018 Jun
                                   Shiprocket                                             2020
                                   New Delhi
                                      Designed, developed, and tested mobile and desktop software
                                      applications and systems      optimize performance
                                      Implemented features and Uls that
                                      Work cross-functionally with Product Designers & Managers,
                                      and other Engineers to deliver exciting user-facing products_
                                      Translated user and business needs into functional front- end
                                            usingJavaScript; HTML, and CSS_
                                      design
                                      Built reusable code and libraries for future use.
             SKILLS                Object-Oriented Web Applications, Python, PostgresQL Spring
                                   Framework; Git Docker; Cascading Style Sheets (CSS) , User
                                   Interface Design; Web Development; Responsive Web Design;
                                   Node:js
              COURSES              Meta Front-End Developer Professional                  2021
                                   Certificate
                                   Meta
              EDUCATION            B.Tech Software Engineering                            2018
                                   Amity School of Engineering & Technology
                                   New Delhi
              LANGUAGES            Hindi, English