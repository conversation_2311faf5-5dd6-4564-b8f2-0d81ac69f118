  LIAM ANDERSON
      Angular Developer
      <EMAIL> (*************  Los Angeles
      WWWqwikresume.com
      PROFESSIONAL SUMMARY                                                    SKILLS
  Skilled Angular Developer with a passion for creating responsive        User Interface Design
  and efficient web applications_ Over 4 years f experience in
  developing single-page applications using Angular; HTML, CSS,
  andJavaScript: Strong problem-solving abilities and a commitment        Javascript Development
      to best practices in coding and design. Excellent communication
  skills for effective teamwork                                           Progressive Web Apps
      WORK EXPERIENCE                                                     Microservices
  Angular Developer                            Jan /2024-Ongoing
  Seaside Innovations                           Santa Monica, CA          JSON
   Developed user interfaces utilizing Angular best practices to
   ensure a seamless user experience.
      2. Implemented responsive design principles using CSS and HTML           INTERESTS
   for cross-device compatibility.
 3. Collaborated with backend developers to integrate RESTful APIs,
   ensuring efficient data handling:                                         Home Brewing      Wildlife
 4. Conducted code reviews and maintained documentation for                                    Conservation
   ongoing projects                                                          Running           Public Speaking
 5. Participated in agile development practices, contributing to sprint
   planning and retrospectives:
 6. Utilized version control systems to manage code changes and                STRENGTHS
   collaborate with team members_
      7. Engaged in testing and debugging to ensure high-quality
   software delivery:                                                         Willingness      Wisdom
         Angular Developer                     Jan 2023-Jan 2024              Zeal     Ingenuity
      Silver Lake Enterprises                      1 Seattle, WA
      1. Contributed to the development of a dynamic web application for               LANGUAGES
   Allstate Corporation, enhancing user engagement and
   functionality:
 2. Utilized AngularJs, HTML, and CSS to create an intuitive                                      Q
   administrative Ul that improved user interactions:
 3. Integrated back-end RESTful services using AJAX for efficient
   data retrieval and submission.
 4. Implemented Angular Google Maps and custom Ul components to             English     German       Polish
   enhance application features.
 5. Designed responsive web pages using Bootstrap and Media
   Queries for optimal viewing across devices                                 ACHIEVEMENTS
      EDUCATION                                                               Improved application performance
                                                                              by 30% through optimized Angular
      Bachelor of Science in Computer           Jan /   Jan /                 code;
  Science                                        2022   2023                  Collaborated on a project that
                                                                                      loading times by 40%
  State University                                     Denver; CO             reduced  loading techniques:
  Focused on software development and web technologies_                       usinglazy
Powered by  Qwikresume