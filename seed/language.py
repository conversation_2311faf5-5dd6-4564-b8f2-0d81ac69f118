from random import randint

from faker import Faker

from models.account import Account
from models.languages import Language
from seed.define_constants import LANGUAGES
from utils import get_instance, setup_logger

_, db = get_instance()


def language_seeder():
    logger = setup_logger()
    try:
        logger.info("Start seeding Languages...")
        fake = Faker()
        query = Account.query.order_by(Account.created_at.desc())  # type: ignore

        for i in range(query.count()):
            account = query.offset(i).first()
            if not account:
                continue

            selected_languages = fake.random_elements(elements=LANGUAGES, unique=True, length=randint(1, len(LANGUAGES)))
            for language in selected_languages:
                if language["name"].lower() == "jlpt":
                    certificate_name = fake.random_element(elements=["N1", "N2", "N3", "N4", "N5"])
                elif language["name"].lower() == "ielts":
                    certificate_name = f"{round(fake.random.uniform(1.0, 9.0), 1)}"
                elif language["name"].lower() == "toeic":
                    certificate_name = str(randint(10, 990))
                else:
                    certificate_name = f"{language['name']} Proficiency Certificate"

                language_model = Language(
                    account_id=account.account_id,
                    language_name=language["name"],
                    language_score=str(randint(60, 100)),  # More realistic score range
                    language_certificate_name=certificate_name,
                    language_certificate_date=fake.date_between(start_date='-5y', end_date='today'),  # Certificate date within the last 5 years
                )
                db.session.add(language_model)

        db.session.commit()
        logger.info("Finished seeding Languages.")
    except Exception as error:
        db.session.rollback()
        logger.error(error)
