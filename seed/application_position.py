from random import randint

from faker import Faker

from models.account import Account
from models.application_position import ApplicationPosition
from models.constant import Constant
from seed.define_constants import POSITIONS, SALARY_RANGES
from utils import get_instance, setup_logger

_, db = get_instance()


def application_position_seeder():
    logger = setup_logger()
    try:
        fake = Faker()
        logger.info("Start seeding Application Positions...")

        total_users = Account.query.count()
        query = Account.query.order_by(Account.created_at.desc())  # type: ignore

        for idx in range(total_users):
            user = query.offset(idx).first()
            if not user:
                continue

            for _ in range(randint(1, len(POSITIONS))):
                salary_currency = "USD" if randint(0, 1) == 0 else "VND"
                salary = randint(300, 10000) if salary_currency == "USD" else randint(7000000, *********)
                
                application_position = ApplicationPosition(
                    account_id=user.account_id,
                    apply_position_title=POSITIONS[randint(0, len(POSITIONS) - 1)],
                    salary=f"{salary} {salary_currency}",
                    description=fake.text(),
                )
                db.session.add(application_position)

        db.session.commit()
        logger.info("Finished seeding Application Positions...")
    except Exception as error:
        db.session.rollback()
        logger.error(error)
