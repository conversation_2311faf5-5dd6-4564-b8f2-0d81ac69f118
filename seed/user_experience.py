from random import randint

from faker import Faker

from models.user import User
from models.user_experience import UserExperience
from utils import get_instance, setup_logger

_, db = get_instance()


def user_experience_seeder(repeat_times=1000):
    logger = setup_logger()
    try:
        logger.info("Start seeding User Experiences...")
        fake = Faker()

        query = User.query.order_by(User.created_at.desc())  # type: ignore
        for i in range(repeat_times):
            account = query.offset(i).first()
            if not account:
                continue

            for _ in range(randint(1, 5)):
                start_date = fake.date_this_decade()

                user_experience = UserExperience(
                    account_id=account.account_id,
                    experience_start_time=start_date,
                    experience_end_time=fake.date_between(start_date),
                    experience_title=fake.job(),
                    position=fake.job(),
                    work_place=fake.company(),
                    description=fake.text()
                )
                db.session.add(user_experience)

        db.session.commit()
        logger.info("Finished seeding User Experiences.")
    except Exception as error:
        db.session.rollback()
        logger.error(error)
