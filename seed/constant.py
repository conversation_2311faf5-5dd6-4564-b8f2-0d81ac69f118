from models.constant import Constant
from seed.define_constants import (
    EXPERIENCE_TYPES,
    EXPERIENCE_TYPES_PREFIX,
    LANGUAGES,
    LANGUAGES_PREFIX,
    NOTIFICATIONS,
    NOTIFICATIONS_PREFIX,
    POSITIONS,
    POSITIONS_PREFIX,
    SALARY_RANGES,
    SALARY_RANGES_PREFIX,
    SKILLS,
    SKILLS_PREFIX,
    SYSTEM_ROLES,
)
from utils import get_instance, setup_logger

_, db = get_instance()


def common_constants(type, prefix: str):
    for idx, name in enumerate(type):
        constant = Constant(constant_name=name, prefix=prefix, index=idx, description={"note": "Common constant"})
        db.session.add(constant)


def constant_seeder():
    logger = setup_logger()
    try:
        logger.info("Start seeding Constants...")

        # Seed SYSTEM_ROLE constants
        for idx, (name, prefix) in enumerate(SYSTEM_ROLES):
            constant = Constant(constant_name=name, prefix=prefix, index=idx, description={"note": "System role"})
            db.session.add(constant)

        # Seed NOTIFICATION constants
        common_constants(NOTIFICATIONS, NOTIFICATIONS_PREFIX)

        db.session.commit()
        logger.info("Finished seeding Constants.")
    except Exception as error:
        db.session.rollback()
        logger.error(error)
