#!/bin/bash

# Test script cho hệ thống Swipe Feedback đ<PERSON><PERSON> giản
# Chỉ test 2 API: swipe like và swipe dislike

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BASE_URL="http://localhost:8081/api/v1"
FLASK_PASSWORD="default"  # Thay đổi theo config của bạn

# Test data
USER_ID="af47578a-cc80-4638-958a-72ea1a6356e5"  # User ID test
COMPANY_ID="af3810de-36de-4fd0-9e5c-775421ae4c56"  # Company ID test
JOB_ID="51fec1b3-44d0-4489-880a-7d578f8dbda5"  # Job ID test

echo -e "${BLUE}=== Test Simple Swipe Feedback System ===${NC}"
echo ""

# Function to print test step
print_step() {
    echo -e "${YELLOW}Step $1: $2${NC}"
}

# Function to print success
print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

# Function to print error
print_error() {
    echo -e "${RED}✗ $1${NC}"
}

# Function to make API call and check response
api_call() {
    local method=$1
    local url=$2
    local data=$3
    local description=$4
    
    echo "Making $method request to: $url"
    if [ -n "$data" ]; then
        echo "Data: $data"
    fi
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "\n%{http_code}" "$url")
    else
        response=$(curl -s -w "\n%{http_code}" -X $method -H "Content-Type: application/json" -d "$data" "$url")
    fi
    
    http_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | head -n -1)
    
    echo "HTTP Code: $http_code"
    echo "Response: $body"
    
    if [ "$http_code" -ge 200 ] && [ "$http_code" -lt 300 ]; then
        print_success "$description"
        echo "$body"
    else
        print_error "$description failed with HTTP $http_code"
        echo "$body"
        return 1
    fi
    echo ""
}

# Step 1: Test health check
print_step "1" "Test health check"
api_call "GET" "$BASE_URL/../health" "" "Health check"

# Step 2: Test swipe LIKE for a job
print_step "2" "Test swipe LIKE for a job"
like_data="{\"key\":\"$FLASK_PASSWORD\",\"user_id\":\"$USER_ID\",\"item_id\":\"$JOB_ID\",\"item_type\":\"job\"}"
api_call "POST" "$BASE_URL/personalization/swipe/like" "$like_data" "Swipe LIKE for job"

# Step 3: Test swipe DISLIKE for a job
print_step "3" "Test swipe DISLIKE for a job"
dislike_data="{\"key\":\"$FLASK_PASSWORD\",\"user_id\":\"$USER_ID\",\"item_id\":\"$JOB_ID\",\"item_type\":\"job\"}"
api_call "POST" "$BASE_URL/personalization/swipe/dislike" "$dislike_data" "Swipe DISLIKE for job"

# Step 4: Test swipe LIKE for a company
print_step "4" "Test swipe LIKE for a company"
like_company_data="{\"key\":\"$FLASK_PASSWORD\",\"user_id\":\"$USER_ID\",\"item_id\":\"$COMPANY_ID\",\"item_type\":\"company\"}"
api_call "POST" "$BASE_URL/personalization/swipe/like" "$like_company_data" "Swipe LIKE for company"

# Step 5: Test swipe DISLIKE for a company
print_step "5" "Test swipe DISLIKE for a company"
dislike_company_data="{\"key\":\"$FLASK_PASSWORD\",\"user_id\":\"$USER_ID\",\"item_id\":\"$COMPANY_ID\",\"item_type\":\"company\"}"
api_call "POST" "$BASE_URL/personalization/swipe/dislike" "$dislike_company_data" "Swipe DISLIKE for company"

# Step 6: Test multiple swipes to simulate real usage
print_step "6" "Test multiple swipes to simulate real usage"

echo "Simulating 5 swipes..."
for i in {1..5}; do
    # Alternate between like and dislike
    if [ $((i % 2)) -eq 1 ]; then
        feedback_type="like"
        data="{\"key\":\"$FLASK_PASSWORD\",\"user_id\":\"$USER_ID\",\"item_id\":\"test-item-$i\",\"item_type\":\"job\"}"
        url="$BASE_URL/personalization/swipe/like"
    else
        feedback_type="dislike"
        data="{\"key\":\"$FLASK_PASSWORD\",\"user_id\":\"$USER_ID\",\"item_id\":\"test-item-$i\",\"item_type\":\"job\"}"
        url="$BASE_URL/personalization/swipe/dislike"
    fi
    
    echo "Swipe $i: $feedback_type"
    api_call "POST" "$url" "$data" "Swipe $i ($feedback_type)"
done

# Step 7: Test error cases
print_step "7" "Test error cases"

echo "Testing missing user_id..."
invalid_data="{\"key\":\"$FLASK_PASSWORD\",\"item_id\":\"$JOB_ID\",\"item_type\":\"job\"}"
curl -s -X POST -H "Content-Type: application/json" -d "$invalid_data" "$BASE_URL/personalization/swipe/like" | echo "Response: $(cat)"

echo ""
echo "Testing missing key..."
no_key_data="{\"user_id\":\"$USER_ID\",\"item_id\":\"$JOB_ID\",\"item_type\":\"job\"}"
curl -s -X POST -H "Content-Type: application/json" -d "$no_key_data" "$BASE_URL/personalization/swipe/like" | echo "Response: $(cat)"

echo ""
echo "Testing invalid key..."
invalid_key_data="{\"key\":\"wrong_key\",\"user_id\":\"$USER_ID\",\"item_id\":\"$JOB_ID\",\"item_type\":\"job\"}"
curl -s -X POST -H "Content-Type: application/json" -d "$invalid_key_data" "$BASE_URL/personalization/swipe/like" | echo "Response: $(cat)"

echo ""
echo -e "${GREEN}=== Test completed! ===${NC}"
echo ""
echo -e "${BLUE}Summary:${NC}"
echo "- Health check: ✓"
echo "- Swipe LIKE for job: ✓"
echo "- Swipe DISLIKE for job: ✓"
echo "- Swipe LIKE for company: ✓"
echo "- Swipe DISLIKE for company: ✓"
echo "- Multiple swipes simulation: ✓"
echo "- Error cases testing: ✓"
echo ""
echo -e "${YELLOW}The swipe feedback APIs are working!${NC}"
echo -e "${YELLOW}Next step: Implement background processing to update personalization matrix.${NC}"
