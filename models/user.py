from datetime import datetime

from sqlalchemy import (
    ARRA<PERSON>,
    JSON,
    TIMESTAMP,
    UUID,
    Boolean,
    Column,
    ForeignKey,
    String,
    Text,
)

from utils import get_instance

_, db = get_instance()


class User(db.Model):
    __tablename__ = "users"

    account_id = Column(
        UUID(as_uuid=True),
        ForeignKey(
            "accounts.account_id",
            match="FULL",
            onupdate="NO ACTION",
            ondelete="CASCADE",
        ),
        nullable=False,  # Kept for foreign key
        primary_key=True,
    )
    date_of_birth = Column(TIMESTAMP)
    first_name = Column(String(1000))
    gender = Column(Boolean)
    last_name = Column(String(1000))
    others = Column(JSON)
    social_media_link = Column(ARRAY(String(1000)))
    summary_introduction = Column(Text)
    created_at = Column(TIMESTAMP)
    updated_at = Column(TIMESTAMP)

    def __repr__(self) -> str:
        return f"<User {self.account_id}>"

    def __init__(
        self,
        account_id,
        date_of_birth,
        first_name,
        gender,
        last_name,
        social_media_link=[],
        summary_introduction="",
    ):
        self.account_id = account_id
        self.date_of_birth = date_of_birth
        self.first_name = first_name
        self.gender = gender
        self.last_name = last_name
        self.social_media_link = social_media_link
        self.summary_introduction = summary_introduction
        self.created_at = datetime.now()
