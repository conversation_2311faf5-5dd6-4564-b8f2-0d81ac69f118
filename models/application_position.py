from datetime import datetime
from uuid import uuid4

from sqlalchemy import TIMESTAMP, UUID, Boolean, Column, ForeignKey, String

from utils import get_instance

_, db = get_instance()


class ApplicationPosition(db.Model):
    __tablename__ = "application_positions"

    id = Column(UUID(as_uuid=True), nullable=False, primary_key=True)
    account_id = Column(
        UUID(as_uuid=True),
        ForeignKey("accounts.account_id", match="FULL", onupdate="NO ACTION", ondelete="CASCADE"),
        nullable=False,
    )
    apply_position_title = Column(String(1000))
    salary = Column(String(1000))
    status = Column(Boolean, default=True)
    description = Column(String(10000), nullable=True)
    created_at = Column(TIMESTAMP)
    updated_at = Column(TIMESTAMP, nullable=True)

    def __repr__(self) -> str:
        return f"<ApplicationPosition {self.id}>"

    def __init__(self, account_id, apply_position_title, salary, description=None, status=True):
        self.id = uuid4()
        self.account_id = account_id
        self.apply_position_title = apply_position_title
        self.salary = salary
        self.description = description
        self.status = status
        self.created_at = datetime.now()
        self.updated_at = datetime.now()