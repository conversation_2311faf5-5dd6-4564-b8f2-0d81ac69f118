from datetime import datetime
from uuid import uuid4

from sqlalchemy import <PERSON><PERSON>ESTAMP, UUID, Column, ForeignKey, String

from utils import get_instance

_, db = get_instance()


class ApplicationSkill(db.Model):
    __tablename__ = "application_skills"

    id = Column(UUID(as_uuid=True), nullable=False, primary_key=True)
    application_position_id = Column(
        UUID(as_uuid=True),
        ForeignKey("application_positions.id", match="FULL", onupdate="NO ACTION", ondelete="CASCADE"),
        nullable=False,
    )
    skill_name = Column(String(1000))
    description = Column(String(10000), nullable=True)
    created_at = Column(TIMESTAMP)
    updated_at = Column(TIMESTAMP, nullable=True)

    def __repr__(self) -> str:
        return f"<ApplicationSkill {self.id}>"

    def __init__(self, application_position_id, skill_name, description=None):
        self.id = uuid4()
        self.application_position_id = application_position_id
        self.skill_name = skill_name
        self.description = description
        self.created_at = datetime.now()
        self.updated_at = datetime.now()
