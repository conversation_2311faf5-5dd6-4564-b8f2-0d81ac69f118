from datetime import datetime
from sqlalchemy import Column, UUID, String, Integer, LargeBinary, JSON, TIMESTAMP, func
from utils import get_instance

_, db = get_instance()


class PersonalizationMatrix(db.Model):
    __tablename__ = "personalization_matrices"

    id = Column(UUID(as_uuid=True), primary_key=True, server_default=func.gen_random_uuid())
    entity_id = Column(String, nullable=False)  # Changed from UUID to String to support composite IDs
    entity_type = Column(String, nullable=False)  # 'user' hoặc 'job'
    matrix = Column(LargeBinary, nullable=False)  # Ma trận được serialize thành bytes
    dimension = Column(Integer, nullable=False)
    matrix_metadata = Column('metadata', JSON, nullable=True)  # Use column name 'metadata' but attribute name 'matrix_metadata'
    created_at = Column(TIMESTAMP, nullable=True, server_default=func.now())
    updated_at = Column(TIMESTAMP, nullable=True, server_default=func.now(), onupdate=func.now())

    def __repr__(self) -> str:
        return f"<PersonalizationMatrix {self.id} - {self.entity_type}:{self.entity_id}>"

    def __init__(self, entity_id, entity_type, matrix_bytes, dimension, metadata=None):
        self.entity_id = entity_id
        self.entity_type = entity_type
        self.matrix = matrix_bytes
        self.dimension = dimension
        self.matrix_metadata = metadata or {}
