from datetime import datetime
from uuid import uuid4

from sqlalchemy import TIMESTAMP, UUID, Boolean, Column, ForeignKey, Numeric, String

from utils import get_instance

_, db = get_instance()


class UserEducation(db.Model):
    __tablename__ = "user_educations"

    id = Column(UUID(as_uuid=True), nullable=False, primary_key=True)
    account_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.account_id", match="FULL", onupdate="NO ACTION", ondelete="CASCADE"),
        nullable=False,
    )
    cpa = Column(Numeric(100, 10))
    majority = Column(String(1000), nullable=True)
    description = Column(String(1000), nullable=True)
    study_end_time = Column(TIMESTAMP, nullable=True)
    study_place = Column(String(1000))
    study_start_time = Column(TIMESTAMP)
    is_university = Column(Boolean, default=True)
    created_at = Column(TIMESTAMP)
    updated_at = Column(TIMESTAMP, nullable=True)

    def __repr__(self) -> str:
        return f"<UserEducation {self.id}>"

    def __init__(
        self,
        account_id,
        cpa,
        study_place,
        study_start_time,
        majority="",
        study_end_time=None,
        is_university=True,
        description=None,
    ):
        self.id = uuid4()
        self.account_id = account_id
        self.cpa = cpa
        self.majority = majority
        self.study_end_time = study_end_time
        self.study_place = study_place
        self.study_start_time = study_start_time
        self.is_university = is_university
        self.created_at = datetime.now()
        self.updated_at = datetime.now()
        self.description = description
