from datetime import datetime
from uuid import uuid4

from sqlalchemy import TIMESTAMP, UUID, Column, ForeignKey, String

from utils import get_instance

_, db = get_instance()


class Language(db.Model):
    __tablename__ = "languages"

    id = Column(UUID(as_uuid=True), nullable=False, primary_key=True)
    account_id = Column(
        UUID(as_uuid=True),
        ForeignKey("accounts.account_id", match="FULL", onupdate="NO ACTION", ondelete="CASCADE"),
        nullable=False,
    )
    language_name = Column(String(1000))
    language_score = Column(String(1000))
    language_certificate_name = Column(String(1000), nullable=True)
    language_certificate_date = Column(TIMESTAMP, nullable=True)
    created_at = Column(TIMESTAMP)
    updated_at = Column(TIMESTAMP, nullable=True)

    def __repr__(self) -> str:
        return f"<Language {self.id}>"

    def __init__(
        self,
        account_id: str,
        language_score: str,
        language_certificate_date: str = "",
        language_certificate_name: str = None,
        language_name: str = None,
    ):
        self.id = uuid4()
        self.account_id = account_id
        self.language_score = language_score
        self.language_certificate_date = language_certificate_date
        self.created_at = datetime.now()
        self.updated_at = datetime.now()
        self.language_certificate_name = language_certificate_name
        self.language_name = language_name
