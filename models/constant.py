from datetime import datetime
from uuid import uuid4

from sqlalchemy import <PERSON>Constraint, JSON, TIMESTAMP, UUID, Column, String

from utils import get_instance

_, db = get_instance()

TYPE_DIGITS = 7


def generate_type(prefix: str, index: int) -> str:
    missing_digits = TYPE_DIGITS - len(str(index)) - len(prefix)
    return prefix + "".join("0" for _ in range(missing_digits)) + str(index)


class Constant(db.Model):
    __tablename__ = "constants"

    constant_id = Column(UUID(as_uuid=True), nullable=False, primary_key=True)
    constant_name = Column(String(1000))
    constant_type = Column(String(1000), nullable=False, unique=True)
    description = Column(JSON, nullable=True)
    created_at = Column(TIMESTAMP)
    updated_at = Column(TIMESTAMP, nullable=True)

    __table_args__ = (
        CheckConstraint(
            "constant_type IN ('SYSTEM_ROLE', 'NOTIFICATION')",
            name="valid_constant_type",
        ),
    )

    def __repr__(self) -> str:
        return f"<Constant {self.constant_id}>"

    def __init__(self, constant_name: str, prefix: str, index: int, description=None):
        self.constant_id = uuid4()
        self.constant_name = constant_name
        self.constant_type = generate_type(prefix, index)
        self.created_at = datetime.now()
        self.updated_at = datetime.now()
        self.description = description if description else {}
