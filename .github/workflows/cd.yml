name: Continuous Deployment

on:
    push:
        branches:
            - main
    workflow_dispatch: {}

jobs:
    deploy:
        name: Deploy Application
        runs-on: ubuntu-latest

        steps:
            - name: Checkout Code
              uses: actions/checkout@v3

            - name: Set up Python
              uses: actions/setup-python@v4
              with:
                  python-version: '3.12'

            # - name: Install Dependencies
            #   run: |
            #       python -m pip install --upgrade pip
            #       pip install -r requirements.txt

            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v4
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: ap-southeast-2

            - name: Log in to Amazon ECR
              id: login-ecr
              uses: aws-actions/amazon-ecr-login@v2

            - name: Build, tag, and push Docker image to ECR
              env:
                  ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
                  ECR_REPOSITORY: job-swipe/recommendation
                  IMAGE_TAG: latest
              run: |
                  docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
                  docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG

            - name: Deploy to EC2
              env:
                  EC2_HOST: ${{ secrets.EC2_HOST }}
                  EC2_USER: ${{ secrets.EC2_USER }}
                  SSH_PRIVATE_KEY: ${{ secrets.EC2_SSH_KEY }}
                  ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
              run: |
                  echo "$SSH_PRIVATE_KEY" > private_key.pem
                  chmod 600 private_key.pem

                  ssh -o StrictHostKeyChecking=no -i private_key.pem $EC2_USER@$EC2_HOST bash << EOF
                    export ECR_REGISTRY=${{ steps.login-ecr.outputs.registry }}

                    cd /home/<USER>/job_swipe

                    aws ecr get-login-password --region ap-southeast-2 | docker login --username AWS --password-stdin \$ECR_REGISTRY
                    # Pull the latest image
                    docker-compose pull --no-cache

                    # Stop and remove existing containers
                    docker-compose down

                    # Remove any cached images for the service
                    docker rmi 992382651472.dkr.ecr.ap-southeast-2.amazonaws.com/job-swipe/recommendation:latest || true

                    # Start containers with the latest image
                    docker-compose up -d --force-recreate

                    # Clean up unused images and resources
                    docker system prune -af
                  EOF

                  rm private_key.pem
