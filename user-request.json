{"key": "QGFx3qWO487TkpHFSvmlhvAsI", "data": {"account_id": "5728a90b-98b1-4d14-99ca-3782cd0e893f", "email": "<EMAIL>", "account_status": true, "first_name": "Văn A", "last_name": "<PERSON><PERSON><PERSON><PERSON>", "summary_introduction": "Tôi là một lập trình viên NodeJS với hơn 3 năm kinh nghiệm, chuyên về JavaScript và TypeScript, React, Express và MongoDB.", "educations": [{"study_place": "<PERSON><PERSON><PERSON> h<PERSON> Bách Khoa TP.HCM", "majority": "<PERSON><PERSON><PERSON> h<PERSON><PERSON> m<PERSON> t<PERSON>h", "study_start_time": "2015-09-01T00:00:00Z", "study_end_time": "2019-05-30T00:00:00Z"}], "experiences": [{"position": "NodeJS Developer", "work_place": "Tech Software", "experience_start_time": "2019-06-01T00:00:00Z", "experience_end_time": "2022-05-30T00:00:00Z", "note": "Phát triển các API RESTful, xây dựng và tối ưu hóa backend cho <PERSON>ng dụng thương mại điện tử."}, {"position": "Senior NodeJS Developer", "work_place": "Global Solutions", "experience_start_time": "2022-06-01T00:00:00Z", "experience_end_time": null, "note": "<PERSON><PERSON><PERSON> dựng kiến trúc microservices, áp dụng DevOps và CI/CD, triển khai hệ thống thanh toán trực tuyến."}], "languages": [{"language_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"language_name": "Tiếng <PERSON>"}]}}