services:
  web:
    build:
      context: .
      dockerfile: Dockerfile
    restart: always
    container_name: recommendation-server
    environment:
      - FLASK_HOST=${FLASK_HOST}
      - FLASK_ENV=${FLASK_ENV}
      - FLASK_PASSWORD=${FLASK_PASSWORD}
      - DEFAULT_PASSWORD=${DEFAULT_PASSWORD}
      - DATABASE_URI=${DATABASE_URI}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - CHROMA_HOST=chroma
      - CHROMA_PORT=8000
      - S3_BUCKET_NAME=${S3_BUCKET_NAME}
      - S3_ACCESS_KEY=${S3_ACCESS_KEY}
      - S3_SECRET_KEY=${S3_SECRET_KEY}
      - S3_REGION=${S3_REGION}
    ports:
      - "8081:5000"
    env_file:
      - .env
    volumes:
      - .:/app
    command: flask run --host=0.0.0.0 --reload
    depends_on:
      - chroma
    networks:
      - net

  chroma:
    image: ghcr.io/chroma-core/chroma:0.3.26
    restart: always
    container_name: chroma-db
    volumes:
      - chroma_data:/chroma/chroma
    environment:
      - CHROMA_SERVER_HOST=0.0.0.0
      - CHROMA_SERVER_PORT=8000
      - CHROMA_SERVER_CORS_ALLOW_ORIGINS=["*"]
      - ALLOW_RESET=true
      - IS_PERSISTENT=TRUE
    ports:
      - "8000:8000"
    networks:
      - net
    healthcheck:
      test: [ "CMD", "/bin/sh", "-c", "cat < /dev/null > /dev/tcp/localhost/8000" ]
      interval: 5s
      timeout: 3s
      retries: 3
      start_period: 10s

volumes:
  chroma_data:
    driver: local

networks:
  net:
    driver: bridge
