from flask import Blueprint, jsonify
import logging
import os

from utils.response import AppResponse

health_bp = Blueprint("health", __name__, url_prefix="/api/v1/health")
logger = logging.getLogger(__name__)

@health_bp.route("/", methods=["GET"])
def health_check():
    """
    Health check endpoint to verify the service is running.
    """
    return "Service is healthy", 200

@health_bp.route("/all", methods=["GET"])
def all_services_health():
    """
    Health check endpoint to verify all services are running.
    """
    health_status = {
        "app": "healthy",
        "chroma": "unknown",
        "personalization": "unknown"
    }

    # Check ChromaDB health
    try:
        # Import here to avoid circular imports
        from vector import get_client

        client = get_client()
        if client is not None:
            try:
                client.list_collections()
                health_status["chroma"] = "healthy"
            except Exception as e:
                logger.warning(f"ChromaDB health check failed: {e}")
                health_status["chroma"] = "unhealthy"
        else:
            health_status["chroma"] = "unavailable"
    except Exception as e:
        logger.warning(f"Error checking ChromaDB health: {e}")
        health_status["chroma"] = "error"
    
    # Check Personalization health - verify user_matrices directory exists
    try:
        user_matrices_dir = os.path.join(os.getcwd(), "user_matrices")
        
        if os.path.exists(user_matrices_dir) and os.path.isdir(user_matrices_dir):
            health_status["personalization"] = "healthy"
        else:
            # Try to create the directory
            try:
                os.makedirs(user_matrices_dir, exist_ok=True)
                health_status["personalization"] = "healthy"
            except Exception as e:
                logger.warning(f"Error creating user_matrices directory: {e}")
                health_status["personalization"] = "unhealthy"
    except Exception as e:
        logger.warning(f"Error checking personalization health: {e}")
        health_status["personalization"] = "error"

    return AppResponse.success_with_data(data=health_status)

@health_bp.route("/personalization", methods=["GET"])
def personalization_health():
    """
    Health check endpoint for personalization service.
    Verifies the user_matrices directory exists and can list users.
    """
    try:
        # Import personalization modules
        from personalization.matrix_storage import get_matrix_storage
        
        # Check if directory exists and is accessible
        matrix_storage = get_matrix_storage()
        users = matrix_storage.list_users()
        
        # Try creating a test matrix
        import numpy as np
        test_matrix = np.eye(5)
        user_id = "_health_test"
        
        success, message = matrix_storage.save_user_matrix(
            test_matrix,
            user_id,
            {"note": "Health check test"}
        )
        
        if not success:
            return AppResponse.server_error(error=f"Cannot write to user_matrices: {message}")
            
        # Clean up test matrix
        matrix_storage.delete_user_matrix(user_id)
        
        return AppResponse.success_with_data(
            data={
                "status": "healthy",
                "storage_directory": matrix_storage.storage_dir,
                "user_count": len(users)
            },
            message="Personalization service is healthy"
        )
    except Exception as e:
        logger.error(f"Personalization health check failed: {e}")
        return AppResponse.server_error(error=f"Personalization service is not healthy: {str(e)}")
