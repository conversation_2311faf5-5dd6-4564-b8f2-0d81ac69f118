import logging
import sys

# <PERSON><PERSON><PERSON><PERSON> lập logging chuẩn cho <PERSON>lask + <PERSON><PERSON> + Docker
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s %(levelname)s - %(name)s || %(message)s",
    handlers=[logging.StreamHandler(sys.stdout)]
)

from data import data_bp
from data.user_data import normalize_bp
from embed import embed_bp
from recommend import recommend_bp
from seed import seed_bp
from utils import get_instance, setup_logger
from utils.environment import Env
from health import health_bp
from vector import vector_bp
from extract import extract_bp
from personalization import personalization_bp
# Swipe feedback will be added to personalization module

# Setup logging
logger = logging.getLogger(__name__)
logger.info("Setting up Flask application...")

app, _ = get_instance()

app.register_blueprint(data_bp)
app.register_blueprint(seed_bp)
app.register_blueprint(recommend_bp)
app.register_blueprint(normalize_bp)
app.register_blueprint(health_bp)
app.register_blueprint(vector_bp)
app.register_blueprint(extract_bp)
app.register_blueprint(personalization_bp)
# Swipe feedback endpoints will be added to personalization module
app.register_blueprint(embed_bp)

if __name__ == "__main__":
    print("Starting Flask server...")
    print(f"FLASK_ENV: {Env.FLASK_ENV}")
    print(f"FLASK_HOST: {Env.FLASK_HOST}")
    print(f"FLASK_PASSWORD: {Env.FLASK_PASSWORD}")
    print(f"MOBILE_APP_URL: {Env.MOBILE_APP_URL}")
    print(f"DATABASE_URI: {Env.DATABASE_URI}")
    print(f"JWT_SECRET_KEY: {Env.JWT_SECRET_KEY}")
    print(f"DEFAULT_PASSWORD: {Env.DEFAULT_PASSWORD}")
    print(f"CHROMA_HOST: {Env.CHROMA_HOST}")
    print(f"CHROMA_PORT: {Env.CHROMA_PORT}")
    print(f"OPENAI_API_KEY: {'Được cấu hình' if hasattr(Env, 'OPENAI_API_KEY') and Env.OPENAI_API_KEY else 'Chưa cấu hình'}")


    app.run(
        debug=False if Env.FLASK_ENV == "production" else True, port=8081, threaded=True
    )
