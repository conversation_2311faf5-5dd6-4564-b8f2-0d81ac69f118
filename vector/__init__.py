from flask import Blueprint, request
import uuid
import logging
import numpy as np
from utils.environment import Env
from utils.response import AppResponse
from vector.visualization import VectorVisualizer
import time

vector_bp = Blueprint("vector", __name__, url_prefix="/api/v1/vector")
logger = logging.getLogger(__name__)

def get_client():
    """Get ChromaDB client with error handling and retry mechanism"""
    max_retries = 3
    retry_delay = 1

    for attempt in range(max_retries):
        try:
            from utils.chroma_db import get_chroma_client
            client = get_chroma_client()
            if client is not None:
                logger.info(f"Successfully obtained ChromaDB client (attempt {attempt+1})")
                return client
            else:
                logger.warning(f"ChromaDB client is None (attempt {attempt+1})")
        except Exception as e:
            logger.error(f"Failed to import or initialize ChromaDB client (attempt {attempt+1}): {e}")

        if attempt < max_retries - 1:
            logger.info(f"Retrying to get ChromaDB client in {retry_delay} seconds...")
            import time
            time.sleep(retry_delay)
            retry_delay *= 2  # Exponential backoff

    logger.error(f"Failed to get ChromaDB client after {max_retries} attempts")
    return None

@vector_bp.route("/collections", methods=["GET"])
def list_collections():
    """List all collections in ChromaDB"""
    try:
        flask_key = request.args.get("key", "")
        if flask_key != Env.FLASK_PASSWORD:
            return AppResponse.bad_request(message="Forbidden", status_code=403)

        client = get_client()
        if client is None:
            return AppResponse.server_error(error="ChromaDB service is not available")

        # Get fresh list of collections
        logger.info("Retrieving collections from ChromaDB")
        collections = client.list_collections()
        logger.info(f"Found {len(collections)} collections")

        # Convert Collection objects to serializable dictionaries
        collection_list = []
        for collection in collections:
            # Get collection with a direct reference to ensure fresh data
            try:
                # Get the collection directly to ensure we have the latest data
                coll = client.client.get_collection(collection.name)

                # Get count with retry mechanism
                count = 0
                max_retries = 3
                for attempt in range(max_retries):
                    try:
                        # Get all items to ensure accurate count
                        result = coll.get(include=[])
                        count = len(result["ids"]) if "ids" in result else 0
                        logger.info(f"Collection '{collection.name}' has {count} items (attempt {attempt+1})")
                        break
                    except Exception as count_error:
                        logger.warning(f"Error getting count for collection '{collection.name}' (attempt {attempt+1}): {count_error}")
                        if attempt < max_retries - 1:
                            import time
                            time.sleep(0.5)  # Short delay before retry

                collection_list.append({
                    "name": collection.name,
                    "metadata": collection.metadata,
                    "count": count
                })
                logger.info(f"Added collection '{collection.name}' with count {count}")
            except Exception as coll_error:
                logger.error(f"Error processing collection '{collection.name}': {coll_error}")
                collection_list.append({
                    "name": collection.name,
                    "metadata": collection.metadata,
                    "count": 0
                })

        return AppResponse.success_with_data(data=collection_list)
    except Exception as error:
        logger.error(f"Error listing collections: {error}")
        import traceback
        logger.error(traceback.format_exc())
        return AppResponse.server_error(error=str(error))

@vector_bp.route("/collections", methods=["POST"])
def create_collection():
    """Create a new collection in ChromaDB"""
    try:
        body = request.get_json()
        flask_key = body.get("key", "")
        if flask_key != Env.FLASK_PASSWORD:
            return AppResponse.bad_request(message="Forbidden", status_code=403)

        collection_name = body.get("collection_name")
        metadata = body.get("metadata", {})

        if not collection_name:
            return AppResponse.bad_request(message="Collection name is required")

        client = get_client()
        if client is None:
            return AppResponse.server_error(error="ChromaDB service is not available")

        collection = client.create_collection(collection_name, metadata)
        return AppResponse.success_with_message(
            message=f"Collection '{collection_name}' created successfully"
        )
    except Exception as error:
        logger.error(f"Error creating collection: {error}")
        return AppResponse.server_error(error=error)

@vector_bp.route("/collections/<collection_name>", methods=["DELETE"])
def delete_collection(collection_name):
    """Delete a collection from ChromaDB"""
    try:
        flask_key = request.args.get("key", "")
        if flask_key != Env.FLASK_PASSWORD:
            return AppResponse.bad_request(message="Forbidden", status_code=403)

        client = get_client()
        if client is None:
            return AppResponse.server_error(error="ChromaDB service is not available")

        client.delete_collection(collection_name)
        return AppResponse.success_with_message(
            message=f"Collection '{collection_name}' deleted successfully"
        )
    except Exception as error:
        logger.error(f"Error deleting collection: {error}")
        return AppResponse.server_error(error=error)

@vector_bp.route("/collections/<collection_name>/documents", methods=["POST"])
def add_documents(collection_name):
    """Add documents to a collection in ChromaDB"""
    try:
        body = request.get_json()
        flask_key = body.get("key", "")
        if flask_key != Env.FLASK_PASSWORD:
            return AppResponse.bad_request(message="Forbidden", status_code=403)

        documents = body.get("documents", [])
        metadatas = body.get("metadatas", [])
        ids = body.get("ids", [str(uuid.uuid4()) for _ in range(len(documents))])

        if not documents:
            return AppResponse.bad_request(message="Documents are required")

        if len(metadatas) > 0 and len(metadatas) != len(documents):
            return AppResponse.bad_request(message="Number of metadatas must match number of documents")

        if len(ids) != len(documents):
            return AppResponse.bad_request(message="Number of ids must match number of documents")

        client = get_client()
        if client is None:
            return AppResponse.server_error(error="ChromaDB service is not available")

        client.add_documents(collection_name, documents, metadatas or None, ids)
        return AppResponse.success_with_message(
            message=f"Added {len(documents)} documents to collection '{collection_name}'"
        )
    except Exception as error:
        logger.error(f"Error adding documents: {error}")
        return AppResponse.server_error(error=error)

@vector_bp.route("/collections/<collection_name>/query", methods=["POST"])
def query_collection(collection_name):
    """Query a collection in ChromaDB"""
    try:
        body = request.get_json()
        flask_key = body.get("key", "")
        if flask_key != Env.FLASK_PASSWORD:
            return AppResponse.bad_request(message="Forbidden", status_code=403)

        query_texts = body.get("query_texts", [])
        n_results = body.get("n_results", 5)
        where = body.get("where", None)
        where_document = body.get("where_document", None)

        if not query_texts:
            return AppResponse.bad_request(message="Query texts are required")

        client = get_client()
        if client is None:
            return AppResponse.server_error(error="ChromaDB service is not available")

        results = client.query_collection(
            collection_name,
            query_texts,
            n_results,
            where,
            where_document
        )
        return AppResponse.success_with_data(data=results)
    except Exception as error:
        logger.error(f"Error querying collection: {error}")
        return AppResponse.server_error(error=error)

@vector_bp.route("/collections/<collection_name>/personalized_query", methods=["POST"])
def personalized_query_collection(collection_name):
    """
    Query a collection in ChromaDB with personalization

    This endpoint uses a user's personalization matrix to modify the query results.
    """
    try:
        body = request.get_json()
        flask_key = body.get("key", "")
        if flask_key != Env.FLASK_PASSWORD:
            return AppResponse.bad_request(message="Forbidden", status_code=403)

        # Extract query parameters
        query_texts = body.get("query_texts", [])
        user_id = body.get("user_id")
        n_results = body.get("n_results", 5)
        where = body.get("where", None)
        where_document = body.get("where_document", None)

        # Check required parameters
        if not query_texts:
            return AppResponse.bad_request(message="Query texts are required")

        if not user_id:
            return AppResponse.bad_request(message="User ID is required for personalized query")

        # Get ChromaDB client
        client = get_client()
        if client is None:
            return AppResponse.server_error(error="ChromaDB service is not available")

        # Get collection and verify it exists
        try:
            collection = client.client.get_collection(collection_name)
        except Exception as e:
            logger.error(f"Error getting collection '{collection_name}': {e}")
            return AppResponse.not_found(message=f"Collection '{collection_name}' not found")

        # Get all embeddings from the collection
        try:
            # Import personalization modules
            from personalization.matrix_storage import get_matrix_storage
            from personalization.similarity import personalized_similarity

            # Get all items from the collection
            collection_data = collection.get(include=["embeddings", "documents", "metadatas"])
            if not collection_data["ids"]:
                return AppResponse.not_found(message=f"Collection '{collection_name}' is empty")

            # Get user matrix
            matrix_storage = get_matrix_storage()
            success, result = matrix_storage.load_user_matrix(user_id)

            if not success:
                logger.warning(f"User matrix not found for user_id={user_id}, using standard query")

                # Fall back to standard query if personalization is not available
                results = client.query_collection(
                    collection_name,
                    query_texts,
                    n_results,
                    where,
                    where_document
                )

                # Add a flag to indicate this is not personalized
                if isinstance(results, dict):
                    results["personalized"] = False

                return AppResponse.success_with_data(data=results)

            # Process collection data
            collection_embeddings = np.array(collection_data["embeddings"], dtype=np.float32)
            collection_ids = collection_data["ids"]
            collection_documents = collection_data["documents"] if "documents" in collection_data else [None] * len(collection_ids)
            collection_metadatas = collection_data["metadatas"] if "metadatas" in collection_data else [{}] * len(collection_ids)

            # Get embeddings for query_texts
            embeddings_results = []

            # For each query text, we need to get its embedding and calculate personalized similarity
            for query_text in query_texts:
                # Use Chroma to generate embedding for the query text
                query_results = collection.query(
                    query_texts=[query_text],
                    n_results=0  # We don't want results, just the embedding
                )

                if "embeddings" in query_results and query_results["embeddings"]:
                    query_embedding = np.array(query_results["embeddings"][0], dtype=np.float32).reshape(1, -1)

                    # Apply filtering if needed
                    filtered_indices = None
                    if where or where_document:
                        # Use Chroma to filter based on metadata/documents
                        filter_results = collection.query(
                            query_texts=[query_text],
                            n_results=len(collection_ids),  # Get all potential matches
                            where=where,
                            where_document=where_document
                        )

                        if filter_results["ids"]:
                            # Create a mapping from ID to index
                            id_to_index = {id: i for i, id in enumerate(collection_ids)}
                            filtered_indices = [id_to_index[id] for id in filter_results["ids"][0] if id in id_to_index]

                    # Apply personalized similarity
                    user_matrix = result  # The user matrix we got earlier

                    if filtered_indices is not None:
                        # Apply similarity only to filtered items
                        filtered_embeddings = collection_embeddings[filtered_indices]
                        similarities = personalized_similarity(query_embedding, filtered_embeddings, user_matrix).flatten()

                        # Map back to original indices
                        all_similarities = np.full(len(collection_ids), -1.0)
                        all_similarities[filtered_indices] = similarities

                        # Get top matches from filtered items
                        top_indices = filtered_indices[np.argsort(-similarities)[:n_results]]
                        top_distances = similarities[np.argsort(-similarities)[:n_results]]
                    else:
                        # Apply similarity to all items
                        similarities = personalized_similarity(query_embedding, collection_embeddings, user_matrix).flatten()

                        # Get top matches
                        top_indices = np.argsort(-similarities)[:n_results]
                        top_distances = similarities[top_indices]

                    # Extract results
                    result_ids = [collection_ids[i] for i in top_indices]
                    result_distances = top_distances.tolist()
                    result_documents = [collection_documents[i] for i in top_indices]
                    result_metadatas = [collection_metadatas[i] for i in top_indices]

                    embeddings_results.append({
                        "ids": result_ids,
                        "distances": result_distances,
                        "documents": result_documents,
                        "metadatas": result_metadatas,
                    })
                else:
                    logger.error(f"Failed to get embedding for query text: {query_text}")
                    embeddings_results.append({
                        "ids": [],
                        "distances": [],
                        "documents": [],
                        "metadatas": [],
                    })

            # Format results like ChromaDB's query response
            results = {
                "ids": [r["ids"] for r in embeddings_results],
                "distances": [r["distances"] for r in embeddings_results],
                "documents": [r["documents"] for r in embeddings_results],
                "metadatas": [r["metadatas"] for r in embeddings_results],
                "personalized": True,
                "user_id": user_id
            }

            return AppResponse.success_with_data(data=results)

        except Exception as e:
            logger.error(f"Error in personalized query: {e}")
            import traceback
            logger.error(traceback.format_exc())

            # Fall back to standard query
            logger.warning("Falling back to standard query")
            results = client.query_collection(
                collection_name,
                query_texts,
                n_results,
                where,
                where_document
            )

            if isinstance(results, dict):
                results["personalized"] = False

            return AppResponse.success_with_data(data=results)

    except Exception as error:
        logger.error(f"Error in personalized query endpoint: {error}")
        return AppResponse.server_error(error=str(error))

@vector_bp.route("/health", methods=["GET"])
def health_check():
    """Check if ChromaDB is available"""
    try:
        # Import here to avoid circular imports
        from utils.chroma_db import test_chroma_connection

        success, message = test_chroma_connection()
        if success:
            return AppResponse.success_with_message(message=message)
        else:
            logger.error(f"ChromaDB health check failed: {message}")
            return AppResponse.server_error(error=message)
    except Exception as error:
        logger.error(f"ChromaDB health check failed: {error}")
        return AppResponse.server_error(error=f"ChromaDB service is not healthy: {error}")

@vector_bp.route("/visualize", methods=["POST"])
def visualize_vectors():
    """
    Visualize job and resume embeddings in 2D space using PCA

    Optional parameters in request body:
    - highlight_job_id: ID of a job to highlight
    - highlight_resume_id: ID of a resume to highlight
    - show_labels: Whether to show labels for each point (default: false)
    - show_highlight_labels: Whether to show labels for highlighted points only (default: true)
    - user_id: User ID for personalization (required if show_personalized=true)
    - show_personalized: Whether to apply personalization matrix transformation (default: false)
    """
    try:
        body = request.get_json() or {}
        flask_key = body.get("key", "")
        if flask_key != Env.FLASK_PASSWORD:
            return AppResponse.bad_request(message="Forbidden", status_code=403)

        # Get parameters from request
        highlight_job_id = body.get("highlight_job_id")
        highlight_resume_id = body.get("highlight_resume_id")
        show_labels = body.get("show_labels", False)
        show_highlight_labels = body.get("show_highlight_labels", True)
        user_id = body.get("user_id")
        show_personalized = body.get("show_personalized", False)

        logger.info(f"Visualizing vectors with parameters: highlight_job_id={highlight_job_id}, "
                    f"highlight_resume_id={highlight_resume_id}, show_labels={show_labels}, "
                    f"show_highlight_labels={show_highlight_labels}, user_id={user_id}, "
                    f"show_personalized={show_personalized}")

        # Validate personalization parameters
        if show_personalized and not user_id:
            return AppResponse.bad_request(message="user_id is required when show_personalized=true")

        # Check ChromaDB connection first
        from utils.chroma_db import test_chroma_connection
        conn_success, conn_message = test_chroma_connection()
        if not conn_success:
            return AppResponse.server_error(error=f"ChromaDB is not available: {conn_message}")

        logger.info(f"ChromaDB connection check: {conn_message}")

        # Initialize visualizer
        visualizer = VectorVisualizer()

        # Get job embeddings
        logger.info("Retrieving job embeddings...")
        job_embeddings = visualizer.get_embeddings("job_descriptions")
        if job_embeddings[0] is None:
            return AppResponse.server_error(error="Failed to retrieve job embeddings")

        # Get resume embeddings
        logger.info("Retrieving resume embeddings...")
        resume_embeddings = visualizer.get_embeddings("user_resumes")
        if resume_embeddings[0] is None:
            return AppResponse.server_error(error="Failed to retrieve resume embeddings")

        # Create visualization
        start_time = time.time()
        logger.info("Creating visualization...")
        image_buffer = visualizer.create_visualization(
            job_embeddings,
            resume_embeddings,
            highlight_job_id,
            highlight_resume_id,
            show_labels,
            show_highlight_labels,
            user_id,
            show_personalized
        )
        logger.info(f"Visualization created in {time.time() - start_time:.2f} seconds")

        # Upload to S3
        start_time = time.time()
        logger.info("Uploading visualization to S3...")
        image_url = visualizer.upload_to_s3(image_buffer)
        logger.info(f"Uploaded to S3 in {time.time() - start_time:.2f} seconds")

        if not image_url:
            return AppResponse.server_error(error="Failed to upload visualization to S3")

        # Return the URL and additional debug info
        return AppResponse.success_with_data(data={
            "visualization_url": image_url,
            "job_count": len(job_embeddings[0]) if job_embeddings[0] else 0,
            "resume_count": len(resume_embeddings[0]) if resume_embeddings[0] else 0,
            "job_ids": job_embeddings[0][:5] if job_embeddings[0] and len(job_embeddings[0]) > 0 else [],
            "resume_ids": resume_embeddings[0][:5] if resume_embeddings[0] and len(resume_embeddings[0]) > 0 else [],
            "highlight_job_id": highlight_job_id,
            "highlight_resume_id": highlight_resume_id,
            "show_labels": show_labels,
            "show_highlight_labels": show_highlight_labels,
            "user_id": user_id,
            "show_personalized": show_personalized
        })

    except Exception as error:
        logger.error(f"Error visualizing vectors: {error}")
        import traceback
        logger.error(traceback.format_exc())
        return AppResponse.server_error(error=str(error))

@vector_bp.route("/similarity", methods=["POST"])
def calculate_similarity():
    """
    Calculate cosine similarity between a job and resume vector
    
    Request Body:
    - key: FLASK_PASSWORD
    - job_id: ID of the job (can include or exclude 'job_' prefix)
    - resume_id: ID of the resume/user (can include or exclude 'user_' prefix)
    - use_personalized: Whether to apply personalization matrix (default: false)
    - user_id: User ID for personalization (required if use_personalized=true)
    
    Returns:
        JSON response with similarity score and additional info
    """
    try:
        body = request.get_json() or {}
        flask_key = body.get("key", "")
        if flask_key != Env.FLASK_PASSWORD:
            return AppResponse.bad_request(message="Forbidden", status_code=403)

        # Get parameters
        job_id = body.get("job_id")
        resume_id = body.get("resume_id")
        use_personalized = body.get("use_personalized", False)
        user_id = body.get("user_id")

        if not job_id:
            return AppResponse.bad_request(message="job_id is required")
        if not resume_id:
            return AppResponse.bad_request(message="resume_id is required")
        if use_personalized and not user_id:
            return AppResponse.bad_request(message="user_id is required when use_personalized=true")

        # Initialize visualizer to get embeddings
        visualizer = VectorVisualizer()

        # Get job embeddings
        job_embeddings = visualizer.get_embeddings("job_descriptions")
        if job_embeddings[0] is None or len(job_embeddings[0]) == 0:
            return AppResponse.server_error(error="Failed to retrieve job embeddings")

        # Get resume embeddings
        resume_embeddings = visualizer.get_embeddings("user_resumes")
        if resume_embeddings[0] is None or len(resume_embeddings[0]) == 0:
            return AppResponse.server_error(error="Failed to retrieve resume embeddings")

        job_ids, job_vectors, job_metadatas = job_embeddings
        resume_ids, resume_vectors, resume_metadatas = resume_embeddings

        # Find job vector
        job_vector = None
        job_metadata = None
        job_index = None
        
        # Try with different ID formats
        search_job_ids = [job_id, f"job_{job_id}"]
        for search_id in search_job_ids:
            try:
                job_index = job_ids.index(search_id)
                job_vector = job_vectors[job_index]
                job_metadata = job_metadatas[job_index] if job_metadatas else None
                break
            except ValueError:
                continue
        
        if job_vector is None:
            return AppResponse.not_found(message=f"Job with ID '{job_id}' not found in embeddings")

        # Find resume vector
        resume_vector = None
        resume_metadata = None
        resume_index = None
        
        # Try with different ID formats
        search_resume_ids = [resume_id, f"user_{resume_id}"]
        for search_id in search_resume_ids:
            try:
                resume_index = resume_ids.index(search_id)
                resume_vector = resume_vectors[resume_index]
                resume_metadata = resume_metadatas[resume_index] if resume_metadatas else None
                break
            except ValueError:
                continue
        
        if resume_vector is None:
            return AppResponse.not_found(message=f"Resume with ID '{resume_id}' not found in embeddings")

        # Store original vectors for comparison
        original_job_vector = job_vector.copy()
        original_resume_vector = resume_vector.copy()

        # Apply personalization if requested
        personalized_similarity = None
        if use_personalized and user_id:
            try:
                # Apply personalization transformation
                job_vectors_copy = [job_vector]
                resume_vectors_copy = [resume_vector]
                
                transformed_job_vectors, transformed_resume_vectors = visualizer._apply_personalization(
                    job_vectors_copy, resume_vectors_copy, user_id
                )
                
                if transformed_job_vectors and transformed_resume_vectors:
                    personalized_job_vector = transformed_job_vectors[0]
                    personalized_resume_vector = transformed_resume_vectors[0]
                    
                    # Calculate personalized similarity
                    personalized_similarity = visualizer.calculate_cosine_similarity(
                        personalized_job_vector, personalized_resume_vector
                    )
                    
                    logger.info(f"Applied personalization for user {user_id}")
                else:
                    logger.warning("Personalization transformation failed, using original vectors")
            except Exception as e:
                logger.error(f"Error applying personalization: {e}")
                logger.warning("Using original vectors due to personalization error")

        # Calculate original similarity
        original_similarity = visualizer.calculate_cosine_similarity(original_job_vector, original_resume_vector)

        if original_similarity is None:
            return AppResponse.server_error(error="Failed to calculate similarity")

        # Find most similar jobs and resumes for context
        try:
            similar_jobs = visualizer.find_most_similar_vectors(
                original_resume_vector, job_vectors, job_ids, top_k=5
            )
            similar_resumes = visualizer.find_most_similar_vectors(
                original_job_vector, resume_vectors, resume_ids, top_k=5
            )
        except Exception as e:
            logger.warning(f"Failed to find similar vectors: {e}")
            similar_jobs = []
            similar_resumes = []

        # Prepare response data
        response_data = {
            "similarity": {
                "original": round(original_similarity, 4),
                "personalized": round(personalized_similarity, 4) if personalized_similarity is not None else None,
                "improvement": round(personalized_similarity - original_similarity, 4) if personalized_similarity is not None else None
            },
            "job": {
                "id": job_ids[job_index],
                "metadata": job_metadata,
                "vector_dimensions": len(original_job_vector)
            },
            "resume": {
                "id": resume_ids[resume_index], 
                "metadata": resume_metadata,
                "vector_dimensions": len(original_resume_vector)
            },
            "context": {
                "similar_jobs_to_resume": [
                    {"id": similar_jobs[i][1], "similarity": round(similar_jobs[i][2], 4)}
                    for i in range(min(3, len(similar_jobs)))
                ],
                "similar_resumes_to_job": [
                    {"id": similar_resumes[i][1], "similarity": round(similar_resumes[i][2], 4)}
                    for i in range(min(3, len(similar_resumes)))
                ]
            },
            "personalization": {
                "applied": use_personalized and personalized_similarity is not None,
                "user_id": user_id if use_personalized else None
            }
        }

        logger.info(f"Calculated similarity between job {job_id} and resume {resume_id}: {original_similarity:.4f}")
        if personalized_similarity is not None:
            logger.info(f"Personalized similarity: {personalized_similarity:.4f} (improvement: {personalized_similarity - original_similarity:+.4f})")

        return AppResponse.success_with_data(
            data=response_data,
            message=f"Similarity calculated successfully"
        )

    except Exception as error:
        logger.error(f"Error calculating similarity: {error}")
        import traceback
        logger.error(traceback.format_exc())
        return AppResponse.server_error(error=str(error))


@vector_bp.route("/batch-similarity", methods=["POST"])
def batch_calculate_similarity():
    """
    Calculate cosine similarity between multiple job-resume pairs
    
    Request Body:
    - key: FLASK_PASSWORD
    - pairs: List of {"job_id": str, "resume_id": str} objects
    - use_personalized: Whether to apply personalization matrix (default: false)
    - user_id: User ID for personalization (required if use_personalized=true)
    - limit: Maximum number of pairs to process (default: 100)
    
    Returns:
        JSON response with similarity scores for all pairs
    """
    try:
        body = request.get_json() or {}
        flask_key = body.get("key", "")
        if flask_key != Env.FLASK_PASSWORD:
            return AppResponse.bad_request(message="Forbidden", status_code=403)

        # Get parameters
        pairs = body.get("pairs", [])
        use_personalized = body.get("use_personalized", False)
        user_id = body.get("user_id")
        limit = body.get("limit", 100)

        if not pairs:
            return AppResponse.bad_request(message="pairs array is required")
        if use_personalized and not user_id:
            return AppResponse.bad_request(message="user_id is required when use_personalized=true")
        if len(pairs) > limit:
            return AppResponse.bad_request(message=f"Too many pairs. Maximum allowed: {limit}")

        # Validate pairs format
        for i, pair in enumerate(pairs):
            if not isinstance(pair, dict) or "job_id" not in pair or "resume_id" not in pair:
                return AppResponse.bad_request(message=f"Invalid pair format at index {i}. Expected {{\"job_id\": str, \"resume_id\": str}}")

        # Initialize visualizer to get embeddings
        visualizer = VectorVisualizer()

        # Get job embeddings
        job_embeddings = visualizer.get_embeddings("job_descriptions")
        if job_embeddings[0] is None or len(job_embeddings[0]) == 0:
            return AppResponse.server_error(error="Failed to retrieve job embeddings")

        # Get resume embeddings
        resume_embeddings = visualizer.get_embeddings("user_resumes")
        if resume_embeddings[0] is None or len(resume_embeddings[0]) == 0:
            return AppResponse.server_error(error="Failed to retrieve resume embeddings")

        job_ids, job_vectors, job_metadatas = job_embeddings
        resume_ids, resume_vectors, resume_metadatas = resume_embeddings

        # Apply personalization if requested
        if use_personalized and user_id:
            try:
                job_vectors, resume_vectors = visualizer._apply_personalization(
                    job_vectors, resume_vectors, user_id
                )
                logger.info(f"Applied personalization for user {user_id}")
            except Exception as e:
                logger.error(f"Error applying personalization: {e}")
                return AppResponse.server_error(error="Failed to apply personalization")

        # Process each pair
        results = []
        not_found = []
        
        for i, pair in enumerate(pairs):
            job_id = pair["job_id"]
            resume_id = pair["resume_id"]
            
            try:
                # Find job vector
                job_vector = None
                search_job_ids = [job_id, f"job_{job_id}"]
                for search_id in search_job_ids:
                    try:
                        job_index = job_ids.index(search_id)
                        job_vector = job_vectors[job_index]
                        break
                    except ValueError:
                        continue
                
                # Find resume vector
                resume_vector = None
                search_resume_ids = [resume_id, f"user_{resume_id}"]
                for search_id in search_resume_ids:
                    try:
                        resume_index = resume_ids.index(search_id)
                        resume_vector = resume_vectors[resume_index]
                        break
                    except ValueError:
                        continue
                
                if job_vector is None:
                    not_found.append({"pair_index": i, "missing": "job_id", "id": job_id})
                    continue
                    
                if resume_vector is None:
                    not_found.append({"pair_index": i, "missing": "resume_id", "id": resume_id})
                    continue
                
                # Calculate similarity
                similarity = visualizer.calculate_cosine_similarity(job_vector, resume_vector)
                
                if similarity is not None:
                    results.append({
                        "pair_index": i,
                        "job_id": job_id,
                        "resume_id": resume_id,
                        "similarity": round(similarity, 4)
                    })
                else:
                    not_found.append({"pair_index": i, "error": "Failed to calculate similarity"})
            except Exception as e:
                logger.error(f"Error processing pair {i}: {e}")
                not_found.append({"pair_index": i, "error": str(e)})

        # Prepare response
        response_data = {
            "results": results,
            "summary": {
                "total_pairs": len(pairs),
                "successful": len(results),
                "failed": len(not_found)
            },
            "personalization": {
                "applied": use_personalized,
                "user_id": user_id if use_personalized else None
            }
        }

        if not_found:
            response_data["errors"] = not_found

        logger.info(f"Batch similarity calculated for {len(results)}/{len(pairs)} pairs")
        return AppResponse.success_with_data(
            data=response_data,
            message=f"Batch similarity calculated for {len(results)}/{len(pairs)} pairs"
        )

    except Exception as error:
        logger.error(f"Error in batch similarity calculation: {error}")
        import traceback
        logger.error(traceback.format_exc())
        return AppResponse.server_error(error=str(error))