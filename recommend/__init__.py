import jwt
import numpy as np
import pandas as pd
from flask import Blueprint, request
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import MinMaxScaler
import logging
import time
from datetime import datetime

from data import (
    calc_awards,
    calc_cpa,
    calc_exp,
    calc_year,
    compare_language,
    compare_need,
)
from models.account import Account
from models.application_position import ApplicationPosition
from models.application_skill import ApplicationSkill
from models.company import Company
from models.constant import Constant
from models.match import Match
from models.user import User
from utils import get_instance
from utils.environment import Env
from utils.response import AppResponse
from utils.chroma_db import get_chroma_client, test_chroma_connection

_, db = get_instance()
recommend_bp = Blueprint("recommend", __name__, url_prefix="/api/v1/recommend")
logger = logging.getLogger(__name__)


def train_data(df: pd.DataFrame):
    df = df.dropna()
    X = df.iloc[:, :-1]
    y = df.iloc[:, -1]

    scaler = MinMaxScaler()
    scaler.fit(X.values)
    X = scaler.transform(X.values)

    model = LinearRegression()
    model.fit(X, y)

    return model, scaler


def get_position_skills(position_id: str):
    """
    Get skills for a specific application position

    Args:
        position_id: The ID of the application position

    Returns:
        List of skill dictionaries with id, skill_name, and description
    """
    try:
        skills = ApplicationSkill.query.filter(
            ApplicationSkill.application_position_id == position_id
        ).all()

        if not skills:
            return []

        result = []
        for skill in skills:
            try:
                # Safe date formatting helper function
                def safe_strftime(dt, format_str="%Y-%m-%dT%H:%M:%S"):
                    return dt.strftime(format_str) if dt is not None else None

                result.append({
                    "id": str(skill.id),
                    "skill_name": skill.skill_name,
                    "description": skill.description,
                    "created_at": safe_strftime(skill.created_at),
                    "updated_at": safe_strftime(skill.updated_at),
                })
            except Exception as e:
                logger.warning(f"Error processing skill {skill.id}: {e}")
                continue

        return result
    except Exception as e:
        logger.error(f"Error getting skills for position {position_id}: {e}")
        return []


def get_salary(account_id: str):
    try:
        applications = (
            db.session.query(ApplicationPosition, Constant)
            .filter(ApplicationPosition.account_id == account_id)  # type: ignore
            .join(Constant, ApplicationPosition.salary_range == Constant.constant_id)  # type: ignore
            .all()
        )
        if not applications:
            return []

        result = []
        for application in applications:
            try:
                position = (
                    db.session.query(ApplicationPosition, Constant)
                    .filter(ApplicationPosition.id == application[0].id)  # type: ignore
                    .join(Constant, ApplicationPosition.apply_position == Constant.constant_id)  # type: ignore
                    .first()
                )
                if not position:
                    continue

                result.append(
                    {
                        "apply_position": {
                            "constant_id": position[1].constant_id,
                            "constant_name": position[1].constant_name,
                            "constant_type": position[1].constant_type,
                        },
                        "salary_range": {
                            "constant_id": application[1].constant_id,
                            "constant_name": application[1].constant_name,
                            "constant_type": application[1].constant_type,
                        },
                    }
                )
            except Exception as e:
                logger.warning(f"Error processing application {application[0].id}: {e}")
                continue

        return result
    except Exception as e:
        logger.error(f"Error in get_salary for account {account_id}: {e}")
        # Return empty list instead of failing
        return []


def decode_jwt_token(jwt_token: str | None) -> str | None:
    if not jwt_token:
        return None

    try:
        jwt_payload = jwt.decode(
            jwt_token.split(" ")[1],
            Env.JWT_SECRET_KEY,
            algorithms=["HS256"],
            options={
                "verify_signature": False,
                "require": ["sub", "exp", "iat"],
                "verify_exp": False,  # Disable expiration check for now
                "verify_iat": "verify_signature",
            },
        )
        return jwt_payload["sub"]
    except jwt.ExpiredSignatureError:
        logger.warning("JWT token has expired")
        return None
    except jwt.InvalidTokenError as e:
        logger.error(f"Invalid JWT token: {e}")
        return None
    except Exception as e:
        logger.error(f"Error decoding JWT token: {e}")
        return None


@recommend_bp.route("/user", methods=["GET"])
def user_predict():
    try:
        start_time = time.time()
        logger.info("Starting user recommendation using vector similarity")

        # Get user account ID from JWT token
        account_id = decode_jwt_token(request.headers.get("Authorization"))
        user = User.query.filter(User.account_id == account_id).first()  # type: ignore
        if not user:
            return AppResponse.bad_request(message="Unauthorized")

        # Get account data to access email (email is in Account model, not User model)
        account = Account.query.filter(Account.account_id == account_id).first()
        if not account:
            return AppResponse.bad_request(message="Account not found")

        # Verify ChromaDB connection
        chroma_client = get_chroma_client()
        if not chroma_client:
            logger.error("ChromaDB client not available")
            return AppResponse.server_error(error="Vector database service not available")

        # Get user collection and job collection
        try:
            user_collection = chroma_client.client.get_collection("user_resumes")
            job_collection = chroma_client.client.get_collection("job_descriptions")

            logger.info(f"Successfully retrieved ChromaDB collections")
        except Exception as e:
            logger.error(f"Error retrieving ChromaDB collections: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return AppResponse.server_error(error="Vector collections not found")

        # Get user embedding - try multiple ID formats
        user_embedding = None
        tried_ids = []

        try:
            # List of possible ID formats to try
            possible_ids = [
                account_id,                           # Raw account ID
                f"user_{account_id}",                # Standard prefix format
                f"{account_id}_resume",             # Possible suffix format
                f"resume_{account_id}",             # Another possible prefix
            ]

            # Add email-based formats if email is available
            if account and account.email:
                email_username = account.email.split('@')[0]
                possible_ids.extend([
                    email_username,                   # Username part of email
                    f"user_{email_username}"         # Username with prefix
                ])

            # Try each format until we find a match
            for possible_id in possible_ids:
                tried_ids.append(possible_id)
                logger.info(f"Trying to find user embedding with ID format: {possible_id}")

                try:
                    user_embedding_result = user_collection.get(ids=[possible_id], include=["embeddings", "metadatas"])

                    if user_embedding_result and user_embedding_result.get("ids"):
                        user_embedding = user_embedding_result["embeddings"][0]
                        logger.info(f"Found user embedding with ID: {possible_id}, dimensions: {len(user_embedding)}")
                        break
                except Exception as format_error:
                    logger.warning(f"Error trying ID format {possible_id}: {format_error}")
                    continue

            # If we still don't have a embedding, try a catch-all approach
            if not user_embedding:
                # Try a partial match approach - get all IDs and look for ones containing the account_id
                try:
                    logger.info("No exact matches found, trying partial ID matching")
                    all_users = user_collection.get(include=["embeddings"])

                    if all_users and all_users.get("ids"):
                        for idx, user_id in enumerate(all_users["ids"]):
                            # Check if user ID contains the account_id
                            if account_id in user_id:
                                user_embedding = all_users["embeddings"][idx]
                                logger.info(f"Found user embedding with partial match ID: {user_id}")
                                break
                except Exception as partial_match_error:
                    logger.warning(f"Error during partial ID matching: {partial_match_error}")

            # If we still don't have a embedding, give a detailed error
            if not user_embedding:
                logger.error(f"User embedding not found after trying formats: {tried_ids}")

                # Check if there are any user embeddings at all
                try:
                    all_ids = user_collection.get(include=[])
                    total_embeddings = len(all_ids["ids"]) if "ids" in all_ids else 0

                    logger.error(f"Total user embeddings available: {total_embeddings}")
                    if total_embeddings > 0:
                        logger.error(f"Sample IDs: {all_ids['ids'][:5]}")

                    return AppResponse.bad_request(message=f"User profile not found in vector database. Tried formats: {tried_ids}")
                except Exception as count_error:
                    logger.error(f"Error getting embedding count: {count_error}")
                    return AppResponse.bad_request(message="User profile not found and couldn't enumerate available embeddings")

        except Exception as e:
            logger.error(f"Error retrieving user embedding: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return AppResponse.server_error(error=f"Error retrieving user profile data: {str(e)}")

        # If we got this far and still don't have a user embedding, return empty results
        if not user_embedding:
            logger.warning(f"No embedding found for user {account_id}, returning empty results")
            return AppResponse.success_with_meta(
                data=[],
                meta={
                    "current_page": 1,
                    "next_page": 1,
                    "previous_page": 1,
                    "total_page": 0,
                    "total_count": 0,
                    "error": "No user embedding found"
                }
            )

        # Query job descriptions to find matches
        try:
            query_results = job_collection.query(
                query_embeddings=[user_embedding],
                n_results=100  # Get more results than needed to ensure we have enough unique companies
            )

            if not query_results["ids"] or len(query_results["ids"][0]) == 0:
                logger.warning("No job matches found")
                return AppResponse.success_with_meta(
                    data=[],
                    meta={
                        "current_page": 1,
                        "next_page": 1,
                        "previous_page": 1,
                        "total_page": 0,
                        "total_count": 0
                    }
                )

            logger.info(f"Found {len(query_results['ids'][0])} potential job matches")
        except Exception as e:
            logger.error(f"Error querying job embeddings: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return AppResponse.server_error(error=f"Error finding job matches: {str(e)}")

        # Get recommendation mode - whether to group by company or show all jobs
        group_by_company = request.args.get("group_by_company", "true").lower() == "true"

        # Process results
        job_ids = query_results["ids"][0]
        job_distances = query_results["distances"][0]
        job_similarity = [1 - (dist/2) for dist in job_distances]  # Convert distance to similarity score (0-1)

        # Get job positions and company data
        suggest_companies = []
        company_ids_seen = set()
        processed_count = 0
        error_count = 0

        for i, job_id in enumerate(job_ids):
            try:
                # Extract job ID without prefix if needed
                position_id = None

                if job_id.startswith("job_"):
                    # Format: job_company-id_position-id
                    parts = job_id.split("_")
                    if len(parts) >= 3:
                        # Extract position ID from the job ID
                        position_id = parts[2]
                    else:
                        logger.warning(f"Unexpected job ID format: {job_id}")
                        # Try to extract a numeric ID as a fallback
                        import re
                        numeric_ids = re.findall(r'\d+', job_id)
                        if numeric_ids:
                            position_id = numeric_ids[-1]  # Take the last numeric part as position ID

                        if not position_id:
                            error_count += 1
                            continue
                else:
                    position_id = job_id

                # Get position from ID
                position = ApplicationPosition.query.filter(ApplicationPosition.id == position_id).first()
                if not position:
                    logger.warning(f"Position not found for ID: {position_id}")
                    error_count += 1
                    continue

                company_id = position.account_id

                # If grouping by company, skip if we've already included this company
                if group_by_company and company_id in company_ids_seen:
                    continue

                if group_by_company:
                    company_ids_seen.add(company_id)

                # Get company data with better error handling
                try:
                    company_data = db.session.query(Account, Company, Constant).filter(
                        Account.account_id == company_id,
                        Account.deleted_at == None,
                        Account.account_status == True
                    ).join(
                        Company, Account.account_id == Company.account_id
                    ).join(
                        Constant, Account.system_role == Constant.constant_id
                    ).first()

                    if not company_data:
                        logger.warning(f"Company data not found for ID: {company_id}")
                        error_count += 1
                        continue

                    account_data, company, role_constant = company_data
                    processed_count += 1

                    # Safe date formatting helper function
                    def safe_strftime(dt, format_str="%Y-%m-%dT%H:%M:%S"):
                        return dt.strftime(format_str) if dt is not None else None

                    # Get skills for this position
                    try:
                        position_skills = get_position_skills(str(position.id))
                    except Exception as skill_error:
                        logger.warning(f"Error getting skills for position {position.id}: {skill_error}")
                        position_skills = []

                    # Create company result with the specific job that matched
                    company_result = {
                        "similarity": round(job_similarity[i], 3),
                        "account_id": account_data.account_id,
                        "email": account_data.email,
                        "account_status": account_data.account_status,
                        "address": account_data.address or "",
                        "avatar": account_data.avatar,
                        "phone_number": account_data.phone_number,
                        "application_positions": [{
                            "id": position.id,
                            "apply_position_title": position.apply_position_title,
                            "salary": position.salary,
                            "description": position.description,
                            "status": position.status,
                            "skills": position_skills,
                            "created_at": safe_strftime(position.created_at),
                            "updated_at": safe_strftime(position.updated_at),
                        }],
                        "system_role": {
                            "constant_id": account_data.system_role,
                            "constant_name": role_constant.constant_name,
                            "constant_type": role_constant.constant_type,
                        },
                        "created_at": safe_strftime(account_data.created_at),
                        "updated_at": safe_strftime(account_data.updated_at),
                        "deleted_at": safe_strftime(account_data.deleted_at),
                        "company_name": company.company_name,
                        "company_url": company.company_url,
                        "established_date": safe_strftime(company.established_date),
                        "description": company.description,
                        # Add job-specific info for better matching
                        "matched_job": {
                            "job_id": job_id,
                            "position_id": position_id,
                            "similarity_rank": i + 1
                        }
                    }

                    suggest_companies.append(company_result)

                except Exception as db_error:
                    logger.error(f"Database error for company ID {company_id}: {db_error}")
                    import traceback
                    logger.error(traceback.format_exc())
                    error_count += 1
                    continue

            except Exception as item_error:
                error_count += 1
                logger.error(f"Error processing job result {job_id}: {item_error}")
                import traceback
                logger.error(traceback.format_exc())
                continue

        # Log processing summary
        logger.info(f"User recommendation processing summary: {processed_count} companies processed successfully, {error_count} errors out of {len(job_ids)} total job candidates")

        # Sort companies by similarity score (highest first)
        suggest_companies = sorted(suggest_companies, key=lambda x: x["similarity"], reverse=True)

        # Handle empty results
        if not suggest_companies:
            logger.warning("No companies to recommend after processing")
            return AppResponse.success_with_meta(
                data=[],
                meta={
                    "current_page": 1,
                    "next_page": 1,
                    "previous_page": 1,
                    "total_page": 0,
                    "total_count": 0,
                    "processing_summary": {
                        "job_candidates_found": len(job_ids),
                        "companies_processed": processed_count,
                        "errors": error_count
                    }
                }
            )

        # Apply pagination
        page = request.args.get("page", 1, type=int)
        paging = request.args.get("paging", 10, type=int)

        # Validate page and paging parameters
        if page < 1:
            page = 1
        if paging < 1:
            paging = 10
        if paging > 100:  # Limit max page size
            paging = 100

        total_count = len(suggest_companies)
        total_page = max(1, (total_count + paging - 1) // paging)  # Ceiling division

        # Handle out-of-range pages - redirect to first page to show results
        if page > total_page:
            page = 1
            logger.info(f"Requested page {request.args.get('page')} is beyond total pages {total_page}, redirecting to page {page}")

        idx_from = (page - 1) * paging
        idx_to = min(page * paging, total_count)

        paged_results = suggest_companies[idx_from:idx_to]

        # Calculate processing time
        processing_time = time.time() - start_time
        logger.info(f"Returning {len(paged_results)} companies with vector similarity (processed in {processing_time:.2f}s)")
        logger.info(f"Pagination info: page={page}, paging={paging}, total_count={total_count}, total_page={total_page}, showing indices {idx_from}-{idx_to-1}")

        return AppResponse.success_with_meta(
            data=paged_results,
            meta={
                "current_page": page,
                "next_page": min(page + 1, total_page),
                "previous_page": max(page - 1, 1),
                "total_page": total_page,
                "total_count": total_count,
                "processing_time": f"{processing_time:.2f}s",
                "processing_summary": {
                    "job_candidates_found": len(job_ids),
                    "companies_processed": processed_count,
                    "errors": error_count,
                    "group_by_company": group_by_company
                },
                "pagination_info": {
                    "page_size": paging,
                    "showing_from": idx_from + 1 if paged_results else 0,
                    "showing_to": idx_to if paged_results else 0,
                    "requested_page": int(request.args.get("page", 1))
                }
            },
        )
    except Exception as error:
        logger.error(f"Error in user recommendation: {error}")
        import traceback
        logger.error(traceback.format_exc())
        return AppResponse.server_error(error=str(error))


@recommend_bp.route("/job", methods=["GET"])
def job_predict():
    try:
        start_time = time.time()
        logger.info("Starting company-job recommendation using vector similarity")

        # Get company account ID from JWT token
        account_id = decode_jwt_token(request.headers.get("Authorization"))
        if not account_id:
            return AppResponse.bad_request(message="Unauthorized - invalid or expired token")

        company = Company.query.filter(Company.account_id == account_id).first()  # type: ignore
        if not company:
            return AppResponse.bad_request(message="Unauthorized")

        # Get job position ID from query parameters
        position_id = request.args.get("position_id")
        if not position_id:
            return AppResponse.bad_request(message="Missing position_id parameter")

        # Verify the position belongs to this company
        position = ApplicationPosition.query.filter(
            ApplicationPosition.id == position_id,
            ApplicationPosition.account_id == account_id
        ).first()

        if not position:
            return AppResponse.bad_request(message="Position not found or does not belong to this company")

        # Verify ChromaDB connection
        chroma_client = get_chroma_client()
        if not chroma_client:
            logger.error("ChromaDB client not available")
            return AppResponse.server_error(error="Vector database service not available")

        # Get job collection and user collection
        try:
            job_collection = chroma_client.client.get_collection("job_descriptions")
            user_collection = chroma_client.client.get_collection("user_resumes")
            logger.info(f"Successfully retrieved ChromaDB collections")
        except Exception as e:
            logger.error(f"Error retrieving ChromaDB collections: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return AppResponse.server_error(error="Vector collections not found")

        # Get job embedding - try multiple ID formats
        job_embedding = None
        tried_ids = []

        try:
            # List of possible ID formats to try
            possible_ids = [
                position_id,                           # Raw position ID
                f"job_{position_id}",                 # Simple prefix
                f"job_{account_id}_{position_id}",    # Full format with company ID
                f"{position_id}_job",                 # Suffix format
                f"{account_id}_{position_id}"         # Company and position without prefix
            ]

            # Try each format until we find a match
            for possible_id in possible_ids:
                tried_ids.append(possible_id)
                logger.info(f"Trying to find job embedding with ID: {possible_id}")

                try:
                    job_embedding_result = job_collection.get(ids=[possible_id], include=["embeddings", "metadatas"])

                    if job_embedding_result and job_embedding_result.get("ids"):
                        job_embedding = job_embedding_result["embeddings"][0]
                        logger.info(f"Found job embedding with ID: {possible_id}, dimensions: {len(job_embedding)}")
                        break
                except Exception as format_error:
                    logger.warning(f"Error trying ID format {possible_id}: {format_error}")
                    continue

            # If we still don't have an embedding, try a partial match approach
            if not job_embedding:
                try:
                    logger.info("No exact matches found, trying partial ID matching")

                    # Try to find by partial match on position ID
                    all_jobs = job_collection.get(include=["embeddings"])

                    if all_jobs and all_jobs.get("ids"):
                        for idx, job_id in enumerate(all_jobs["ids"]):
                            # Check if job ID contains the position_id
                            if position_id in job_id:
                                job_embedding = all_jobs["embeddings"][idx]
                                logger.info(f"Found job embedding with partial match ID: {job_id}")
                                break
                except Exception as partial_error:
                    logger.warning(f"Error during partial ID matching: {partial_error}")

            # If we still don't have an embedding, try looking up by position metadata
            if not job_embedding:
                try:
                    logger.info("Trying to find job by title metadata")

                    # Get position title from database
                    position_title = position.apply_position_title
                    if position_title:
                        # Try to query by metadata or document content
                        title_results = job_collection.query(
                            query_texts=[position_title],
                            n_results=5,
                            where={"company_id": account_id} if "company_id" in str(job_collection.get(include=["metadatas"])) else None
                        )

                        if title_results and title_results.get("embeddings") and len(title_results["embeddings"][0]) > 0:
                            # Use the most similar result
                            job_embedding = title_results["embeddings"][0][0]
                            logger.info(f"Found job embedding by title similarity search")
                except Exception as metadata_error:
                    logger.warning(f"Error trying to find by metadata: {metadata_error}")

            # If we still don't have an embedding, give a detailed error
            if not job_embedding:
                logger.error(f"Job embedding not found after trying formats: {tried_ids}")

                # Check if there are any job embeddings at all
                try:
                    all_ids = job_collection.get(include=[])
                    total_embeddings = len(all_ids["ids"]) if "ids" in all_ids else 0

                    logger.error(f"Total job embeddings available: {total_embeddings}")
                    if total_embeddings > 0:
                        logger.error(f"Sample IDs: {all_ids['ids'][:5]}")

                    return AppResponse.bad_request(
                        message=f"Job position not found in vector database. Please ensure the position has been embedded."
                    )
                except Exception as count_error:
                    logger.error(f"Error getting embedding count: {count_error}")
                    return AppResponse.bad_request(message="Job position not found and couldn't enumerate available embeddings")

        except Exception as e:
            logger.error(f"Error retrieving job embedding: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return AppResponse.server_error(error=f"Error retrieving job position data: {str(e)}")

        # If we got this far and still don't have a job embedding, return empty results
        if not job_embedding:
            logger.warning(f"No embedding found for job position {position_id}, returning empty results")
            return AppResponse.success_with_meta(
                data=[],
                meta={
                    "current_page": 1,
                    "next_page": 1,
                    "previous_page": 1,
                    "total_page": 0,
                    "total_count": 0,
                    "error": "No job embedding found"
                }
            )

        # Query user resumes to find matches
        try:
            query_results = user_collection.query(
                query_embeddings=[job_embedding],
                n_results=100  # Get more results than needed
            )

            if not query_results["ids"] or len(query_results["ids"][0]) == 0:
                logger.warning("No user matches found")
                return AppResponse.success_with_meta(
                    data=[],
                    meta={
                        "current_page": 1,
                        "next_page": 1,
                        "previous_page": 1,
                        "total_page": 0,
                        "total_count": 0
                    }
                )

            logger.info(f"Found {len(query_results['ids'][0])} potential user matches")
        except Exception as e:
            logger.error(f"Error querying user embeddings: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return AppResponse.server_error(error=f"Error finding user matches: {str(e)}")

        # Process results
        user_ids = query_results["ids"][0]
        user_distances = query_results["distances"][0]
        user_similarity = [1 - (dist/2) for dist in user_distances]  # Convert distance to similarity score (0-1)

        # Get user data
        suggest_users = []
        processed_count = 0
        error_count = 0

        for i, user_id in enumerate(user_ids):
            try:
                # Extract user ID without prefix - improved parsing logic
                account_id_from_user = None

                if user_id.startswith("user_"):
                    # Format: user_account-id
                    parts = user_id.split("_", 1)  # Split only on first underscore
                    if len(parts) >= 2:
                        account_id_from_user = parts[1]
                    else:
                        logger.warning(f"Unexpected user ID format: {user_id}")
                        error_count += 1
                        continue
                elif "_resume" in user_id:
                    # Format: account-id_resume
                    account_id_from_user = user_id.replace("_resume", "")
                elif user_id.startswith("resume_"):
                    # Format: resume_account-id
                    account_id_from_user = user_id.replace("resume_", "")
                else:
                    # Assume it's the raw account ID
                    account_id_from_user = user_id

                # Validate that we have a valid account ID
                if not account_id_from_user:
                    logger.warning(f"Could not extract account ID from user_id: {user_id}")
                    error_count += 1
                    continue

                # Get user data with better error handling
                try:
                    user_data = db.session.query(Account, User, Constant).filter(
                        Account.account_id == account_id_from_user,
                        Account.deleted_at == None,
                        Account.account_status == True
                    ).join(
                        User, Account.account_id == User.account_id
                    ).join(
                        Constant, Account.system_role == Constant.constant_id
                    ).first()

                    if not user_data:
                        logger.warning(f"User data not found for account ID: {account_id_from_user} (from user_id: {user_id})")
                        error_count += 1
                        continue

                    account, user, role_constant = user_data

                    # Get salary data with error handling
                    try:
                        application_positions = get_salary(account.account_id)
                    except Exception as salary_error:
                        logger.warning(f"Error getting salary data for user {account.account_id}: {salary_error}")
                        application_positions = []

                    # Get skills for the matched position
                    try:
                        matched_position_skills = get_position_skills(str(position.id))
                    except Exception as skill_error:
                        logger.warning(f"Error getting skills for matched position {position.id}: {skill_error}")
                        matched_position_skills = []

                    # Safe date formatting helper function
                    def safe_strftime(dt, format_str="%Y-%m-%dT%H:%M:%S"):
                        return dt.strftime(format_str) if dt is not None else None

                    # Create user result with similarity score
                    user_result = {
                        "similarity": round(user_similarity[i], 3),
                        "account_id": account.account_id,
                        "email": account.email,
                        "account_status": account.account_status,
                        "address": account.address or "",
                        "avatar": account.avatar,
                        "phone_number": account.phone_number,
                        "application_positions": application_positions,
                        "system_role": {
                            "constant_id": account.system_role,
                            "constant_name": role_constant.constant_name,
                            "constant_type": role_constant.constant_type,
                        },
                        "created_at": safe_strftime(account.created_at),
                        "updated_at": safe_strftime(account.updated_at),
                        "deleted_at": safe_strftime(account.deleted_at),
                        "first_name": user.first_name,
                        "last_name": user.last_name,
                        "gender": user.gender,
                        "date_of_birth": safe_strftime(user.date_of_birth),
                        "summary_introduction": user.summary_introduction,
                        # Include the matched position info with skills
                        "matched_position": {
                            "id": position.id,
                            "apply_position_title": position.apply_position_title,
                            "skills": matched_position_skills
                        },
                        # Add ranking info
                        "similarity_rank": i + 1
                    }

                    suggest_users.append(user_result)
                    processed_count += 1

                except Exception as db_error:
                    logger.error(f"Database error for account ID {account_id_from_user}: {db_error}")
                    import traceback
                    logger.error(traceback.format_exc())
                    error_count += 1
                    continue

            except Exception as item_error:
                error_count += 1
                logger.error(f"Error processing user result {user_id}: {item_error}")
                import traceback
                logger.error(traceback.format_exc())
                continue

        # Log processing summary with more detail
        logger.info(f"Job recommendation processing summary: {processed_count} users processed successfully, {error_count} errors out of {len(user_ids)} total candidates")

        # Handle empty results with better messaging
        if not suggest_users:
            if error_count == len(user_ids):
                logger.error("All user candidates failed processing - this indicates a systematic issue")
                return AppResponse.server_error(error="Failed to process any user candidates - please check logs")
            else:
                logger.warning("No users to recommend after processing")
                return AppResponse.success_with_meta(
                    data=[],
                    meta={
                        "current_page": 1,
                        "next_page": 1,
                        "previous_page": 1,
                        "total_page": 0,
                        "total_count": 0,
                        "processing_summary": {
                            "candidates_found": len(user_ids),
                            "successfully_processed": processed_count,
                            "errors": error_count
                        }
                    }
                )

        # Sort users by similarity score (highest first)
        suggest_users = sorted(suggest_users, key=lambda x: x["similarity"], reverse=True)

        # Apply pagination
        page = request.args.get("page", 1, type=int)
        paging = request.args.get("paging", 10, type=int)

        # Validate page and paging parameters
        if page < 1:
            page = 1
        if paging < 1:
            paging = 10
        if paging > 100:  # Limit max page size
            paging = 100

        total_count = len(suggest_users)
        total_page = max(1, (total_count + paging - 1) // paging)  # Ceiling division

        # Handle out-of-range pages - redirect to first page to show results
        if page > total_page:
            page = 1
            logger.info(f"Requested page {request.args.get('page')} is beyond total pages {total_page}, redirecting to page {page}")

        idx_from = (page - 1) * paging
        idx_to = min(page * paging, total_count)

        paged_results = suggest_users[idx_from:idx_to]

        # Calculate processing time
        processing_time = time.time() - start_time
        logger.info(f"Returning {len(paged_results)} users with vector similarity (processed in {processing_time:.2f}s)")
        logger.info(f"Pagination info: page={page}, paging={paging}, total_count={total_count}, total_page={total_page}, showing indices {idx_from}-{idx_to-1}")

        return AppResponse.success_with_meta(
            data=paged_results,
            meta={
                "current_page": page,
                "next_page": min(page + 1, total_page),
                "previous_page": max(page - 1, 1),
                "total_page": total_page,
                "total_count": total_count,
                "processing_time": f"{processing_time:.2f}s",
                "processing_summary": {
                    "candidates_found": len(user_ids),
                    "successfully_processed": processed_count,
                    "errors": error_count
                },
                "pagination_info": {
                    "page_size": paging,
                    "showing_from": idx_from + 1 if paged_results else 0,
                    "showing_to": idx_to if paged_results else 0,
                    "requested_page": int(request.args.get("page", 1))
                }
            },
        )
    except Exception as error:
        logger.error(f"Error in company-job recommendation: {error}")
        import traceback
        logger.error(traceback.format_exc())
        return AppResponse.server_error(error=str(error))


@recommend_bp.route("/vectors/status", methods=["GET"])
def vector_status():
    """Check the status of vector collections and counts"""
    try:
        # Check API key if needed
        api_key = request.args.get("key", "")
        if Env.FLASK_PASSWORD and api_key != Env.FLASK_PASSWORD:
            return AppResponse.bad_request(message="Unauthorized", status_code=403)

        # Test ChromaDB connection
        connection_success, connection_message = test_chroma_connection()
        if not connection_success:
            return AppResponse.server_error(error=f"ChromaDB connection error: {connection_message}")

        # Get client and collections
        client = get_chroma_client()
        if not client:
            return AppResponse.server_error(error="ChromaDB client not available")

        # Get collections
        try:
            collections = client.list_collections()
            collection_info = []

            for collection in collections:
                try:
                    # Get the collection directly to ensure fresh data
                    coll = client.client.get_collection(collection.name)
                    result = coll.get(include=[])
                    count = len(result["ids"]) if "ids" in result else 0

                    collection_info.append({
                        "name": collection.name,
                        "count": count,
                        "metadata": collection.metadata
                    })
                except Exception as e:
                    logger.error(f"Error getting info for collection '{collection.name}': {e}")
                    collection_info.append({
                        "name": collection.name,
                        "count": 0,
                        "metadata": collection.metadata,
                        "error": str(e)
                    })

            return AppResponse.success_with_data(
                data={
                    "connection": {
                        "status": "connected",
                        "message": connection_message
                    },
                    "collections": collection_info
                }
            )

        except Exception as e:
            logger.error(f"Error listing collections: {e}")
            return AppResponse.server_error(error=f"Error listing collections: {e}")

    except Exception as e:
        logger.error(f"Error checking vector status: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return AppResponse.server_error(error=str(e))


@recommend_bp.route("/vectors/debug", methods=["GET"])
def vector_debug():
    """Debug endpoint to see the actual IDs stored in the collections"""
    try:
        # Check API key if needed
        api_key = request.args.get("key", "")
        if Env.FLASK_PASSWORD and api_key != Env.FLASK_PASSWORD:
            return AppResponse.bad_request(message="Unauthorized", status_code=403)

        # Get client and collections
        client = get_chroma_client()
        if not client:
            return AppResponse.server_error(error="ChromaDB client not available")

        # Get collections
        try:
            collections = client.list_collections()
            collection_data = {}

            for collection in collections:
                try:
                    # Get the collection directly to ensure fresh data
                    coll = client.client.get_collection(collection.name)
                    result = coll.get()

                    collection_data[collection.name] = {
                        "ids": result.get("ids", []),
                        "metadatas": result.get("metadatas", []),
                        "count": len(result.get("ids", [])),
                    }
                except Exception as e:
                    logger.error(f"Error getting data for collection '{collection.name}': {e}")
                    collection_data[collection.name] = {
                        "error": str(e)
                    }

            return AppResponse.success_with_data(
                data=collection_data
            )

        except Exception as e:
            logger.error(f"Error listing collections: {e}")
            return AppResponse.server_error(error=f"Error listing collections: {e}")

    except Exception as e:
        logger.error(f"Error in debug endpoint: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return AppResponse.server_error(error=str(e))


@recommend_bp.route("/feedback", methods=["POST"])
def record_recommendation_feedback():
    """
    Record user feedback on recommendations to improve personalization

    Expected request body:
    {
        "user_id": "string",           # Account ID of the user providing feedback
        "item_id": "string",           # ID of job or user that received feedback
        "item_type": "job"|"user",     # Whether feedback was for a job or a user
        "feedback_type": "like"|"dislike"|"click"|"match"|"reject",  # Type of interaction
        "timestamp": "string",         # ISO timestamp (optional)
        "additional_data": {}          # Any additional feedback data (optional)
    }
    """
    try:
        body = request.get_json()
        if not body:
            return AppResponse.bad_request(message="Request body is required")

        # Get user account ID from JWT token for authentication
        jwt_account_id = decode_jwt_token(request.headers.get("Authorization"))
        if not jwt_account_id:
            return AppResponse.bad_request(message="Unauthorized", status_code=401)

        # Extract required fields
        user_id = body.get("user_id")
        item_id = body.get("item_id")
        item_type = body.get("item_type")
        feedback_type = body.get("feedback_type")

        # Validate required fields
        if not all([user_id, item_id, item_type, feedback_type]):
            missing_fields = []
            if not user_id: missing_fields.append("user_id")
            if not item_id: missing_fields.append("item_id")
            if not item_type: missing_fields.append("item_type")
            if not feedback_type: missing_fields.append("feedback_type")
            return AppResponse.bad_request(message=f"Missing required fields: {', '.join(missing_fields)}")

        # Verify the requesting user is authorized to submit this feedback
        # Only allow providing feedback for oneself or for a company position that the user manages
        is_authorized = False
        if user_id == jwt_account_id:
            is_authorized = True

        # For company users submitting feedback on behalf of their positions
        if not is_authorized and item_type == "job":
            company = Company.query.filter(Company.account_id == jwt_account_id).first()
            if company:
                position = ApplicationPosition.query.filter(
                    ApplicationPosition.id == item_id,
                    ApplicationPosition.account_id == jwt_account_id
                ).first()
                if position:
                    is_authorized = True

        if not is_authorized:
            return AppResponse.bad_request(message="Not authorized to submit feedback for this user/item", status_code=403)

        # Try to import personalization modules
        try:
            from personalization.feedback_store import store_feedback
            success = store_feedback(user_id, item_id, item_type, feedback_type, body.get("additional_data"))

            if success:
                return AppResponse.success_with_message(message="Feedback recorded successfully")
            else:
                return AppResponse.server_error(error="Failed to store feedback")
        except ImportError:
            logger.warning("Personalization feedback module not available, storing feedback in database")

            # Fall back to storing in the Match model if personalization module not available
            try:
                # Convert feedback type to match status
                status_map = {
                    "like": "LIKED",
                    "dislike": "REJECTED",
                    "match": "MATCHED",
                    "reject": "REJECTED",
                    "click": "VIEWED"
                }

                status = status_map.get(feedback_type, "VIEWED")

                # Determine user and company based on item_type
                user_account_id = None
                company_account_id = None
                position_id = None

                if item_type == "job":
                    user_account_id = user_id
                    # Get company from position
                    position = ApplicationPosition.query.filter(ApplicationPosition.id == item_id).first()
                    if position:
                        company_account_id = position.account_id
                        position_id = position.id
                else:  # item_type == "user"
                    company_account_id = user_id
                    user_account_id = item_id
                    # No position specified - this is a general feedback

                if not user_account_id or not company_account_id:
                    return AppResponse.bad_request(message="Could not determine user and company for feedback")

                # Check if match record already exists
                existing_match = Match.query.filter(
                    Match.user_id == user_account_id,
                    Match.company_id == company_account_id
                ).first()

                if existing_match:
                    # Update existing match based on feedback type
                    if item_type == "job" and feedback_type in ["like", "match"]:
                        existing_match.user_matched = True
                    elif item_type == "user" and feedback_type in ["like", "match"]:
                        existing_match.company_matched = True
                    elif feedback_type in ["dislike", "reject"]:
                        if item_type == "job":
                            existing_match.user_matched = False
                        else:
                            existing_match.company_matched = False

                    # Update matched_time if both parties have matched
                    if existing_match.user_matched and existing_match.company_matched:
                        existing_match.matched_time = datetime.now()

                    existing_match.updated_at = datetime.now()
                    db.session.commit()
                else:
                    # Create new match
                    user_matched = item_type == "job" and feedback_type in ["like", "match"]
                    company_matched = item_type == "user" and feedback_type in ["like", "match"]

                    new_match = Match(
                        user_id=user_account_id,
                        company_id=company_account_id,
                        user_matched=user_matched,
                        company_matched=company_matched
                    )
                    db.session.add(new_match)
                    db.session.commit()

                return AppResponse.success_with_message(message="Feedback stored in match records")

            except Exception as db_error:
                logger.error(f"Error storing feedback in database: {db_error}")
                import traceback
                logger.error(traceback.format_exc())
                return AppResponse.server_error(error="Failed to store feedback in database")

    except Exception as error:
        logger.error(f"Error recording feedback: {error}")
        import traceback
        logger.error(traceback.format_exc())
        return AppResponse.server_error(error=str(error))


@recommend_bp.route("/personalization/status", methods=["GET"])
def get_personalization_status():
    """
    Get the status of a user's personalization profile

    This endpoint returns information about the personalization matrix for a user,
    including whether they have a matrix, feedback summary, and other metadata.
    """
    try:
        # Get user account ID from JWT token
        account_id = decode_jwt_token(request.headers.get("Authorization"))
        if not account_id:
            return AppResponse.bad_request(message="Unauthorized", status_code=401)

        # Allow checking status of a specific user if admin user is making the request
        target_user_id = request.args.get("user_id")
        if target_user_id and target_user_id != account_id:
            # Verify requesting user has admin rights
            requester = Account.query.filter(
                Account.account_id == account_id,
                Account.deleted_at == None
            ).first()

            if not requester or requester.system_role != "ADMIN_ROLE":
                return AppResponse.bad_request(message="Not authorized to view other user's personalization data", status_code=403)

            # Use the target user ID for lookup
            user_id = target_user_id
        else:
            # Use the authenticated user's ID
            user_id = account_id

        # Try to import personalization modules
        try:
            from personalization.feedback_store import get_user_feedback_summary
            feedback_summary = get_user_feedback_summary(user_id)

            # Get vector embedding status
            vector_status = {
                "has_embedding": False,
                "embedding_id": None
            }

            # Check if user has an embedding in the vector database
            try:
                from utils.chroma_db import get_chroma_client
                client = get_chroma_client()
                if client:
                    collection = client.client.get_collection("user_resumes")

                    # Try different ID formats
                    found = False
                    for user_format in [user_id, f"user_{user_id}"]:
                        result = collection.get(ids=[user_format], include=[])
                        if result["ids"]:
                            vector_status["has_embedding"] = True
                            vector_status["embedding_id"] = user_format
                            found = True
                            break

                    if not found:
                        logger.info(f"No vector embedding found for user_id={user_id}")
            except Exception as e:
                logger.error(f"Error checking vector embedding status: {e}")

            # Combine data and return
            return AppResponse.success_with_data(data={
                "user_id": user_id,
                "personalization": feedback_summary,
                "vector": vector_status
            })

        except ImportError:
            logger.warning("Personalization modules not available")
            return AppResponse.server_error(error="Personalization service not available")

    except Exception as error:
        logger.error(f"Error retrieving personalization status: {error}")
        import traceback
        logger.error(traceback.format_exc())
        return AppResponse.server_error(error=str(error))


@recommend_bp.route("/personalization/reset", methods=["POST"])
def reset_personalization():
    """
    Reset a user's personalization matrix to the identity matrix

    This endpoint resets the personalization matrix for a user to the default identity matrix,
    effectively removing the influence of their past feedback on recommendations.
    """
    try:
        # Get user account ID from JWT token
        account_id = decode_jwt_token(request.headers.get("Authorization"))
        if not account_id:
            return AppResponse.bad_request(message="Unauthorized", status_code=401)

        body = request.get_json() or {}

        # Allow resetting a specific user if admin user is making the request
        target_user_id = body.get("user_id")
        if target_user_id and target_user_id != account_id:
            # Verify requesting user has admin rights
            requester = Account.query.filter(
                Account.account_id == account_id,
                Account.deleted_at == None
            ).first()

            if not requester or requester.system_role != "ADMIN_ROLE":
                return AppResponse.bad_request(message="Not authorized to reset other user's personalization data", status_code=403)

            # Use the target user ID
            user_id = target_user_id
        else:
            # Use the authenticated user's ID
            user_id = account_id

        # Try to import personalization modules
        try:
            from personalization.feedback import get_feedback_processor

            # Get the dimension from the vector database if not specified
            dimension = body.get("dimension")
            if not dimension:
                # Try to get from existing matrix
                from personalization.matrix_storage import get_matrix_storage
                matrix_storage = get_matrix_storage()
                metadata = matrix_storage.get_user_metadata(user_id)

                if metadata and "matrix_shape" in metadata:
                    dimension = metadata.get("matrix_shape")[0]
                else:
                    # Try to get from user embedding
                    try:
                        from utils.chroma_db import get_chroma_client
                        client = get_chroma_client()
                        if client:
                            collection = client.client.get_collection("user_resumes")

                            # Try different ID formats
                            for user_format in [user_id, f"user_{user_id}"]:
                                result = collection.get(ids=[user_format], include=["embeddings"])
                                if result["ids"] and result["embeddings"]:
                                    dimension = len(result["embeddings"][0])
                                    break
                    except Exception as e:
                        logger.error(f"Error getting embedding dimension: {e}")

            if not dimension:
                # Default dimension if all else fails
                dimension = 1536  # Common dimension for embeddings
                logger.warning(f"Using default dimension {dimension} for reset")

            # Reset the user matrix
            feedback_processor = get_feedback_processor()
            success, result = feedback_processor.reset_user_matrix(user_id, dimension)

            if success:
                return AppResponse.success_with_message(message="Personalization reset successfully")
            else:
                return AppResponse.server_error(error=result.get("error", "Unknown error"))

        except ImportError:
            logger.warning("Personalization modules not available")
            return AppResponse.server_error(error="Personalization service not available")

    except Exception as error:
        logger.error(f"Error resetting personalization: {error}")
        import traceback
        logger.error(traceback.format_exc())
        return AppResponse.server_error(error=str(error))


@recommend_bp.route("/debug/embedding", methods=["GET"])
def debug_embedding_retrieval():
    """Debug endpoint to test embedding retrieval for a specific user or job position"""
    try:
        # Check API key for security
        api_key = request.args.get("key", "")
        if Env.FLASK_PASSWORD and api_key != Env.FLASK_PASSWORD:
            return AppResponse.bad_request(message="Unauthorized", status_code=403)

        # Get the type and ID from query parameters
        embedding_type = request.args.get("type", "user")  # "user" or "job"
        entity_id = request.args.get("id")

        if not entity_id:
            return AppResponse.bad_request(message="Missing 'id' parameter")

        # Verify ChromaDB connection
        chroma_client = get_chroma_client()
        if not chroma_client:
            logger.error("ChromaDB client not available")
            return AppResponse.server_error(error="Vector database service not available")

        # Determine which collection to use based on embedding_type
        collection_name = "user_resumes" if embedding_type == "user" else "job_descriptions"

        try:
            # Get the collection
            collection = chroma_client.client.get_collection(collection_name)
            logger.info(f"Successfully retrieved '{collection_name}' collection")
        except Exception as e:
            logger.error(f"Error retrieving collection: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return AppResponse.server_error(error=f"Collection '{collection_name}' not found or not accessible")

        # Prepare to try multiple ID formats
        results = {}
        all_formats = []
        embedding_found = False

        # Define possible ID formats to try
        if embedding_type == "user":
            user_id = entity_id
            possible_formats = [
                user_id,
                f"user_{user_id}",
                f"{user_id}_resume",
                f"resume_{user_id}"
            ]
        else:  # Job
            position_id = entity_id
            # Try to get company ID if available
            company_id = request.args.get("company_id", "")
            possible_formats = [
                position_id,
                f"job_{position_id}",
                f"{position_id}_job",
            ]
            if company_id:
                possible_formats.extend([
                    f"job_{company_id}_{position_id}",
                    f"{company_id}_{position_id}"
                ])

        # Try each format
        for id_format in possible_formats:
            all_formats.append(id_format)
            try:
                result = collection.get(ids=[id_format], include=["metadatas"])

                if result and result.get("ids"):
                    embedding_found = True
                    results[id_format] = {
                        "found": True,
                        "metadata": result.get("metadatas", [{}])[0]
                    }
                else:
                    results[id_format] = {"found": False}
            except Exception as e:
                results[id_format] = {"found": False, "error": str(e)}

        # If no exact match was found, try a partial match approach
        partial_matches = []
        if not embedding_found:
            try:
                # Get all IDs in the collection
                all_items = collection.get(include=["metadatas"])

                if all_items and all_items.get("ids"):
                    # Look for IDs containing the entity_id
                    for idx, item_id in enumerate(all_items["ids"]):
                        if entity_id in item_id:
                            partial_matches.append({
                                "id": item_id,
                                "metadata": all_items.get("metadatas", [{}])[idx] if idx < len(all_items.get("metadatas", [])) else {}
                            })
            except Exception as e:
                logger.error(f"Error during partial match search: {e}")

        # Return all results with collection stats
        try:
            # Get collection stats
            collection_info = collection.get(include=[])
            total_embeddings = len(collection_info["ids"]) if "ids" in collection_info else 0
            sample_ids = collection_info["ids"][:10] if "ids" in collection_info else []
        except Exception as e:
            logger.error(f"Error getting collection stats: {e}")
            total_embeddings = -1
            sample_ids = []

        return AppResponse.success_with_data(data={
            "type": embedding_type,
            "entity_id": entity_id,
            "collection_name": collection_name,
            "tested_formats": all_formats,
            "results": results,
            "embedding_found": embedding_found,
            "partial_matches": partial_matches,
            "collection_stats": {
                "total_embeddings": total_embeddings,
                "sample_ids": sample_ids
            }
        })

    except Exception as error:
        logger.error(f"Error in debug embedding retrieval: {error}")
        import traceback
        logger.error(traceback.format_exc())
        return AppResponse.server_error(error=str(error))
